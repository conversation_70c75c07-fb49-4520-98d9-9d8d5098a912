import { RouteRecordRaw } from 'vue-router'

const routers: Array<RouteRecordRaw> = [{
  path: '/',
  component: () => import('@/components/starter/common/Menu.vue'),
  children: [
    {
      path: '/demand',
      component: () => import('@/components/starter/common/Body.vue'),
      children: [
        {
          path: '/demand/tracking',
          component: () => import('@/components/scp/demand/Tracking.vue')
        },
        {
          path: '/demand/tracking_test',
          component: () => import('@/components/scp/demand/TrackingTest.vue')
        },
        {
          path: '/demand/open_so_structure',
          component: () => import('@/components/scp/demand/OpenSOStructure.vue')
        },
        {
          path: '/demand/abnormal_demand',
          component: () => import('@/components/scp/demand/AbnormalDemand.vue')
        },
        {
          path: '/demand/waterfall',
          component: () => import('@/components/scp/demand/Waterfall.vue')
        },
        {
          path: '/demand/evolution',
          component: () => import('@/components/scp/demand/Evolution.vue')
        },
        {
          path: '/demand/event',
          component: () => import('@/components/scp/demand/EventMgmt.vue')
        },
        {
          path: '/demand/mini_siop',
          component: () => import('@/components/scp/demand/MiniSIOP.vue')
        },
        {
          path: '/demand/pipeline_sp',
          component: () => import('@/components/scp/demand/PipelineSP.vue')
        },
        {
          path: '/demand/so_transfer',
          component: () => import('@/components/scp/demand/SOTransfer.vue')
        },
        {
          path: '/demand/so_structure',
          component: () => import('@/components/scp/demand/SOStructure.vue')
        },
        {
          path: '/demand/distributor_demand',
          component: () => import('@/components/scp/demand/DistributorDemand.vue')
        },
        {
          path: '/demand/open_pms_structure',
          component: () => import('@/components/scp/demand/OpenPmsStructure.vue')
        },
        {
          path: '/demand/pms_structure',
          component: () => import('@/components/scp/demand/PmsStructure.vue')
        }
      ]
    },
    {
      path: '/master',
      component: () => import('@/components/starter/common/Body.vue'),
      children: [
        {
          path: '/master/abnormal_master_data',
          component: () => import('@/components/scp/master/AbnormalMasterData.vue')
        },
        {
          path: '/master/trigger_application_report',
          component: () => import('@/components/scp/master/TriggerApplicationReport.vue')
        },
        {
          path: '/master/summary',
          component: () => import('@/components/scp/master/MasterDataSummary.vue')
        },
        {
          path: '/master/cto_bom',
          component: () => import('@/components/scp/master/CtoBom.vue')
        },
        {
          path: '/master/eto_bom',
          component: () => import('@/components/scp/master/EtoBom.vue')
        },
        {
          path: '/master/sales_master_data_summary',
          component: () => import('@/components/scp/master/SalesMasterDataSummary.vue')
        },
        {
          path: '/master/bom_structure_p001',
          component: () => import('@/components/scp/master/BOMStructureP001.vue')
        },
        {
          path: '/master/critical_bom',
          component: () => import('@/components/scp/master/criticalBom/CriticalBomContainer.vue')
        }
      ]
    },
    {
      path: '/canvas',
      component: () => import('@/components/starter/common/Body.vue'),
      children: [
        {
          path: '/canvas/my_story',
          component: () => import('@/components/scp/canvas/MyStory.vue')
        },
        {
          path: '/canvas/five_dc_report',
          component: () => import('@/components/scp/canvas/FiveDCReport.vue')
        }
      ]
    },
    {
      path: '/inventory',
      component: () => import('@/components/starter/common/Body.vue'),
      children: [
        {
          path: '/inventory/uhs_structure',
          component: () => import('@/components/scp/inventory/UHSStructure.vue')
        },
        {
          path: '/inventory/aging',
          component: () => import('@/components/scp/inventory/Aging.vue')
        },
        {
          path: '/inventory/alert',
          component: () => import('@/components/scp/inventory/Alert.vue')
        },
        {
          path: '/inventory/structure',
          component: () => import('@/components/scp/inventory/Structure.vue')
        },
        {
          path: '/inventory/uhs',
          component: () => import('@/components/scp/inventory/UHS.vue')
        },
        {
          path: '/inventory/delivery',
          component: () => import('@/components/scp/inventory/Delivery.vue')
        },
        {
          path: '/inventory/rebalance',
          component: () => import('@/components/scp/inventory/Rebalance.vue')
        },
        {
          path: '/inventory/monthly_slice_report',
          component: () => import('@/components/scp/inventory/MonthlySliceReport.vue')
        },
        {
          path: '/inventory/summary_projection',
          component: () => import('@/components/scp/inventory/SummaryProjection.vue')
        },
        {
          path: '/inventory/excess_so_stock',
          component: () => import('@/components/scp/inventory/ExcessSOStock.vue')
        },
        {
          path: '/inventory/DistributorInventory',
          component: () => import('@/components/scp/inventory/DistributorInventory.vue')
        },
        {
          path: '/inventory/PreStock',
          component: () => import('@/components/scp/inventory/PreStock.vue')
        }
      ]
    },
    {
      path: '/toolbox',
      component: () => import('@/components/starter/common/Body.vue'),
      children: [
        {
          path: '/toolbox/md_editor',
          component: () => import('@/components/scp/toolbox/MarkdownEditor.vue')
        }, {
          path: '/toolbox/custom_report',
          component: () => import('@/components/scp/toolbox/CustomReport.vue')
        }, {
          path: '/toolbox/application_form',
          component: () => import('@/components/scp/toolbox/ApplicationForm.vue')
        }, {
          path: '/toolbox/data_mart',
          component: () => import('@/components/scp/toolbox/DataMart.vue')
        }, {
          path: '/toolbox/sql_console',
          component: () => import('@/components/scp/toolbox/SQLConsole.vue')
        }, {
          path: '/toolbox/wechat_notification',
          component: () => import('@/components/scp/toolbox/WeChatNotification.vue')
        }, {
          path: '/toolbox/wechat_auto_reply',
          component: () => import('@/components/scp/toolbox/WeChatAutoReply.vue')
        }, {
          path: '/toolbox/email_notification',
          component: () => import('@/components/scp/toolbox/EmailNotification.vue')
        }, {
          path: '/toolbox/quick_look',
          component: () => import('@/components/scp/toolbox/QuickLook.vue')
        }, {
          path: '/toolbox/csf_validation',
          component: () => import('@/components/scp/toolbox/CsfValidation.vue')
        }, {
          path: '/toolbox/demand_collection',
          component: () => import('@/components/scp/toolbox/DemandCollection.vue')
        }, {
          path: '/toolbox/broadcast_history',
          component: () => import('@/components/scp/toolbox/BroadcastHistory.vue')
        }, {
          path: '/toolbox/database_link',
          component: () => import('@/components/scp/toolbox/DatabaseLink.vue')
        }
      ]
    },
    {
      path: '/mo',
      component: () => import('@/components/starter/common/Body.vue'),
      children: [
        {
          path: '/mo/open_mo_structure',
          component: () => import('@/components/scp/mo/OpenMOStructure.vue')
        }
      ]
    },
    {
      path: '/simulation',
      component: () => import('@/components/starter/common/Body.vue'),
      children: [
        {
          path: '/simulation/best_can_do',
          component: () => import('@/components/scp/simulation/BestCanDo.vue')
        },
        {
          path: '/simulation/plant_demand_supply',
          component: () => import('@/components/scp/simulation/PlantDemandSupply.vue')
        },
        {
          path: '/simulation/best_can_do_calculator',
          component: () => import('@/components/scp/simulation/BestCanDoCalculator.vue')
        },
        {
          path: '/simulation/rmx_scheduler',
          component: () => import('@/components/scp/simulation/RMXScheduler.vue')
        }
      ]
    },
    {
      path: '/po',
      component: () => import('@/components/starter/common/Body.vue'),
      children: [
        {
          path: '/po/open_po_structure',
          component: () => import('@/components/scp/po/OpenPOStructure.vue')
        },
        {
          path: '/po/po_structure',
          component: () => import('@/components/scp/po/POStructure.vue')
        },
        {
          path: '/po/shipping_mode_tracking',
          component: () => import('@/components/scp/po/ShippingModeTracking.vue')
        }
      ]
    },
    {
      path: '/customer',
      component: () => import('@/components/starter/common/Body.vue'),
      children: [
        {
          path: '/customer/nor',
          component: () => import('@/components/scp/customer/NOR.vue')
        },
        {
          path: '/customer/nor_open',
          component: () => import('@/components/scp/customer/NOROpen.vue')
        },
        {
          path: '/customer/ac2',
          component: () => import('@/components/scp/customer/AC2.vue')
        },
        {
          path: '/customer/otdm',
          component: () => import('@/components/scp/customer/OTDM.vue')
        },
        {
          path: '/customer/otdc',
          component: () => import('@/components/scp/customer/OTDC.vue')
        },
        {
          path: '/customer/otds',
          component: () => import('@/components/scp/customer/OTDS.vue')
        },
        {
          path: '/customer/otc',
          component: () => import('@/components/scp/customer/OTC.vue')
        },
        {
          path: '/customer/bol',
          component: () => import('@/components/scp/customer/BOL.vue')
        },
        {
          path: '/customer/clo_lt_evolution',
          component: () => import('@/components/scp/customer/CloLtEvolution.vue')
        },
        {
          path: '/customer/po_nor',
          component: () => import('@/components/scp/customer/PONOR.vue')
        },
        {
          path: '/customer/open_po_nor',
          component: () => import('@/components/scp/customer/OpenPONOR.vue')
        },
        {
          path: '/customer/clo_structure',
          component: () => import('@/components/scp/customer/CloStructure.vue')
        },
        {
          path: '/customer/mycp',
          component: () => import('@/components/scp/customer/MyCP.vue')
        },
        {
          path: '/customer/rta',
          component: () => import('@/components/scp/customer/RTA.vue')
        },
        {
          path: '/customer/tex',
          component: () => import('@/components/scp/customer/TEX.vue')
        },
        {
          path: '/customer/end_to_end_delivery_tracking',
          component: () => import('@/components/scp/customer/e2eDelivery/EndToEndDeliveryContainer.vue')
        },
        {
          path: '/customer/end_to_end_so_transfer',
          component: () => import('@/components/scp/customer/e2eDelivery/EndToEndSOTransferContainer.vue')
        },
        {
          path: '/customer/e2e_lt_analysis',
          component: () => import('@/components/scp/customer/e2eLTAnalysis/E2ELT.vue')
        }
      ]
    }
  ]
}]

export default routers
