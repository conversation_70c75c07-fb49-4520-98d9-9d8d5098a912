<template>
  <div class="left-sidebar" style="width:100%;height: calc(100% - 60px)">
    <div class="widget" style="height: 100%">
      <div class="widget-body" style="height: 100%">
        <el-row class="search-box">
          <!-- date type -->
          <el-col :span="24" style="margin-bottom: var(--scp-widget-margin)">
            <el-switch
                size="large"
                v-model="pageCtl.enableDebug"
                @change="switchChange"
                active-value="Y"
                inactive-value="N"
                inline-prompt
                active-text="&nbsp;调试已打开&nbsp;"
                inactive-text="&nbsp;调试已关闭&nbsp;"
                active-color="var(--scp-bg-color-highlight)"
                inactive-color="var(--scp-bg-color-error)">
            </el-switch>
            <el-button @click="pageCtl.logs = []" style="margin-left: 0.5rem">
              Clean Console
            </el-button>
            <span class="input-tips">
                当调试页面关闭时, 系统会自动关闭所有已经打开的调试页面
              </span>
          </el-col>
        </el-row>
        <pre id="debugConsole" style="height: calc(100% - 50px);overflow: auto">{{ pageCtl.logs.join('') }}</pre>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { inject, onBeforeUnmount, onMounted, onUpdated, reactive } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'

let timer: any = {}
let client: any = {} // mqtt client
const $randomString: any = inject('$randomString')
const $createMqttClient: any = inject('$createMqttClient')

const pageCtl = reactive({
  logs: [],
  enableDebug: 'N',
  clientHeight: 600
})

const switchChange = (a) => {
  localStorage.setItem('debug', a)
}

const readState = () => {
  if (timer) {
    clearTimeout(timer)
  }
  timer = setTimeout(() => {
    pageCtl.enableDebug = localStorage.getItem('debug') + ''
    readState()
  }, 2000)
}

const connectMqtt = () => {
  const topic = 'scp/dss/ui/debug/' + (localStorage.getItem('username') || 'nobody').toLowerCase()
  client = $createMqttClient('scp-ui-debug')
  client.on('connect', e => {
    client.subscribe(topic, { qos: 2 }, (err) => {
      if (!err) {
        console.log('subscribe topic: ' + topic)
        pageCtl.logs.push('Server connected, subscribe topic: ' + topic + '\r\n')
      }
    })
  })

  client.on('message', (topic, message) => {
    if (pageCtl.logs.length > 256) {
      pageCtl.logs.shift()
    }
    pageCtl.logs.push('' + message)
  })
}

onMounted(() => {
  pageCtl.clientHeight = document.documentElement.clientHeight - 200
  connectMqtt()
  readState()
})

onUpdated(() => {
  const debugConsole = document.getElementById('debugConsole') as HTMLPreElement
  debugConsole.scrollTo({ top: debugConsole.scrollHeight, behavior: 'smooth' })
})

const closeMqttClient = () => {
  try {
    localStorage.setItem('debug', 'N')
    client.end()
  } catch (e) {

  }
}

onBeforeRouteLeave((to, from, next) => {
  closeMqttClient()
  next()
})

onBeforeUnmount(() => {
  closeMqttClient()
})

window.onbeforeunload = () => {
  closeMqttClient()
}
</script>
