<template>
  <div id="mossLogs">
    <el-button @click="addNodeAndEdge" style="position: absolute;top: 10px; right: 20px">Add Node</el-button>
    <el-row style="height: 600px;width: 800px">
      <el-col :span="6">
        <div style="overflow: auto" id="mossLogsFlowChat">
          <svg class="dagre" :width="svgWidth" :height="svgHeight">
            <g class="container"></g>
          </svg>
        </div>
      </el-col>
      <el-col :span="18">
        <el-card style="width: 100%; margin-bottom: 8px" v-for="item in pageCtl.nodes" :key="item.id">
          <template #header>
            <div class="card-header">
              <span :style="{fontWeight: item.type==='step'?'bold':'normal'}">
                {{item.type==='step'?'':'&nbsp;&nbsp;&nbsp;>> '}}{{item.name}}
              </span>
            </div>
          </template>
          <scp-md-preview v-model="item.tooltip" style="border-radius: 8px;" :noKatex="true" :auto-fold-threshold="50" />
        </el-card>
      </el-col>
    </el-row>

    <div class="tooltip" ref="tooltipRef">
      <scp-md-preview v-model="pageCtl.tooltip" style="border-radius: 8px;" :noKatex="true" :auto-fold-threshold="50" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, onUpdated, reactive, ref, watch } from 'vue'
import * as d3 from 'd3'
import * as dagreD3 from 'dagre-d3'
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'

const svgWidth = ref(190)
const svgHeight = ref(100)

const tooltipRef = ref()
const $randomString: any = inject('$randomString')
let render = {} as any
let svg = {} as any
let inner = {} as any
let g = {} as any

interface Node {
  id: string,
  name: string,
  tooltip: string,
  type: string
}

const pageCtl = reactive({
  tooltip: '',
  stepColor: '#3490dc',
  subStepColor: '#ccc',
  index: 1,
  rank: 1,
  nodes: [] as Array<Node>,
  edges: []
})

const addNodeAndEdge = () => {
  const id = $randomString(8)
  let name = `节点${id}`
  const tooltip = `这是节点${id}的描述`
  const type = Math.random() < 0.2 ? 'step' : 'sub-step'

  if (type === 'step') {
    name = (pageCtl.index++) + '. ' + name
  }

  pageCtl.nodes.push({ id, name, tooltip, type } as Node)
  svgHeight.value += 70

  if (pageCtl.nodes.length === 1) {
    drawGraph()
    return
  }

  const start = pageCtl.nodes[pageCtl.nodes.length - 2].id
  const end = id

  if (type === 'step') {
    for (let i = pageCtl.nodes.length - 2; i >= 0; i--) {
      const n = pageCtl.nodes[i]
      if (n.type === 'step') {
        pageCtl.edges.push({ start: n.id, end: id })
        break
      }
    }
  }

  pageCtl.edges.push({ start, end })
  drawGraph()
}

onMounted(() => {
  // eslint-disable-next-line new-cap
  render = new dagreD3.render()
  svg = d3.select('svg.dagre')
  inner = svg.select('g.container')
  g = new dagreD3.graphlib.Graph()
    .setGraph({ rankdir: 'TB', nodesep: 50, edgesep: 50, ranksep: 50 })
    .setDefaultEdgeLabel(() => ({}))

  svg.on('click', (event, d) => {
    let foundNode = false
    let target = event.target
    for (let i = 0; i < 6; i++) {
      if (target && target.className === 'node' && target.tagName === 'g') {
        foundNode = true
        break
      }
      target = target.parentElement
    }
    if (!foundNode) {
      tooltipRef.value.style.display = 'none'
    }
  })
})

watch(() => pageCtl.nodes, () => {
  const mossLogsFlowChat = document.getElementById('mossLogsFlowChat') as HTMLPreElement
  mossLogsFlowChat.scrollTo({ top: mossLogsFlowChat.scrollHeight, behavior: 'smooth' })
}, {
  deep: true
})

const drawGraph = () => {
  // Clear existing nodes and edges
  g.nodes().forEach((node) => g.removeNode(node))
  g.edges().forEach((edge) => g.removeEdge(edge))

  // Add nodes
  pageCtl.nodes.forEach((node) => {
    const strokeWidth = node.type === 'step' ? '2px' : '1px'
    const fontWeight = node.type === 'step' ? 'bold' : 'normal'
    const padding = node.type === 'step' ? 8 : 3
    const fillColor = node.type === 'step' ? pageCtl.stepColor : pageCtl.subStepColor
    const radius = node.type === 'step' ? 10 : 2

    g.setNode(node.id, {
      label: node.name,
      shape: 'rect',
      style: 'fill: #ffffff; stroke: ' + fillColor + '; stroke-width: ' + strokeWidth + ';',
      labelStyle: 'fill: ' + fillColor + '; font-size: 10px; font-weight: ' + fontWeight + ';',
      rx: radius,
      ry: radius,
      padding,
      rank: pageCtl.rank
    })
    if (node.type === 'step') {
      pageCtl.rank++
    }
  })

  // Add edges
  pageCtl.edges.forEach((edge) => {
    const mainRoute = _nodeMap.value[edge.start] === 'step' && _nodeMap.value[edge.end] === 'step'
    const strokeWidth = mainRoute ? '2px' : '1px'
    const fillColor = mainRoute ? pageCtl.stepColor : pageCtl.subStepColor

    g.setEdge(edge.start, edge.end, {
      style: 'stroke: ' + fillColor + '; fill: none; stroke-width: ' + strokeWidth + ';',
      arrowheadStyle: 'fill: ' + fillColor + '; stroke: ' + fillColor + ';',
      arrowhead: 'normal'
    })
  })

  // Render the graph
  inner.selectAll('*').remove()
  render(inner, g)

  // Set up tooltip
  inner.selectAll('g.node').on('click', (event, d) => {
    event.stopPropagation()
    const node = pageCtl.nodes.find((n) => n.id === d)
    if (node) {
      pageCtl.tooltip = node.tooltip
      tooltipRef.value.style.top = (event.clientY - 40) + 'px'
      tooltipRef.value.style.left = (event.clientX + 20) + 'px'
      tooltipRef.value.style.display = 'block'
    }
  })
}

const _nodeMap = computed(() => {
  const nodeMap = {}
  for (const i in pageCtl.nodes) {
    const node = pageCtl.nodes[i]
    nodeMap[node.id] = node.type
  }
  return nodeMap
})
</script>

<style lang="scss">
#mossLogs {
  position: relative;
  padding: 20px;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;

  g.node {
    cursor: pointer;
  }

  .tooltip {
    position: absolute;
    font-size: 12px;
    background-color: #ffffff;
    cursor: pointer;
    display: none;
    padding: 5px 10px 5px 10px;
    border: 1px solid #ccc;
    border-radius: 3px;
    z-index: 1000;

    div.github-theme {
      p:last-of-type {
        margin-bottom: 2px !important;
      }
    }
  }
}

#mossLogsFlowChat {
  .card-header {
    padding: 5px 12px 5px 12px !important;
  }
}
</style>
