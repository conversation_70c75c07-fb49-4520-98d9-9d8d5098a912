<template>
  <div class="left-sidebar" id="SQLTemplate">
    <div class="widget">
      <div class="widget-body">
        <el-container @contextmenu.prevent="">
          <el-aside width="350px" style="border-right: 1px solid var(--scp-border-color);margin-right:10px;"
                    :style="{height: _initHeight + 'px'}" class="left-container" v-show="pageCtl.visible.tree">
            <scp-tree-menu
                ref="treeRef"
                url="/agent/rag/sql_template/query_table_list"
                :node-click="clickNode"
                :new-click="()=>pageCtl.visible.newTemplate = true"
            ></scp-tree-menu>
          </el-aside>
          <el-main style="padding: 0" :style="{height:_initHeight+'px'}" v-if="!!pageCtl.conditions.templateID">
            <el-row>
              <el-col :span="16">
                <el-input type="text"
                          size="default"
                          style="width: var(--scp-input-width) !important;"
                          v-model="pageCtl.conditions.question"
                          placeholder="SQL Template"
                ></el-input>
              </el-col>
              <el-col :span="8">
                <el-select
                    class="inline-input"
                    v-model="_updateTemplateGroup"
                    size="default"
                    placeholder="Template Group"
                    multiple
                    collapse-tags>
                  <el-option v-for="item in pageCtl.tableOptions"
                             :key="item"
                             :label="item"
                             :value="item"/>
                </el-select>
              </el-col>
            </el-row>
            <hr style="margin: 10px 0">
            <scp-ace-editor v-model="pageCtl.conditions.answer" lang="sql" :editor-init="editorInit"
                            style="height: 300px"
                            :enableLiveAutocompletion="pageCtl.aceOptions.enableLiveAutocompletion"></scp-ace-editor>
            <el-button @click="toggleTable" style="margin-top:20px">
              <font-awesome-icon :icon="pageCtl.treeClass"/>
            </el-button>
            <el-button @click="clearSQL" style="margin-top:20px">
              <font-awesome-icon icon="eraser"/>
            </el-button>
            <el-button @click="formatSQL" style="margin-top:20px" :loading="pageCtl.loading.format">
              <font-awesome-icon icon="align-left"/>&nbsp;
              Format SQL
            </el-button>
            <el-button @click="search" style="margin-top:20px" :loading="pageCtl.loading.execute" type="primary">
              <font-awesome-icon icon="search"/>&nbsp;
              Execute SQL
            </el-button>
            <el-popconfirm title="确定保存 SQL Template?"
                           iconColor="orange"
                           @confirm="updateTemplate"
                           confirmButtonType="warning"
                           confirmButtonText='确定'
                           cancelButtonText='取消'
                           style="margin-top:20px">
              <template #reference>
                <el-button style="margin-top:20px; float: right; margin-right: 10px" type="primary">
                  <font-awesome-icon icon="save"/>&nbsp;
                  Save
                </el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm title="确定删除 SQL Template?"
                           iconColor="red"
                           @confirm="deleteTemplate"
                           confirmButtonType="danger"
                           confirmButtonText='确定'
                           cancelButtonText='取消'
                           style="margin-top:20px">
              <template #reference>
                <el-button style="margin-top:20px; float: right;" type="danger">
                  <font-awesome-icon icon="trash"/>&nbsp;
                  Delete
                </el-button>
              </template>
            </el-popconfirm>
            <div v-if="pageCtl.errorMessage.length > 0">
              <hr>
              <pre style="color: var(--scp-text-color-error)">{{ pageCtl.errorMessage }}</pre>
            </div>
            <hr>
            <scp-table
                :max-height="300"
                ref="report1Ref"
                :lazy="true"
                url="/agent/rag/sql_template/query_report1"
                download-url="/agent/rag/sql_template/download_report1"
                :params="pageCtl.conditions"
                :columns="pageCtl.columns"
                :download-specify-column="false"
                v-loading="pageCtl.loading.execute"
                :after-search="afterReport1Search"
                :editable="false"
            />
            <hr style="margin-bottom: 0; margin-top: 5px">
          </el-main>
          <el-main v-else>
            <h1 style="text-align: center;font-size: 1.6rem; color: var(--scp-text-color-lighter);height:350px;line-height: 350px">SQL Template</h1>
          </el-main>
        </el-container>
      </div>

      <scp-draggable-resizable w="1024" h="600" v-model="pageCtl.visible.newTemplate" title="New Template"
                               :save="saveNewTemplate"
                               :save-loading="pageCtl.loading.save">
        <template v-slot="{height}">
          <div style="padding: 5px">
            <div style="margin-bottom: 10px">
              <el-row>
                <el-col :span="16">
                  <el-input
                      size="default"
                      placeholder="Question"
                      v-model="pageCtl.newTemplate.question"
                      style="width: var(--scp-input-width) !important;"
                      clearable/>
                </el-col>
                <el-col :span="8">
                  <el-select
                      class="inline-input"
                      v-model="_createTemplateGroup"
                      size="default"
                      style="width: var(--scp-input-width) !important;"
                      placeholder="Template Group"
                      multiple
                      collapse-tags>
                    <el-option v-for="item in pageCtl.tableOptions"
                               :key="item"
                               :label="item"
                               :value="item"/>
                  </el-select>
                </el-col>
              </el-row>
            </div>
            <hr style="margin: 10px 0">
            <el-row>
              <el-col :span="24" style="padding-right: 15px">
                <scp-ace-editor v-model="pageCtl.newTemplate.answer" lang="sql"
                                :style="{'height': (height - 150) + 'px'}"
                                :enableLiveAutocompletion="pageCtl.aceOptions.enableLiveAutocompletion"></scp-ace-editor>
              </el-col>
            </el-row>
          </div>
        </template>
      </scp-draggable-resizable>
    </div>
  </div>
</template>

<script lang="ts" setup>
import keywords from '@/assets/js/oracle-keywords'
import { computed, inject, onBeforeMount, reactive, ref } from 'vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const $message: any = inject('$message')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')
const $axios: any = inject('$axios')
const $dateFormatter: any = inject('$dateFormatter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const report1Ref = ref()
const treeRef = ref()

const _initHeight = computed(() => {
  return document.documentElement.clientHeight - 80
})

const pageCtl = reactive({
  width: 0,
  conditions: {
    templateGroup: '',
    question: 'SQL Template',
    answer: '',
    templateID: ''
  },
  columns: [],
  errorMessage: '',
  loading: {
    execute: false,
    format: false,
    create: false,
    delete: false,
    update: false
  },
  visible: {
    tree: true,
    newTemplate: false
  },
  treeClass: 'caret-left',
  aceOptions: {
    enableLiveAutocompletion: true
  },
  aceOptions2: {
    readOnly: true
  },
  completions: [],
  newTemplate: {
    templateGroup: '',
    question: '',
    answer: ''
  },
  tableOptions: []
})

const _updateTemplateGroup = computed<string[]>({
  get () {
    try {
      const val = pageCtl.conditions.templateGroup
      if (!val) return []
      const parsed = JSON.parse(val)
      return Array.isArray(parsed) ? parsed : []
    } catch (e) {
      console.warn('Invalid JSON in templateGroup:', e)
      return []
    }
  },
  set (newValue: string[]) {
    const unique = [...new Set(newValue)]
    const sorted = unique.sort((a, b) => a.localeCompare(b))
    pageCtl.conditions.templateGroup = JSON.stringify(sorted)
  }
})

const _createTemplateGroup = computed<string[]>({
  get () {
    try {
      const val = pageCtl.newTemplate.templateGroup
      if (!val) return []
      const parsed = JSON.parse(val)
      return Array.isArray(parsed) ? parsed : []
    } catch (e) {
      console.warn('Invalid JSON in templateGroup:', e)
      return []
    }
  },
  set (newValue: string[]) {
    const unique = [...new Set(newValue)]
    const sorted = unique.sort((a, b) => a.localeCompare(b))
    pageCtl.newTemplate.templateGroup = JSON.stringify(sorted)
  }
})

const search = () => {
  pageCtl.errorMessage = ''
  pageCtl.loading.execute = true
  $axios({
    method: 'post',
    url: '/agent/rag/sql_template/query_report1_headers',
    data: pageCtl.conditions
  }).then((body) => {
    if (typeof body === 'string') {
      pageCtl.errorMessage = body
      pageCtl.loading.execute = false
      report1Ref.value.clearData()
    } else {
      const columns = [] as any
      for (let i = 0; i < body.length; i++) {
        columns.push({ data: body[i] })
      }
      pageCtl.columns = columns
      report1Ref.value.search()
    }
  }).catch((error) => {
    console.log(error)
  })
}

const deleteTemplate = () => {
  pageCtl.loading.delete = true
  $axios({
    method: 'post',
    url: '/agent/rag/sql_template/delete_template',
    data: {
      key: pageCtl.conditions.templateID
    }
  }).then(() => {
    treeRef.value.search()
    pageCtl.conditions.answer = ''
    pageCtl.conditions.question = ''
    pageCtl.conditions.templateID = ''
    pageCtl.conditions.templateGroup = ''
    $message.success('Template removed')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.delete = false
  })
}

const formatSQL = () => {
  pageCtl.loading.format = true
  $axios({
    method: 'post',
    url: '/home/<USER>/format_sql',
    data: {
      scripts: pageCtl.conditions.answer
    }
  }).then((body) => {
    if (body) {
      pageCtl.conditions.answer = body
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.format = false
  })
}

const clearSQL = () => {
  pageCtl.conditions.answer = ''
}

const afterReport1Search = () => {
  pageCtl.loading.execute = false
}

const editorInit = (editor) => {
  editor.commands.addCommand({
    name: 'formatter',
    bindKey: { win: 'Ctrl-Shift-F', mac: 'Command-Shift-F' },
    exec: () => formatSQL()
  })
  // 加入自定义语法提示
  editor.completers = [{
    getCompletions: (editor, session, pos, prefix, callback) => {
      setCompletions(editor, session, pos, prefix, callback)
    }
  }]
}

const saveNewTemplate = () => {
  if (!pageCtl.newTemplate.templateGroup || !pageCtl.newTemplate.question || !pageCtl.newTemplate.answer) {
    $message.error('请填写问题和答案')
    return
  }
  if (pageCtl.newTemplate.question) {
    pageCtl.loading.create = true
    $axios({
      method: 'post',
      url: '/agent/rag/sql_template/save_new_template',
      data: pageCtl.newTemplate
    }).then(() => {
      treeRef.value.search()
      pageCtl.visible.newTemplate = false
      $message.success('Template Saved.')
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.create = false
    })
  } else {
    $message.error('Invalid subject')
  }
}
const updateTemplate = () => {
  if (!pageCtl.conditions.templateGroup || !pageCtl.conditions.question || !pageCtl.conditions.answer || !pageCtl.conditions.templateID) {
    $message.error('请填写问题和答案')
    return
  }
  pageCtl.loading.update = true
  $axios({
    method: 'post',
    url: '/agent/rag/sql_template/update_template',
    data: pageCtl.conditions
  }).then(() => {
    treeRef.value.search()
    $message.success('Template updated.')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.update = false
  })
}

const setCompletions = (editor, session, pos, prefix, callback) => {
  // 这里只是举例子，具体实现要看自己需求
  if (prefix.length === 0) {
    return callback(null, [])
  } else {
    return callback(null, pageCtl.completions)
  }
}

const toggleTable = () => {
  pageCtl.visible.tree = !pageCtl.visible.tree
  if (pageCtl.visible.tree) {
    pageCtl.treeClass = 'caret-left'
  } else {
    pageCtl.treeClass = 'caret-right'
  }
}

const clickNode = (e) => {
  $axios({
    method: 'post',
    url: '/agent/rag/sql_template/query_template_sql',
    data: {
      templateID: e.key
    }
  }).then((body) => {
    pageCtl.conditions.templateID = body.TEMPLATE_ID
    pageCtl.conditions.answer = body.ANSWER
    pageCtl.conditions.question = body.QUESTION
    pageCtl.conditions.templateGroup = body.TEMPLATE_GROUP
  }).catch((error) => {
    console.log(error)
  })
}

onBeforeMount(() => {
  pageCtl.width = document.documentElement.clientWidth - 50
  $axios({
    method: 'post',
    url: '/agent/rag/sql_template/query_table_cols'
  }).then((body) => {
    const tables = body.tables
    const cols = body.cols
    const completions = [] as any

    for (const i in tables) {
      completions.push({ caption: tables[i], meta: 'table', value: 'SCPA.' + tables[i] })
    }

    for (const i in cols) {
      completions.push({ caption: cols[i], meta: 'column', value: cols[i] })
    }

    const keys = Object.keys(keywords)
    for (const index in keys) {
      const key = keys[index]
      const list = keywords[key]
      for (const i in list) {
        const value = list[i]
        completions.push({ caption: value, meta: key, value })
      }
    }
    completions.sort((e1, e2) => e1.caption.localeCompare(e2.caption))
    pageCtl.completions = completions
    pageCtl.tableOptions = body.tableOptions
  }).catch((error) => {
    console.log(error)
  })
})

</script>

<style lang="scss">

#SQLTemplate {
  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-fill-lighter) !important;
  }

  .el-select__placeholder {
    font-size: 0.5rem;
  }

  .custom-tree-node {
    display: flex;
    align-items: center;
    .el-tooltip__trigger {
      max-width: 300px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}
</style>
