<template>
  <div class="left-sidebar" id="mossQuickLook" :style="{height: pageCtl.layout.pageHeight + 'px'}">
    <div :style="{width : '100%', position: 'relative'}">
      <div :class="['moss-quicklook-header-common', pageCtl.searched ? 'moss-quicklook-header-searched' : 'moss-quicklook-header']">
        <img src="/img/moss.svg" alt="Quick Access" width="30%" style="max-width: 318px;min-width: 218px;"/>
        <div class="moss-autocomplete">
          <el-autocomplete
            v-model="pageCtl.keywords"
            :maxlength="100"
            size="large"
            :fetch-suggestions="queryKeywords"
            placeholder="Table Name / Column Name / View Name"
            show-word-limit
            clearable
            @select="search">
            <template #append>
              <el-button-group>
                <el-tooltip :show-after="1000" effect="light" placement="bottom" content="Copy Share Link">
                  <el-button style="width: 2rem" @click="shareLink" :disabled="pageCtl.currentElement.value===''">
                    <font-awesome-icon icon="share-alt" />
                  </el-button>
                </el-tooltip>
                <el-tooltip :show-after="1000" effect="light" placement="bottom" content="Refresh">
                  <el-button style="width: 2rem" @click="refresh" :disabled="pageCtl.currentElement.value===''">
                    <font-awesome-icon icon="refresh" />
                  </el-button>
                </el-tooltip>
              </el-button-group>
            </template>
            <template v-slot="{ item }">
              <el-row>
                <el-col :span="20">
                  <div v-html="hightLight(item.value)"></div>
                </el-col>
                <el-col :span="4" style="text-align: right;color: var(--scp-text-color-secondary)">
                  {{ item.label }}
                </el-col>
              </el-row>
            </template>
          </el-autocomplete>
        </div>
      </div>
      <div style="margin:auto;width: 62rem;min-height: 10rem;" v-loading="pageCtl.loading.search">
        <scp-md-preview v-model="pageCtl.result" />
      </div>
    </div>
    <scp-draggable-resizable w="60vw" h="450px" v-model="pageCtl.visible.histComments" title="History Comments">
      <template v-slot="{ height }">
        <div style="padding-left: 8px;">
          <scp-md-preview :style="{ height : height - 100 + 'px'}" v-model="pageCtl.histComments" />
        </div>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable h="280" w="900" v-model="pageCtl.visible.createColumnComments"
                             :title="'编辑字段 => ' + pageCtl.newComments.objectName" :save="saveComments"
                             :save-loading="pageCtl.loading.save">
      <div class="create-comment-div">
        <el-row>
          <el-col :span="2" class="title">业务含义</el-col>
          <el-col :span="21" class="content">
            <el-input style="width: 100% !important;" v-model="pageCtl.newComments.businessMeaning" placeholder="请输入这个字段的业务含义" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" class="title">值示例</el-col>
          <el-col :span="21" class="content">
            <el-select v-model="pageCtl.newComments.valueExample" :placeholder="pageCtl.loading.init?'Loading...':'请选择5个左右不重复的值示例'" style="width: 100%"
                       clearable filterable multiple multiple-limit="20" allow-create collapse-tags>
              <el-option
                v-for="item in pageCtl.columnValueOpts"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" class="title">别名</el-col>
          <el-col :span="21" class="content">
            <el-input style="width: 100% !important;" v-model="pageCtl.newComments.alternateTerm" placeholder="请输入这个字段在业务中的一些其他称呼" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="2" class="title">备注</el-col>
          <el-col :span="21" class="content">
            <el-input style="width: 100% !important;" v-model="pageCtl.newComments.comments" placeholder="请输入这个字段的具体逻辑解释" />
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>

    <scp-draggable-resizable h="600" w="1024" v-model="pageCtl.visible.createTableComments"
                             :title="'编辑数据表备注 => ' + pageCtl.newComments.objectName" :save="saveComments"
                             :save-loading="pageCtl.loading.save"
                             delete-confirm-text="确定导入表格Comments模版? 此操作会覆盖未保存的内容"
                             delete-text="使用表格Comments模版" delete-btn-type="default"
                             :delete="importCommentsTemplate">
      <template v-slot="{height}">

        <div style="padding: 8px">
          <scp-md-editor v-model="pageCtl.newComments.comments" :style="{'height': (height - 115) + 'px'}" />
        </div>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import ScpMdEditor from '@/components/starter/components/MdEditor.vue'

const $store = useStore()
const $route = useRoute()
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $copyText: any = inject('$copyText')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $dateFormatter: any = inject('$dateFormatter')

const pageCtl = reactive({
  query: '',
  result: '',
  histComments: '',
  currentElement: {
    value: '',
    label: ''
  },
  allAvalibleTables: [],
  allAvalibleColumns: [],
  columnValueOpts: [],
  searched: false,
  comments: [] as Array<any>,
  visible: {
    createColumnComments: false,
    createTableComments: false,
    histComments: false
  },
  loading: {
    init: false,
    save: false,
    search: false
  },
  newComments: {
    objectName: '',
    objectType: '',
    comments: '',
    businessMeaning: '',
    valueExample: [],
    alternateTerm: ''
  },
  keywords: '',
  splitedWords: [],
  timeout: null as any,
  layout: {
    pageHeight: 0
  }
})

const _allObjectOpts = computed(() => {
  const result = []
  for (let i = 0; i < pageCtl.allAvalibleTables.length; i++) {
    const e = pageCtl.allAvalibleTables[i] as any
    result.push({
      value: e.OBJ_NAME,
      label: e.OBJ_TYPE
    })
  }
  return result
})

const saveComments = () => {
  if (pageCtl.newComments.objectType === 'COLUMN') {
    if (!pageCtl.newComments.businessMeaning) {
      $message.error('请输入业务含义.')
      return
    }
    if (!pageCtl.newComments.valueExample) {
      $message.error('请输入值示例.')
      return
    }
    if (!pageCtl.newComments.alternateTerm) {
      $message.error('请输入别名.')
      return
    }
    if (!pageCtl.newComments.comments) {
      $message.error('请输入备注.')
      return
    }
  } else if (pageCtl.newComments.objectType === 'TABLE') {
    if (!pageCtl.newComments.comments) {
      $message.error('请输入备注.')
      return
    }
  } else {
    $message.error('未知对象类型: ' + pageCtl.newComments.objectType)
    return
  }

  pageCtl.loading.save = true
  $axios({
    method: 'post',
    url: '/agent/rag/moss_quick_look/save_comments',
    data: pageCtl.newComments
  }).then(() => {
    $message.success('Comments Saved.')
    pageCtl.visible.createTableComments = false
    pageCtl.visible.createColumnComments = false
    pageCtl.visible.histComments = false
    refresh()
  }).catch((error) => {
    console.error('Save failed:', error)
    $message.error('Save failed. Please try again.')
  }).finally(() => {
    pageCtl.loading.save = false
  })
}

const search = (item) => {
  pageCtl.searched = true
  if (item && item.label) {
    pageCtl.currentElement.value = item.value
    pageCtl.currentElement.label = item.label
    refresh()
  }
}

const refresh = () => {
  pageCtl.loading.search = true
  $axios({
    method: 'post',
    url: '/agent/rag/moss_quick_look/search',
    data: {
      type: pageCtl.currentElement.label,
      name: pageCtl.currentElement.value
    }
  }).then((body) => {
    pageCtl.result = body || ''
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.search = false
  })
}

const queryHistComments = (obj: HTMLLinkElement) => {
  const objectType = obj.getAttribute('object-type')
  const objectName = obj.getAttribute('object-name')
  $axios({
    method: 'post',
    url: '/agent/rag/moss_quick_look/query_hist_comments',
    data: {
      objectType,
      objectName
    }
  }).then((body) => {
    pageCtl.histComments = body || ''
    pageCtl.visible.histComments = true
  }).catch((error) => {
    console.log(error)
  })
}
window.mossQuickLookQueryHistComments = queryHistComments

const editObject = (obj: HTMLLinkElement) => {
  const objectType = obj.getAttribute('object-type')
  const objectName = obj.getAttribute('object-name')
  pageCtl.columnValueOpts = []
  $axios({
    method: 'post',
    url: '/agent/rag/moss_quick_look/query_object_info',
    data: {
      objectName,
      objectType
    }
  }).then((body) => {
    pageCtl.newComments.objectName = body.OBJECT_NAME
    pageCtl.newComments.objectType = body.OBJECT_TYPE
    pageCtl.newComments.comments = body.COMMENTS
    pageCtl.newComments.valueExample = body.VALUE_EXAMPLE
    pageCtl.newComments.alternateTerm = body.ALTERNATE_TERM
    pageCtl.newComments.businessMeaning = body.BUSINESS_MEANING
    if (body.OBJECT_TYPE === 'COLUMN') {
      pageCtl.visible.histComments = false
      pageCtl.visible.createTableComments = false
      pageCtl.visible.createColumnComments = true

      pageCtl.loading.init = true
      $axios({
        method: 'post',
        url: '/agent/rag/moss_quick_look/query_column_value_sample',
        data: {
          objectName
        }
      }).then((body2) => {
        pageCtl.columnValueOpts = body2
      }).catch((error) => {
        console.log(error)
      }).finally(() => {
        pageCtl.loading.init = false
      })
    } else if (body.OBJECT_TYPE === 'TABLE') {
      pageCtl.visible.histComments = false
      pageCtl.visible.createColumnComments = false
      pageCtl.visible.createTableComments = true
    }
  }).catch((error) => {
    console.log(error)
  })
}
window.mossQuickLookEditObject = editObject

const queryKeywords = (keywords, cb) => {
  const results = [] as any
  if (keywords === '') {
    cb(_allObjectOpts.value)
  } else {
    clearTimeout(pageCtl.timeout)
    pageCtl.timeout = setTimeout(() => {
      $axios({
        method: 'post',
        url: '/agent/rag/moss_quick_look/query_keywords',
        data: {
          keywords
        }
      }).then((body) => {
        pageCtl.splitedWords = body.words
        body.result.forEach((item) => {
          results.push({
            value: item.VAL,
            label: item.LABEL
          })
        })
        cb(results)
      }).catch((error) => {
        console.log(error)
      })
    }, 650)
  }
}

const shareLink = () => {
  let url = window.location.href
  url = url.split('?')[0]
  url = url + '?s=' + pageCtl.currentElement.value
  url = url + '&t=' + pageCtl.currentElement.label
  $copyText(url)
  $message.success('Share link copied!')
}

const hightLight = (val) => {
  if (val) {
    const position = {}
    // 先将所有的文本用占位符替换
    for (const i in pageCtl.splitedWords) {
      const word = pageCtl.splitedWords[i]
      val = val.replace(word, '{' + i + '}')
      position['{' + i + '}'] = word
    }

    // 再用高亮替换占位符
    let i = 0
    for (const k in position) {
      val = val.replace('{' + (i++) + '}', '<span style="color: var(--scp-text-color-highlight)">' + position[k] + '</span>')
    }
    return val
  }
}

const importCommentsTemplate = () => {
  $axios({
    method: 'post',
    url: '/agent/rag/moss_quick_look/query_comments_template'
  }).then((body) => {
    if (body) {
      pageCtl.newComments.comments = body
    } else {
      $message.error('未找到Comments模版, 请联系管理员!')
    }
  }).catch((error) => {
    console.log(error)
  })
}

onMounted(() => {
  pageCtl.layout.pageHeight = document.documentElement.clientHeight - 60
  window.onresize = () => {
    pageCtl.layout.pageHeight = document.documentElement.clientHeight - 60
  }
  // find query string
  const searchStr = $route.query.s as string
  const searchType = $route.query.t as string
  if (searchStr) {
    pageCtl.keywords = searchStr
    search({
      label: searchType,
      value: searchStr
    })
  }

  $axios({
    method: 'post',
    url: '/agent/rag/moss_quick_look/init_page'
  }).then((body) => {
    pageCtl.allAvalibleTables = body.allAvalibleTables
    pageCtl.allAvalibleColumns = body.allAvalibleColumns
  }).catch((error) => {
    console.log(error)
  })
})
</script>
<style lang="scss">
#mossQuickLook {
  .moss-quicklook-header-common {
    // 调整 autocomplete 大小和颜色
    .moss-autocomplete {
      width: 100%;
      height: 1.85rem;
      border-radius: 0.4rem;
      border: 0.03rem solid var(--scp-border-color);

      .el-input__wrapper {
        height: 1.75rem;
        box-shadow: none;
        border-top-left-radius: 0.4rem;
        border-bottom-left-radius: 0.4rem;
      }

      .el-input__inner {
        height: auto;
        box-shadow: none;
      }

      .el-input-group__append {
        height: auto;
        box-shadow: none;
        border-top-right-radius: 0.4rem;
        border-bottom-right-radius: 0.4rem;
      }
    }

    margin: 0 auto;
    width: 80%;
    display: flex;
    padding-top: 10px;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
  }

  .moss-quicklook-header {
    flex-direction: column;

    img {
      height: 6rem;
      padding: 4rem 0 1rem;
      transition: height 0.5s ease
    }

  }

  .moss-quicklook-header-searched {
    position: sticky;
    top: 0;
    z-index: 1000;
    padding-bottom: 5px;
    background-color: white;
    flex-direction: row;
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;

    img {
      height: 2rem;
      padding: 0;
      transition: height 0.5s ease
    }
  }

  .md-editor-preview-wrapper {
    padding: var(--scp-widget-margin) 0 !important;
  }

  .hover-icon:hover {
    color: var(--scp-text-color-error);
  }

  .comment {
    border-radius: 6px;
    border: 1px solid var(--scp-text-color-lighter);
    margin-bottom: 0.4rem;
  }

  table {
    tr {
      td {
        padding: 6px 10px;
        font-size: 10px;
        max-width: 20rem;

        .moss-quicklook-link {
          color: inherit !important;
        }

        figure {
          width: auto !important;
        }

        p {
          padding: 0 !important;
          margin: 0 !important;
        }
      }

      th {
        padding: 6px 10px;
        font-size: 10px;
      }
    }
  }

  blockquote {
    margin: 0 0.5rem !important;
  }

  @media print {
    .moss-quicklook-header {
      display: none;
    }
  }

  .create-comment-div {
    padding-top: 15px;
    padding-left: 10px;

    .title {
      text-align: right;
      padding-right: 8px;
      font-weight: bold;
      line-height: 2;
    }

    .content {
      padding-right: 10px;
    }

    .el-row {
      margin-bottom: 12px;
    }
  }
}
</style>
