<template>
  <div v-loading="pageCtl.loading" style="width:100%;" v-if="pageCtl.xAxisOpts.length > 0 && pageCtl.yAxisOpts.length > 0">
    <el-row class="search-box">
      <el-col :span="4">
        <el-select v-model="pageCtl.chartType" size="small" placeholder="Chart Type" clearable @change="initAxis">
            <el-option
                    v-for="item in ['BAR', 'LINE', 'PIE', 'TREEMAP']"
                    :key="item"
                    :label="item"
                    :value="item">
            </el-option>
        </el-select>
      </el-col>
      <el-col :span="5">
        <el-select v-model="pageCtl.conditions.xAxis" size="small" placeholder="xAxis" collapse-tags collapse-tags-tooltip clearable
                   :multiple="pageCtl.xMulti" :multiple-limit="3">
            <el-option
                    v-for="item in pageCtl.xAxisOpts"
                    :key="item"
                    :label="item"
                    :value="item">
            </el-option>
        </el-select>
      </el-col>
      <el-col :span="6">
        <el-select v-model="pageCtl.conditions.yAxis" size="small" placeholder="yAxis" collapse-tags collapse-tags-tooltip clearable
                  :multiple="pageCtl.yMulti">
            <el-option
                    v-for="item in pageCtl.yAxisOpts"
                    :key="item"
                    :label="item"
                    :value="item">
            </el-option>
        </el-select>
      </el-col>
      <el-col :span="1">
        <el-button @click="search">
          <font-awesome-icon icon="search"/>
        </el-button>
      </el-col>
    </el-row>
    <chart :height="320" :option="pageCtl.options"/>
  </div>
  <div v-else>
    <h1 style="color: var(--scp-text-color-lighter);text-align: center">No Available Chart</h1>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive, watch } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const $axios: any = inject('$axios')
const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const $toFixed: any = inject('$toFixed')
const $shortenNumber: any = inject('$shortenNumber')

const pageCtl = reactive({
  loading: false,
  conditions: {
    xAxis: [],
    yAxis: []
  },
  xAxisOpts: ['-'],
  yAxisOpts: ['-'],
  chartType: '',
  xMulti: false,
  yMulti: true,
  data: {},
  options: {}
})

const initAxis = () => {
  switch (pageCtl.chartType) {
    case 'BAR':
    case 'LINE':
      pageCtl.xMulti = false
      pageCtl.yMulti = true
      break
    case 'PIE':
      pageCtl.xMulti = false
      pageCtl.yMulti = false
      break
    case 'TREEMAP':
      pageCtl.xMulti = true
      pageCtl.yMulti = false
  }

  if (pageCtl.xAxisOpts.indexOf(props.settings.xAxis) !== -1) {
    pageCtl.conditions.xAxis = pageCtl.xMulti ? [props.settings.xAxis] : props.settings.xAxis
  } else {
    pageCtl.conditions.xAxis = pageCtl.xMulti ? [pageCtl.xAxisOpts[0]] : pageCtl.xAxisOpts[0]
  }

  if (pageCtl.yAxisOpts.indexOf(props.settings.yAxis) !== -1) {
    pageCtl.conditions.yAxis = pageCtl.yMulti ? [props.settings.yAxis] : props.settings.yAxis
  } else {
    pageCtl.conditions.yAxis = pageCtl.yMulti ? [pageCtl.yAxisOpts[0]] : pageCtl.yAxisOpts[0]
  }
}

// @ts-ignore
const props = withDefaults(defineProps<{
  sql: string,
  settings: any
}>(), {
  sql: '',
  settings: () => {
    return {}
  }
})

const parseOptions = () => {
  let opts = {}

  if (pageCtl.chartType === 'BAR' || pageCtl.chartType === 'LINE') {
    const series = []
    const legend = []
    for (let i = 0; i < pageCtl.conditions.yAxis.length; i++) {
      if (pageCtl.data['yAxis' + i]) {
        legend.push(pageCtl.conditions.yAxis[i])
        series.push({
          stack: 'total',
          name: pageCtl.conditions.yAxis[i],
          type: pageCtl.chartType.toLowerCase(),
          data: pageCtl.data['yAxis' + i]
        })
      }
    }
    opts = {
      toolbox: $toolbox({ opts: ['line', 'no-details'] }),
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const tip = [] as Array<string>
          for (let i = 0; i < params.length; i++) {
            tip.push('<div style="width:9.5rem;">')
            tip.push(params[i].marker)
            tip.push(params[i].seriesName)
            tip.push('<span style="float: right">')
            tip.push($shortenNumber(params[i].data, 1) || 0)
            tip.push('</span>')
            tip.push('</div>')
          }
          return tip.join('')
        }
      },
      grid: $grid({ left: 25, right: 5 }),
      legend: $legend({ data: legend }),
      xAxis: {
        type: 'category',
        data: pageCtl.data.xAxis
      },
      yAxis: {
        type: 'value',
        scale: true,
        axisLine: {
          lineStyle: {
            color: '#999' // y 轴线条颜色
          }
        },
        axisLabel: {
          formatter: (value) => {
            return $shortenNumber(value, 0)
          }
        },
        splitLine: {
          lineStyle: {
            color: '#eee' // y 轴分割线颜色
          }
        }
      },
      series
    }
  } else if (pageCtl.chartType === 'TREEMAP') {
    opts = {
      toolbox: $toolbox({ opts: [] }),
      tooltip: {
        trigger: 'item',
        triggerOn: 'mousemove',
        formatter: (params) => {
          const treePathInfo = params.treePathInfo || []
          let parentNodeValue = 0
          if (treePathInfo.length > 1) {
            parentNodeValue = treePathInfo[treePathInfo.length - 2].value
          }

          console.log(params)
          const marker = params.marker
          const tip: any = []
          tip.push('<div style="width: 7.5rem;">')
          tip.push('<div>')
          tip.push(marker)
          tip.push(params.name)
          tip.push('<span style="float: right">')
          if (parentNodeValue > 0) {
            tip.push($shortenNumber(params.value) + ' (' + $toFixed(params.value / parentNodeValue * 100, 1) + '%)')
          } else {
            tip.push($shortenNumber(params.value))
          }
          tip.push('</span>')
          tip.push('</div>')
          return tip.join('')
        }
      },
      series: [{
        name: 'Total',
        type: 'treemap',
        silent: false,
        roam: false,
        leafDepth: 1,
        ...$grid({ type: 'treemap' }),
        data: pageCtl.data
      }]
    }
  } else if (pageCtl.chartType === 'PIE') {
    const pieData = []
    for (let i = 0; i < pageCtl.data.xAxis.length; i++) {
      pieData.push({
        name: pageCtl.data.xAxis[i],
        value: pageCtl.data.yAxis0[i]
      })
    }
    opts = {
      tooltip: {
        trigger: 'item',
        triggerOn: 'mousemove',
        formatter: (params) => {
          const marker = params.marker
          const tip = [] as any

          tip.push('<div style="width:7.5rem;">')
          tip.push('<div>')
          tip.push(marker)
          tip.push(params.name)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(params.value))
          tip.push('(')
          tip.push(params.percent)
          tip.push('%)')
          tip.push('</span>')
          tip.push('</div>')

          return tip.join('')
        }
      },
      legend: $legend(),
      series: [
        {
          type: 'pie',
          radius: '50%',
          data: pieData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
  }
  return opts
}

watch(() => props.sql, () => {
  initPage()
})

onMounted(() => {
  initPage()
})

const initPage = () => {
  // 初始化表格类型
  if (pageCtl.chartType === '') {
    const propsType = (props.settings.type || '').toUpperCase()
    switch (propsType) {
      case ('BAR'):
      case ('LINE'):
      case ('PIE'):
        pageCtl.chartType = propsType
    }

    if (pageCtl.chartType === '') {
      pageCtl.chartType = 'BAR'
    }
  }

  pageCtl.loading = true
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/init_chart',
    data: {
      sql: props.sql
    }
  }).then((body) => {
    pageCtl.xAxisOpts = body.xAxisOpts
    pageCtl.yAxisOpts = body.yAxisOpts
    initAxis()
    search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading = false
  })
}

const search = () => {
  if (pageCtl.conditions.xAxis.length === 0) {
    pageCtl.conditions.xAxis = pageCtl.xAxisOpts[0]
  }
  if (pageCtl.conditions.yAxis.length === 0) {
    pageCtl.conditions.yAxis = [pageCtl.yAxisOpts[0]]
  }
  if (pageCtl.chartType.length === 0) {
    pageCtl.chartType = 'BAR'
  }
  pageCtl.loading = true
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/query_chart_by_sql',
    data: {
      sql: props.sql,
      type: pageCtl.chartType,
      ...pageCtl.conditions
    }
  }).then((body) => {
    pageCtl.data = body
    pageCtl.options = parseOptions()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading = false
  })
}
</script>

<script lang="ts">
export default {
  name: 'MossVisualization'
}
</script>
