<template>
  <div id="MOSSChat" :style="{height: height + 'px'}">
    <div class="chat-title">
      <!-- region: 右边栏, 打开关闭侧边栏 -->
      <span style="cursor: pointer" @click="clickShowSidebar(true)" :style="{color:isSidebarBtnShown? 'transparent':'inherit'}">
          <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" data-v-f2dda92e="" style="" width="20"
               height="20" viewBox="0 0 1024 1024" class="iconify"><path
            d="M861.866667 162.133333c-17.066667-17.066667-42.666667-29.866667-68.266667-29.866666H226.133333c-25.6 0-51.2 8.533333-68.266666 29.866666S128 204.8 128 230.4v567.466667c0 25.6 8.533333 51.2 29.866667 68.266666 17.066667 17.066667 42.666667 29.866667 68.266666 29.866667h567.466667c25.6 0 51.2-8.533333 68.266667-29.866667 17.066667-17.066667 29.866667-42.666667 29.866666-68.266666V226.133333c0-25.6-8.533333-46.933333-29.866666-64zM366.933333 814.933333H226.133333c-4.266667 0-8.533333 0-12.8-4.266666-4.266667-4.266667-4.266667-8.533333-4.266666-12.8V226.133333c0-4.266667 0-8.533333 4.266666-12.8 4.266667-4.266667 8.533333-4.266667 12.8-4.266666h140.8v605.866666z m448-17.066666c0 4.266667 0 8.533333-4.266666 12.8-4.266667 4.266667-8.533333 4.266667-12.8 4.266666h-354.133334V209.066667h354.133334c4.266667 0 8.533333 0 12.8 4.266666 4.266667 4.266667 4.266667 8.533333 4.266666 12.8v571.733334z"
            fill="currentColor"></path></svg>
          &nbsp;&nbsp;
        </span>
      <!-- endregion -->
    </div>
    <!--  主功能区域 -->
    <div style="display: flex;flex-direction: column; align-items: center;width: 100%">
      <div id="mossChatBox" class="chat-box" :style="{height: pageCtl.queryImages.length > 0? (height - 308)+'px':(height - 213)+'px'}">
        <!-- region: 欢迎消息 -->
        <el-row style="margin-bottom: 16px" :style="{width: pageCtl.layout.width + 'px'}" v-if="pageCtl.welcomeMsg">
          <el-col :span="1" style="text-align: left">
            <el-avatar :size="36" src="/img/noob.png" />
          </el-col>
          <el-col :span="22" class="align-left">
            <scp-md-preview v-model="pageCtl.welcomeMsg" style="border-radius: 8px;" :noKatex="true" :auto-fold-threshold="50" />
          </el-col>
        </el-row>
        <!-- endregion -->
        <el-row v-for="item in pageCtl.chatLogs" :key="item.key" style="margin-bottom: 16px" :style="{width: pageCtl.layout.width + 'px'}">
          <el-col :span="1" style="text-align: left">
            <div v-if="item.role==='assistant'">
              <el-avatar :size="36" src="/img/noob.png" />
            </div>
          </el-col>
          <el-col :span="22" :class="item.role==='assistant'?'align-left':'align-right'">
            <!-- 显示数据表格 -->
            <div v-if="item.type==='sql_to_table' && item.role==='assistant'" class="chat-box-table">
              <h3 style="margin: 0 0 8px 0">完整数据如下：</h3>
              <scp-table
                :download-specify-column="false"
                :params="{sql: item.data}"
                url="/intelligent_agent/moss/query_data_by_sql"
                download-url="/intelligent_agent/moss/download_data_by_sql"
                :editable="false" />
            </div>
            <div v-else-if="item.type==='sql_to_chart' && item.role==='assistant'" class="chat-box-table">
              <moss-visualization :sql="item.data.sql" :settings="item.data.settings" />
            </div>
            <el-card style="max-width: 480px" v-else-if="item.type==='table_confirm' && item.role==='assistant'" shadow="never">
              <template #header>
                <div class="card-header">补充信息</div>
              </template>
              <div v-for="(c_item, c_index) in item.data.items" :key="c_item.key">
                <div style="font-weight: bold;margin-bottom: var(--scp-widget-margin)">{{ c_index + 1 }}. {{ c_item.subject }}</div>
                <el-checkbox-group v-model="c_item.prefer" @change="checkConfirmTable(c_item)">
                  <el-checkbox v-for="p_item in c_item.options" :key="p_item.key" :label="p_item.label" :value="p_item.key"
                               :disabled="item.data.confirm === true">
                    <el-tooltip :show-after="200" effect="light" placement="right-end">
                      <template #content>
                        <span v-html="parseTooltip(pageCtl.tableTips[p_item.label] || p_item.label)" />
                      </template>
                      {{ p_item.label }}
                    </el-tooltip>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
              <template #footer>
                <el-button type="primary" @click="confirmAction(item.data)" v-if="item.data.confirm === false" :disabled="item.data.disabled === true">
                  Confirm
                </el-button>
                <el-button type="default" disabled v-else>已确认</el-button>
              </template>
            </el-card>
            <el-card style="max-width: 480px" v-else-if="item.type==='result_evaluate' && item.role==='assistant'" shadow="never" body-class="result-evaluator">
              <template #header>
                <div class="card-header">{{ item.data.subject }}</div>
              </template>
              <div>
                <div>{{ item.data.question }}</div>
                <el-rate v-model="item.data.rate" :colors="rateColors" size="large" allow-half :low-threshold="3" :high-threshold="4.5"
                         :disabled="item.data.confirm===true" show-score text-color="#FF9900" />
                <el-checkbox-group v-model="item.data.rate_reasons" style="margin-bottom: 8px;" v-show="item.data.rate > 0 && item.data.rate <= 3">
                  <el-checkbox label="内容准确性问题（如AI回复与需求不符、存在事实错误）" value="WRONG_ANSWER" />
                  <el-checkbox label="内容相关性问题（如AI未理解指令、答非所问）" value="IRRELEVANT_CONTENT" />
                  <el-checkbox label="格式排版问题（如生成内容格式混乱、重点不突出）" value="FORMAT_ISSUE" />
                  <el-checkbox label="效率问题（如本次回复加载耗时过长、卡顿）" value="EFFICIENCY_PROBLEM" />
                  <el-checkbox label="其他问题（请在问题描述中补充说明）" value="OTHERS" />
                </el-checkbox-group>

                <el-input v-model="item.data.comments" placeholder="针对本次问题, 您是否有具体的优化建议?" maxlength="200" show-word-limit />
              </div>
              <template #footer>
                <el-button type="primary" @click="confirmEvaluate(item.data)" v-if="item.data.confirm === false" :disabled="item.data.rate===0">Confirm
                </el-button>
                <el-button type="default" disabled v-else>感谢您的评价</el-button>
              </template>
            </el-card>
            <!-- 显示助手对话 -->
            <scp-md-preview v-else-if="item.role==='assistant' && (!!item.display)" v-model="item.display"
                            style="border-radius: 8px;" :noKatex="true"
                            :theme="item.role==='assistant'?undefined:'dark'" :auto-fold-threshold="50" />
            <!-- 显示助手对话(空) -->
            <div v-else-if="item.role==='assistant' && (!item.display)"
                 style="height: 28px;text-align: left;background-color: #fff;border-radius: 8px;padding: 5px 12px;width: 64px;font-size: 0.65rem;font-weight: bold">
              {{ pageCtl.loading.text }}
            </div>
            <!-- 显示用户对话 -->
            <scp-md-preview v-model="item.display" style="border-radius: 8px;" v-else />
          </el-col>
          <el-col :span="1" style="text-align: right">
            <div v-if="item.role==='user'">
              <el-tooltip v-if="$store.state.maintainer==='Y'" :show-after="1000" effect="light" placement="bottom" content="You are one of DSS maintainers">
                <el-avatar :size="36" :src="_avatar">
                  <img src="/img/avatar-admin.png" alt="admin" />
                </el-avatar>
              </el-tooltip>
              <el-avatar v-if="$store.state.maintainer!=='Y'" :size="36" :src="_avatar">
                <img src="/img/avatar.png" alt="avatar" />
              </el-avatar>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="query-box" :style="{width: pageCtl.layout.width + 'px'}"
           style="border-radius: 8px;border: 1px solid #E5E5E5;padding: 5px;background-color: white;margin-bottom: 8px">
        <el-row>
          <el-col :span="24" style="border-bottom: 1px dotted #E5E5E5;padding-bottom: 3px;padding-top: 8px;" v-if="pageCtl.queryImages.length > 0">
            <el-badge value="x" class="item" v-for="(item, index) in pageCtl.queryImages" :key="item" style=" margin-right:18px;">
              <template #content>
                <font-awesome-icon icon="times" style="cursor: pointer" title="delete this image" @click="deleteImage(index)" />
              </template>
              <el-image
                style="width: 100px; height: 75px;border: 1px solid var(--scp-border-color-lighter)"
                :hide-on-click-modal="true"
                :src="item"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="[item]"
                show-progress
                :initial-index="4"
                fit="cover" />
            </el-badge>
          </el-col>
          <el-col :span="24" style="height: 120px">
            <el-input
              @paste="pasteTextarea"
              :disabled="pageCtl.loading.search"
              @keydown.enter.stop="quickSearch"
              @keydown.shift.enter.stop="insertEnter"
              v-model="pageCtl.query"
              style="width: 100%;"
              :rows="6"
              :max-length="6000"
              resize="none"
              type="textarea" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20"
                  style="text-align: left;color: var(--scp-text-color-lighter);font-style: italic;padding-left: 5px;font-size: 0.45rem;align-items: flex-end;display: flex;"
                  class="query-box-tips">
            ENTER快速发送消息，SHIFT + ENTER 换行 &nbsp;
            <span v-show="pageCtl.usage.token > 0">
              {{ $thousandBitSeparator(pageCtl.usage.token) }}&nbsp;/&nbsp;¥&nbsp;{{ pageCtl.usage.fee }}
            </span>
          </el-col>
          <el-col :span="4" style="text-align: right;color: var(--scp-text-color-lighter);font-style: italic;padding-left: 5px;font-size: 0.45rem"
                  class="query-box-tips">
            <el-button @click="toggleDebugger" title="查看日志" plain round v-show="debuggerCtl.nodes.length > 1">
              <div style="font-size: 10px">
                <font-awesome-icon icon="bug" />
              </div>
            </el-button>
            <el-button @click="createNewConversation" style="margin-right: 5px;" title="开启新对话" plain round
                       v-show="pageCtl.activeAgentId !== '' && pageCtl.loading.search === false">
              <div style="font-size: 10px">
                <font-awesome-icon icon="comment-alt" />&nbsp;开启新对话
              </div>
            </el-button>
            <el-divider direction="vertical"/>
            <font-awesome-icon icon="paper-plane" class="send-btn" @click="quickSearch"/>
          </el-col>
        </el-row>
      </div>
    </div>

    <el-drawer v-model="pageCtl.drawer"
               size="1024px"
               direction="rtl"
               @opened="reDrawDebugger">
      <moss-debugger ref="mossDebuggerRef" :nodes="debuggerCtl.nodes" :edges="debuggerCtl.edges" />
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex'
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'

import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import MossDebugger from '@/components/agt/agent/Moss/Debugger.vue'
import MossVisualization from '@/components/agt/agent/Moss/Visualization.vue'

const $store = useStore()
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $randomString: any = inject('$randomString')
const $createMqttClient: any = inject('$createMqttClient')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const rateColors = ref(['#99A9BF', '#F7BA2A', '#FF9900'])

const mossDebuggerRef = ref()

const props = withDefaults(
  defineProps<{
    clickShowSidebar?: Function,
    isSidebarBtnShown?: boolean,
    queryChatLogs?: Function,
    height?: number
  }>(), {
    clickShowSidebar: undefined,
    isSidebarBtnShown: false,
    queryChatLogs: undefined,
    height: 300
  }
)

interface ChatLog {
  key: string, // log id
  type: string, // 对话日志的类型, 可选的有text, sql_to_table, sql_to_chart
  role: string, // 角色, 可选的有user和assistant
  reasoning: Array<string>, // 存放的reasoning信息, 因为reasoning可能会分批次传递过来, 所以以array存放
  answer: Array<string>, // 存放的answer信息, 同理reasoning
  images: [], // 用户在提问时, 可能会附加图片
  display: string, // 显示在页面中的信息
  data: any // 其他的信息
}

interface Node {
  id: string,
  name: string,
  tooltip: string,
  type: string
}

let client: any = {} // mqtt client
const pageCtl = reactive({
  welcomeMsg: '',
  loadingTimer: {},
  query: '',
  queryImages: [],
  activeAgentId: '',
  topic: 'scp/dss/ui/moss/' + (localStorage.getItem('username') || '').toLowerCase() + '-' + $randomString(4),
  loading: {
    search: false,
    text: '.'
  },
  chatLogs: [] as Array<ChatLog>,
  layout: {
    height: 0,
    width: 0
  },
  usage: {
    token: 0,
    fee: 0
  },
  tableTips: {},
  drawer: false
})

const toggleDebugger = () => {
  pageCtl.drawer = !pageCtl.drawer
}

const reDrawDebugger = () => {
  mossDebuggerRef.value.drawFlowChat()
}

const initNode = {
  id: 'starter',
  name: ' '.repeat(30) + '开始记录日志' + ' '.repeat(30),
  tooltip: '开始记录日志',
  type: 'sub-step'
}

const debuggerCtl = reactive({
  nodes: [initNode] as Array<Node>,
  edges: [],
  index: 0,
  subIndex: 1
})

const parseTooltip = (tooltip) => {
  return tooltip.replace(/，/g, '<br>')
}

const parseReasoningAndAnswer = (item) => {
  const reasoning = item.reasoning
  const answer = item.answer
  const result = []

  if (item.images && item.images.length > 0) {
    for (let i = 0; i < item.images.length; i++) {
      result.push('![image' + i + '](' + item.images[i] + ')')
    }
  }

  if (reasoning.length > 0) {
    result.push('> ' + reasoning.join('\n> '))
  }
  if (answer.length > 0) {
    if (reasoning.length > 0) {
      result.push('\n\n')
    }
    result.push(answer.join(''))
  }
  return result.join('')
}

const connectMqtt = () => {
  client = $createMqttClient('scp-ui-moss')
  client.on('connect', e => {
    client.subscribe(pageCtl.topic, { qos: 2 }, (err) => {
      if (!err) {
        console.log('subscribe topic: ' + pageCtl.topic)
      }
    })
  })

  // 接收后台传递的消息
  client.on('message', (topic, message) => {
    const messageObj = JSON.parse('' + message)
    const answerType = messageObj.answer_type // 获取消息类型
    // 选择智能体并没有代表结束, 所以不能将loading状态取消
    if (messageObj.token) {
      pageCtl.usage.token = messageObj.token || 0
      pageCtl.usage.fee = messageObj.fee || 0
    }
    if (answerType !== 'select_agent' && answerType !== 'logs') {
      pageCtl.loading.search = false
    }
    if (answerType === 'select_agent') { // 选择智能体
      pageCtl.activeAgentId = messageObj.reasoning
      search(messageObj.answer, false)
    } else if (answerType === 'table_confirm') { // 表确认
      pageCtl.chatLogs.push(JSON.parse(messageObj.answer))
    } else if (answerType === 'result_evaluate') { // 表确认
      pageCtl.chatLogs.push(JSON.parse(messageObj.answer))
      setTimeout(() => scrollChatBox(), 500)
    } else if (answerType === 'termination') { // 收到终止信号
      const msg = {
        key: $randomString(8),
        type: 'termination',
        role: 'assistant',
        reasoning: [],
        answer: ['【新对话已开启】'],
        images: [],
        display: ''
      } as ChatLog

      if (messageObj.reasoning) {
        msg.reasoning.push(messageObj.reasoning)
      }
      msg.display = parseReasoningAndAnswer(msg)
      pageCtl.chatLogs.push(msg)
      pageCtl.activeAgentId = ''
      if (messageObj.reasoning !== '等待超时') {
        debuggerCtl.nodes = [initNode]
        debuggerCtl.edges = []
      }
      debuggerCtl.index = 0
      debuggerCtl.subIndex = 1

      // 禁用所有未确认的table_confirm组件
      for (let i = 0; i < pageCtl.chatLogs.length; i++) {
        const item = pageCtl.chatLogs[i]
        if (item.type === 'table_confirm') {
          item.data.disabled = true
        }
      }

      // 刷新左侧历史对话
      setTimeout(() => props.queryChatLogs(), 500)
    } else if (answerType === 'text') { // 处理文本消息
      // 因为有加载画面, 所以需要修改上一条对话记录
      let item = pageCtl.chatLogs[pageCtl.chatLogs.length - 1]
      // 如果上一条是确认, 则新建一个对话内容
      if (item.type === 'table_confirm') {
        item = {
          key: $randomString(8),
          type: 'text',
          role: 'assistant',
          reasoning: [],
          answer: [],
          images: [],
          display: '',
          data: {}
        } as ChatLog
        pageCtl.chatLogs.push(item)
      }
      if (item) {
        if (messageObj.reasoning) {
          item.reasoning.push(messageObj.reasoning)
        }
        if (messageObj.answer) {
          item.answer.push(messageObj.answer)
        }
        item.display = parseReasoningAndAnswer(item)
      }
    } else if (answerType === 'sql') {
      // 后台返回的类型为sql, 则直接转交sql_to_table处理
      pageCtl.chatLogs.push({
        role: 'assistant',
        type: 'sql_to_table',
        data: messageObj.answer
      } as ChatLog)
    } else if (answerType === 'chart') {
      // 后台返回的类型为chart, 生成sql_to_chart需要的参数类型
      const settings = JSON.parse(messageObj.answer)
      const chartSettings = settings.chart_settings
      // 因为table之前展示过了, 这里就不再展示
      if (chartSettings.type !== 'table') {
        pageCtl.chatLogs.push({
          role: 'assistant',
          type: 'sql_to_chart',
          data: { sql: settings.sql, settings: chartSettings }
        } as ChatLog)
      }
    } else if (answerType === 'logs') { // 选择智能体
      addLogNodeAndEdge(messageObj.name, messageObj.tooltip + '', messageObj.type)
    }
    scrollChatBox()
  })
}

const addLogNodeAndEdge = (name, tooltip, type) => {
  const id = $randomString(8)
  if (type === 'step') {
    debuggerCtl.subIndex = 1
    name = (++debuggerCtl.index) + '. ' + name
  } else {
    if (debuggerCtl.index > 0) {
      name = debuggerCtl.index + '.' + (debuggerCtl.subIndex++) + ' ' + name
    }
  }

  debuggerCtl.nodes.push({ id, name, tooltip, type } as Node)

  const start = debuggerCtl.nodes[debuggerCtl.nodes.length - 2].id
  const end = id

  if (type === 'step') {
    for (let i = debuggerCtl.nodes.length - 2; i >= 0; i--) {
      const n = debuggerCtl.nodes[i]
      if (n.type === 'step') {
        debuggerCtl.edges.push({ start: n.id, end: id })
        break
      }
    }
  }

  debuggerCtl.edges.push({ start, end })
}

const quickSearch = (event) => {
  event.preventDefault()
  if (event.shiftKey === false && pageCtl.query.trim()) {
    searchAgentId()
  }
}

const confirmAction = (data) => {
  if (data.confirm === false) {
    data.confirm = true
    $axios({
      method: 'post',
      url: data.callback,
      data: {
        topic: pageCtl.topic,
        agentId: pageCtl.activeAgentId,
        ...data
      }
    }).catch((error) => {
      console.log(error)
    })
  }
}

const confirmEvaluate = (data) => {
  if (data.confirm === false) {
    if (data.rate <= 3 && data.rate_reasons.length === 0) {
      $message.error('请选择至少一个反馈事项')
      return
    }
    data.confirm = true
    $axios({
      method: 'post',
      url: data.callback,
      data: {
        question: data.question,
        sql: data.sql,
        tables: data.tables,
        rate: data.rate,
        rate_reasons: JSON.stringify(data.rate_reasons),
        comments: data.comments
      }
    }).catch((error) => {
      console.log(error)
    })
  }
}

const checkConfirmTable = (data) => {
  console.log(JSON.stringify(data))
  if (data.prefer.indexOf('NONE_OF_THE_ABOVE') === data.prefer.length - 1) {
    data.prefer = ['NONE_OF_THE_ABOVE']
  } else {
    data.prefer = data.prefer.filter(v => v !== 'NONE_OF_THE_ABOVE')
  }
}

const prepareChat = () => {
  pageCtl.loading.search = true
  const userInput = {
    key: $randomString(8),
    role: 'user',
    reasoning: [],
    answer: [pageCtl.query],
    images: pageCtl.queryImages,
    display: ''
  } as ChatLog
  userInput.display = parseReasoningAndAnswer(userInput)
  pageCtl.chatLogs.push(userInput)

  pageCtl.chatLogs.push({
    key: $randomString(8),
    role: 'assistant',
    reasoning: [],
    answer: [],
    images: [],
    display: ''
  } as ChatLog)

  scrollChatBox()
  pageCtl.query = ''
  pageCtl.queryImages = []
}

const quickInput = (obj) => {
  pageCtl.query = obj.innerHTML
}

window.quickInput = quickInput

const searchAgentId = () => {
  if (pageCtl.activeAgentId) {
    search(pageCtl.query)
  } else {
    const query = pageCtl.query
    const images = pageCtl.queryImages
    prepareChat()
    $axios({
      method: 'post',
      url: '/intelligent_agent/moss/search_agent_id',
      data: {
        query,
        images,
        topic: pageCtl.topic
      }
    }).then((body) => {
      if (body.status !== 200) {
        const item = pageCtl.chatLogs[pageCtl.chatLogs.length - 1]
        item.answer = [body.result]
        item.display = parseReasoningAndAnswer(item)
        $axios({
          method: 'post',
          url: '/intelligent_agent/moss/create_new_conversation',
          data: {
            topic: pageCtl.topic
          }
        })
      }
    }).catch((error) => {
      console.log(error)
    })
  }
}

const search = (query, needPrepare = true) => {
  const images = pageCtl.queryImages
  if (needPrepare) {
    prepareChat()
  }
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/search',
    data: {
      query,
      images,
      topic: pageCtl.topic,
      agentId: pageCtl.activeAgentId
    }
  }).then((body) => {
    if (body.status !== 200) {
      const item = pageCtl.chatLogs[pageCtl.chatLogs.length - 1]
      item.answer = [body.result]
      item.display = parseReasoningAndAnswer(item)
      $axios({
        method: 'post',
        url: '/intelligent_agent/moss/create_new_conversation',
        data: {
          topic: pageCtl.topic
        }
      })
    }
  }).catch((error) => {
    console.log(error)
  })
}

const createNewConversation = () => {
  if (pageCtl.loading.search) {
    return
  }
  pageCtl.loading.search = true
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/create_new_conversation',
    data: {
      topic: pageCtl.topic
    }
  })
  setTimeout(() => props.queryChatLogs(), 500)
}

const initTableTips = () => {
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/query_table_tips'
  }).then((body) => {
    pageCtl.tableTips = body
  }).catch((error) => {
    console.log(error)
  })
}

const scrollChatBox = () => {
  nextTick(() => {
    const chatBox = document.getElementById('mossChatBox') as HTMLPreElement
    chatBox.scrollTo({ top: chatBox.scrollHeight, behavior: 'smooth' })
  })
}

const insertEnter = (event) => {
  event.preventDefault()
  if (event.shiftKey && pageCtl.query.trim()) {
    const textarea = event.target
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    // 在光标位置插入换行符
    textarea.value = textarea.value.substring(0, start) + '\n' + textarea.value.substring(end)
    // 移动光标到换行符后面
    textarea.selectionStart = textarea.selectionEnd = start + 1
    nextTick(() => {
      const queryTextArea = document.getElementsByClassName('query-box')[0].getElementsByClassName('el-textarea__inner')[0] as HTMLPreElement
      queryTextArea.scrollTo({ top: queryTextArea.scrollHeight, behavior: 'smooth' })
    })
  }
}

watch(() => pageCtl.loading.search, (newVal) => {
  clearInterval(pageCtl.loadingTimer)
  pageCtl.loading.text = '.'
  if (newVal) {
    pageCtl.loadingTimer = setInterval(() => {
      switch (pageCtl.loading.text.length) {
        case 1:
          pageCtl.loading.text = '..'
          break
        case 2:
          pageCtl.loading.text = '...'
          break
        case 3:
          pageCtl.loading.text = '....'
          break
        case 4:
          pageCtl.loading.text = '.'
          break
      }
    }, 1000)
  } else {
    pageCtl.loading.text = ''
  }
})

const _avatar = computed(() => {
  return 'https://scp-dss.cn.schneider-electric.com/avatar/' + localStorage.getItem('username') + '.jpg'
})

const pasteTextarea = (e) => {
  const items = e.clipboardData?.items
  if (!items) return

  // 检查是否有文件
  const files = []
  for (const item of items) {
    if (item.type.startsWith('text/rtf')) {
      return
    }
    if (item.kind === 'file' && item.type.startsWith('image/')) {
      files.push(item.getAsFile())
      break
    }
  }

  if (files.length > 0) {
    // 阻止默认粘贴行为
    e.preventDefault()

    const data = new FormData()
    data.append('file', files[0])
    data.append('saveType', 'temp')

    $axios({
      method: 'post',
      url: '/system/save_image_to_local',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then((body) => {
      pageCtl.queryImages.push(body)
    }).catch((error) => {
      console.log(error)
    })
  }
}

const deleteImage = (index) => {
  pageCtl.queryImages.splice(index, 1)
}

onMounted(() => {
  pageCtl.layout.width = document.documentElement.clientWidth * 0.76
  window.onresize = () => {
    pageCtl.layout.width = document.documentElement.clientWidth * 0.76
  }
  initTableTips()
  connectMqtt()
})

const closeMqttClient = () => {
  try {
    client.end()
  } catch (e) {

  }
}

const destory = async () => {
  closeMqttClient()
  await $axios({
    method: 'post',
    url: '/intelligent_agent/moss/create_new_conversation',
    data: {
      topic: pageCtl.topic
    }
  })
}

onBeforeUnmount(() => {
  destory()
})

window.onbeforeunload = () => {
  destory()
}

const setQueryParams = (params) => {
  pageCtl.query = params.query
  pageCtl.queryImages = params.queryImages
}

const selectAgent = (item) => {
  pageCtl.activeAgentId = item.AGENT_ID
  pageCtl.welcomeMsg = item.WELCOME_MSG
}

defineExpose({
  selectAgent,
  searchAgentId,
  setQueryParams
})
</script>

<script lang="ts">
export default {
  name: 'MossChat'
}
</script>

<style lang="scss">
#MOSSChat {
  width: 100%;
  display: flex;
  flex-direction: column;

  .send-btn {
    font-size: 0.55rem;
    cursor: pointer;
    margin: 2px 10px 0 5px;

    :hover {
      color: var(--scp-text-color-highlight);
    }
  }

  .chat-title {
    font-size: 0.6rem;
    padding: 12px 5px 0 5px;
    display: flex;
    align-items: flex-start;
    height: 26px;
  }

  .el-drawer__header {
    display: none;
  }

  .el-drawer__body {
    padding: var(--scp-widget-margin) !important;
  }

  .query-box {
    .el-textarea__inner {
      box-shadow: none !important;
      background-color: transparent !important;
    }

    .query-box-tips {
      height: 26px;
      line-height: 26px;

      .el-select__wrapper {
        box-shadow: none !important;
      }

      .el-select .el-input__inner {
        color: var(--scp-text-color-lighter);
        font-style: italic;
        font-size: 0.45rem
      }

      .el-select__selected-item {
        color: var(--scp-text-color-lighter);
      }

      .el-select .el-input .el-select__caret {
        color: transparent !important;
      }
    }
  }

  .chat-box {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: auto;
    margin-bottom: 8px;

    .chat-box-table {
      padding: 10px 20px !important;
      border-radius: 8px;
      background-color: var(--scp-bg-color);
      border: 1px solid #efefef;
    }

    .el-card {
      --el-card-border-radius: 8px !important;
      --el-border-radius-base: 4px !important;
    }

    .el-card__header {
      padding: 8px 16px !important;

      .card-header {
        font-weight: bold;
        font-size: 0.55rem;
      }
    }

    .el-card__footer {
      padding: 8px 16px !important;
      text-align: right;
    }

    .align-right {
      .github-theme table tr {
        background-color: transparent !important;
      }

      padding-right: 8px;
      padding-left: 8px;

      .md-editor-previewOnly {
        float: right;
        border-radius: 8px !important;
        background-color: #2D65F7 !important;

        * {
          color: #fff !important;
        }

        svg, .md-editor-code {
          * {
            color: #333 !important;
          }
        }
      }
    }

    .align-left {
      padding-right: 8px;
      padding-left: 8px;

      .md-editor-previewOnly {
        border-radius: 8px !important;
        float: left;
        border: 1px solid #efefef;
      }
    }

    .md-editor-previewOnly {
      display: inline-block !important;
      width: auto !important;

      pre {
        margin-bottom: 5px !important;
      }

      .md-editor-preview {
        padding: 10px 20px !important;
      }

      .execute-button {
        text-align: right;
        padding-right: 2px;

        .svg-inline--fa {
          cursor: pointer;
        }

        .data-value {
          display: none !important;
        }
      }
    }

    .md-editor-preview {
      blockquote {
        border-left: 1.5px solid var(--scp-border-color) !important;
        color: #818080 !important;
        margin-left: 0 !important;
        margin-bottom: 0 !important;
      }

      blockquote + p {
        margin-top: 16px;
      }

      p:last-child {
        margin-bottom: 2px !important;
      }

      img {
        max-width: 200px;
        display: block;
        margin-bottom: var(--scp-widget-margin);
      }
    }

    .result-evaluator {
      padding: 8px 20px 8px 20px !important;

      .el-rate__text {
        padding-left: 10px;
        font-size: 0.55rem;
      }

      .el-input__wrapper {
        border-radius: 4px;
      }
    }
  }
}
</style>
