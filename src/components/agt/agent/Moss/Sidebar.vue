<template>
  <div id="MOSSSidebar">
    <el-row class="sidebar">
      <el-col :span="20">
        <!-- region: 新对话 -->
        <div class="chat-button new-chat" @click="clickNewChat">
          <svg data-v-f2dda92e="" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img"
               class="new-icon iconify" width="20" height="20" viewBox="0 0 1024 1024">
            <path
              d="M475.136 561.152v89.74336c0 20.56192 16.50688 37.23264 36.864 37.23264s36.864-16.67072 36.864-37.23264v-89.7024h89.7024c20.60288 0 37.2736-16.54784 37.2736-36.864 0-20.39808-16.67072-36.864-37.2736-36.864H548.864V397.63968A37.0688 37.0688 0 0 0 512 360.448c-20.35712 0-36.864 16.67072-36.864 37.2736v89.7024H385.4336a37.0688 37.0688 0 0 0-37.2736 36.864c0 20.35712 16.67072 36.864 37.2736 36.864h89.7024z"
              fill="currentColor"></path>
            <path
              d="M512 118.784c-223.96928 0-405.504 181.57568-405.504 405.504 0 78.76608 22.44608 152.3712 61.35808 214.6304l-44.27776 105.6768a61.44 61.44 0 0 0 56.68864 85.1968H512c223.92832 0 405.504-181.53472 405.504-405.504 0-223.92832-181.57568-405.504-405.504-405.504z m-331.776 405.504a331.776 331.776 0 1 1 331.73504 331.776H198.656l52.59264-125.5424-11.59168-16.62976A330.09664 330.09664 0 0 1 180.224 524.288z"
              fill="currentColor"></path>
          </svg>
          <span style="padding-left: 8px;font-weight: bold">New Chat</span>
        </div>
        <!-- endregion -->
        <!-- region: MOSS+ -->
        <div class="chat-button moss-plus" @click="clickMossPlus">
          <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" width="20" height="20" viewBox="0 0 1024 1024">
            <path
              d="M202.197333 444.928c-17.365333 17.365333-46.506667 14.933333-56.192-7.637333a221.738667 221.738667 0 0 1 360.362667-244.352l179.2 179.2A37.973333 37.973333 0 0 1 631.893333 425.813333l-179.2-179.2a145.664 145.664 0 0 0-241.365333 148.650667c5.632 17.194667 3.669333 36.864-9.088 49.621333z m140.714667 157.738667a37.973333 37.973333 0 0 0 0 53.76l174.677333 174.634666a221.653333 221.653333 0 0 0 363.52-236.672c-9.045333-23.552-39.04-26.538667-56.917333-8.704-12.373333 12.416-14.72 31.36-9.856 48.213334a145.664 145.664 0 0 1-242.986667 143.445333l-174.72-174.677333a37.973333 37.973333 0 0 0-53.717333 0zM448.725333 512a63.317333 63.317333 0 1 0 126.592 0 63.317333 63.317333 0 0 0-126.634666 0zM380.373333 330.112l-187.477333 187.477333a221.653333 221.653333 0 0 0 221.781333 368.64c25.429333-7.765333 29.610667-39.424 10.794667-58.197333-11.690667-11.733333-29.226667-14.634667-45.44-11.221333A145.664 145.664 0 0 1 246.613333 571.306667l187.477334-187.477334a37.973333 37.973333 0 1 0-53.76-53.717333z m263.168 363.776a37.973333 37.973333 0 1 1-53.76-53.76l187.52-187.477333a145.664 145.664 0 0 0-133.418666-245.461334c-16.213333 3.413333-33.749333 0.512-45.482667-11.221333-18.773333-18.773333-14.634667-50.432 10.794667-58.24a221.653333 221.653333 0 0 1 221.866666 368.64l-187.52 187.52z"
              fill="currentColor"></path>
          </svg>
          <span style="padding-left: 8px;font-weight: bold">MOSS+</span>
        </div>
        <!-- endregion -->
        <!-- region: 对话历史 -->
        <div class="chat-button chat-history">
          <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img"
               class="nav-icon iconify" width="20" height="20" viewBox="0 0 1024 1024">
            <path
              d="M512 81.066667c-233.301333 0-422.4 189.098667-422.4 422.4s189.098667 422.4 422.4 422.4 422.4-189.098667 422.4-422.4-189.098667-422.4-422.4-422.4z m-345.6 422.4a345.6 345.6 0 1 1 691.2 0 345.6 345.6 0 1 1-691.2 0z m379.733333-174.933334a38.4 38.4 0 0 0-76.8 0v187.733334a38.4 38.4 0 0 0 11.264 27.136l93.866667 93.866666a38.4 38.4 0 1 0 54.272-54.272L546.133333 500.352V328.533333z"
              fill="currentColor"></path>
          </svg>
          <span style="padding-left: 8px;font-weight: bold">Chat History</span>
        </div>
        <div v-for="item in pageCtl.historyChat" class="chat-button chat-history-details" :key="item.ID" @click="clickChatLog(item)">
          <el-tooltip :show-after="1000" effect="light" placement="right-end" :content="item.SUBJECT">
            {{ item.SUBJECT }}
          </el-tooltip>
        </div>
        <!-- endregion -->
        <!-- region: 查看所有对话 -->
        <div class="chat-button all-chat" @click="clickAllChat">
          <span style="font-weight: bold">All Chats</span>
        </div>
        <!-- endregion -->
        <!-- region: 保存的问题 -->
        <div class="chat-button chat-history" v-show="currentContainer === 'WELCOME_PAGE'">
          <svg data-v-58697b5c="" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 1024 1024">
            <path fill="currentColor"
                  d="M602.496 240.448A192 192 0 1 1 874.048 512l-316.8 316.8A256 256 0 0 1 195.2 466.752L602.496 59.456l45.248 45.248L240.448 512A192 192 0 0 0 512 783.552l316.8-316.8a128 128 0 1 0-181.056-181.056L353.6 579.904a32 32 0 1 0 45.248 45.248l294.144-294.144 45.312 45.248L444.096 670.4a96 96 0 1 1-135.744-135.744l294.144-294.208z"></path>
          </svg>
          <span style="padding-left: 8px;font-weight: bold">My Archive</span>
        </div>
        <div v-show="currentContainer === 'WELCOME_PAGE'" class="chat-archive">
          <div v-for="(item, index) in pageCtl.archiveList" class="archive-btn" :key="item">
            <el-row>
              <el-col :span="20" class="archive-btn-title" @click="clickMyArchive(item.QUESTION)">
                <el-tooltip :show-after="1000" effect="light" placement="top-start" :content="item.QUESTION" :show-arrow="false">
                  {{ item.QUESTION }}
                </el-tooltip>
              </el-col>
              <el-col :span="4">
                <font-awesome-icon class="archive-closs-btn" icon="times" title="delete this" @click.prevent="()=>deleteMyArchive(index, item.ID)" />
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="chat-button all-chat" @click="pageCtl.visible.newArchive=true" v-show="currentContainer === 'WELCOME_PAGE'">
          <span style="font-weight: bold">Add to</span>
        </div>
        <!-- endregion -->
      </el-col>
      <!-- region: 打开关闭侧边栏 -->
      <el-col :span="4" style="padding-top: 7px">
        <svg style="cursor: pointer;" @click="clickHideSidebar(false)"
             xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" data-v-f2dda92e="" width="20" height="20" viewBox="0 0 1024 1024"
             class="iconify">
          <path
            d="M861.866667 162.133333c-17.066667-17.066667-42.666667-29.866667-68.266667-29.866666H226.133333c-25.6 0-51.2 8.533333-68.266666 29.866666S128 204.8 128 230.4v567.466667c0 25.6 8.533333 51.2 29.866667 68.266666 17.066667 17.066667 42.666667 29.866667 68.266666 29.866667h567.466667c25.6 0 51.2-8.533333 68.266667-29.866667 17.066667-17.066667 29.866667-42.666667 29.866666-68.266666V226.133333c0-25.6-8.533333-46.933333-29.866666-64zM366.933333 814.933333H226.133333c-4.266667 0-8.533333 0-12.8-4.266666-4.266667-4.266667-4.266667-8.533333-4.266666-12.8V226.133333c0-4.266667 0-8.533333 4.266666-12.8 4.266667-4.266667 8.533333-4.266667 12.8-4.266666h140.8v605.866666z m448-17.066666c0 4.266667 0 8.533333-4.266666 12.8-4.266667 4.266667-8.533333 4.266667-12.8 4.266666h-354.133334V209.066667h354.133334c4.266667 0 8.533333 0 12.8 4.266666 4.266667 4.266667 4.266667 8.533333 4.266666 12.8v571.733334z"
            fill="currentColor"></path>
        </svg>
      </el-col>
      <!-- endregion -->
    </el-row>

    <el-dialog
      v-model="pageCtl.visible.newArchive"
      title="Add to My Archive"
      width="600">
      <el-input v-model="pageCtl.newQuestion" size="default" clearable placeholder="Type your question..." />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="addToArchive" :loading="pageCtl.loading.newArchive">
            <font-awesome-icon icon="plus" />&nbsp;
            Add
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const $axios: any = inject('$axios')
const $message: any = inject('$message')

const props = withDefaults(
  defineProps<{
    clickChatLog?: Function,
    clickHideSidebar?: Function,
    clickNewChat?: Function,
    clickAllChat?: Function,
    clickMossPlus?: Function,
    clickMyArchive?: Function,
    height?: number,
    currentContainer?: string
  }>(), {
    clickChatLog: undefined,
    clickHideSidebar: undefined,
    clickNewChat: undefined,
    clickAllChat: undefined,
    clickMossPlus: undefined,
    clickMyArchive: undefined,
    height: 300,
    currentContainer: ''
  }
)

const pageCtl = reactive({
  sidebar: true,
  visible: {
    newArchive: false
  },
  loading: {
    newArchive: false
  },
  newQuestion: '',
  historyChat: [],
  archiveList: []
})

const queryChatLogs = () => {
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/query_chat_logs'
  }).then((body) => {
    pageCtl.historyChat = body
  }).catch((error) => {
    console.log(error)
  })
}

const queryMyArchive = () => {
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/query_my_archive'
  }).then((body) => {
    pageCtl.archiveList = body
  }).catch((error) => {
    console.log(error)
  })
}

const addToArchive = () => {
  if (!pageCtl.newQuestion) {
    $message.error('Type your question please.')
    return
  }
  pageCtl.loading.newArchive = true
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/add_to_archive',
    data: {
      question: pageCtl.newQuestion
    }
  }).then(() => {
    pageCtl.newQuestion = ''
    pageCtl.visible.newArchive = false
    queryMyArchive()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.newArchive = false
  })
}

const deleteMyArchive = (index, id) => {
  pageCtl.archiveList.splice(index, 1)
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/delete_my_archive',
    data: {
      id
    }
  }).then(() => {
    queryMyArchive()
  }).catch((error) => {
    console.log(error)
  })
}

onMounted(() => {
  queryChatLogs()
  queryMyArchive()
})

defineExpose({
  queryChatLogs
})
</script>

<script lang="ts">
export default {
  name: 'MossSidebar'
}
</script>

<style lang="scss">
#MOSSSidebar {
  .sidebar {
    width: 240px;
    margin-top: 10px;

    .chat-button {
      width: 175px;
      height: 1.5rem;
      padding-left: 10px;
      margin-left: 5px;
      display: flex;
      align-items: center;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
    }

    .archive-btn {
      height: 1.5rem;
      padding-left: 10px;
      align-items: center;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      margin-left: 28px;
      cursor: pointer;
      color: rgba(0, 0, 0, .6);

      .archive-btn-title {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: block;
        line-height: 1.5rem;
      }
    }

    .new-chat {
      background-color: #fff;
      border-radius: 8px;
      cursor: pointer;
    }

    .new-chat:hover {
      box-shadow: 0 0 2px 3px rgba(0, 0, 0, .015);
    }

    .chat-history {
      margin-top: 0;
    }

    .moss-plus {
      margin-top: 8px;
      cursor: pointer;
    }

    .chat-history-details, .all-chat {
      margin-left: 28px;
      cursor: pointer;
      color: rgba(0, 0, 0, .6);

      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: block;
      line-height: 1.5rem;
    }

    .chat-history-details:hover, .all-chat:hover,
    .moss-plus:hover, .archive-btn:hover {
      border-radius: 8px;
      background-color: rgba(0, 0, 0, .03);
    }

    .chat-archive {
      max-height: 180px;
      overflow-x: hidden;
      overflow-y: auto;

      scrollbar-width: none;
      scrollbar-color: transparent transparent;
    }

    .chat-archive::-webkit-scrollbar {
      display: none;
    }

    .archive-btn:hover {
      .archive-closs-btn {
        color: var(--scp-text-color-error) !important;
        display: inline-block;
      }
    }

    .archive-closs-btn {
      color: var(--scp-text-color-error);
      float: right;
      margin: 0.5rem;
      display: none;
    }
  }

  .el-input__wrapper {
    --el-input-border-radius: 6px !important;
  }
}
</style>
