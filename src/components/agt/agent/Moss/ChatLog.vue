<template>
  <div id="MOSSChatLog">
    <!-- region: 右边栏标题 -->
    <div class="chat-title">
      <!-- region: 右边栏, 打开关闭侧边栏 -->
      <span style="cursor: pointer" @click="clickShowSidebar(true)" :style="{color:isSidebarBtnShown? 'transparent':'inherit'}">
          <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" data-v-f2dda92e="" style="" width="20"
               height="20" viewBox="0 0 1024 1024" class="iconify"><path
            d="M861.866667 162.133333c-17.066667-17.066667-42.666667-29.866667-68.266667-29.866666H226.133333c-25.6 0-51.2 8.533333-68.266666 29.866666S128 204.8 128 230.4v567.466667c0 25.6 8.533333 51.2 29.866667 68.266666 17.066667 17.066667 42.666667 29.866667 68.266666 29.866667h567.466667c25.6 0 51.2-8.533333 68.266667-29.866667 17.066667-17.066667 29.866667-42.666667 29.866666-68.266666V226.133333c0-25.6-8.533333-46.933333-29.866666-64zM366.933333 814.933333H226.133333c-4.266667 0-8.533333 0-12.8-4.266666-4.266667-4.266667-4.266667-8.533333-4.266666-12.8V226.133333c0-4.266667 0-8.533333 4.266666-12.8 4.266667-4.266667 8.533333-4.266667 12.8-4.266666h140.8v605.866666z m448-17.066666c0 4.266667 0 8.533333-4.266666 12.8-4.266667 4.266667-8.533333 4.266667-12.8 4.266666h-354.133334V209.066667h354.133334c4.266667 0 8.533333 0 12.8 4.266666 4.266667 4.266667 4.266667 8.533333 4.266666 12.8v571.733334z"
            fill="currentColor"></path></svg>
          &nbsp;&nbsp;
        </span>
      <!-- endregion -->
      <b>{{ pageCtl.currentChat.SUBJECT }}</b>
    </div>
    <!-- endregion -->
    <!-- region: 右边栏内容 -->
    <div class="chat-box">
      <div class="chat-box-container">
        <el-row v-for="(item, index) in (pageCtl.currentChat.LOGS || [])" :key="index" style="margin-bottom: 16px">
          <!-- region: 对话左侧头像 -->
          <el-col :span="1" style="text-align: left">
            <div v-if="item.role==='assistant'">
              <el-avatar :size="36" src="/img/noob.png" />
            </div>
          </el-col>
          <!-- endregion -->
          <!-- region: 对话内容 -->
          <el-col :span="22" :class="item.role==='assistant'?'align-left':'align-right'">
            <!-- 显示数据表格 -->
            <div v-if="item.type==='sql' && item.role==='assistant'" class="chat-box-table">
              <h3>完整数据如下：</h3>
              <scp-table
                :download-specify-column="false"
                :params="{sql: item.query}"
                url="/intelligent_agent/moss/query_data_by_sql"
                download-url="/intelligent_agent/moss/download_data_by_sql"
                :editable="false" />
            </div>
            <div v-else-if="item.type==='chart' && item.role==='assistant'" class="chat-box-table">
              <moss-visualization :sql="item.query.sql" :settings="item.query.settings" />
            </div>
            <el-card style="max-width: 480px" v-else-if="item.type==='table_confirm' && item.role==='assistant'" shadow="never">
              <template #header>
                <div class="card-header">补充信息</div>
              </template>
              <div v-for="(c_item, c_index) in item.query.data.items" :key="c_item.key">
                <div style="font-weight: bold;margin-bottom: var(--scp-widget-margin)">{{ c_index + 1 }}. {{ c_item.subject }}</div>
                <el-checkbox-group v-model="c_item.prefer">
                  <el-checkbox v-for="p_item in c_item.options" :key="p_item.key" :label="p_item.label" :value="p_item.key" :disabled="true">
                    {{ p_item.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </el-card>
            <!-- 显示助手对话 -->
            <scp-md-preview v-else-if="item.role==='assistant' && (!!item.display)" v-model="item.display"
                            style="border-radius: 8px;" :noKatex="true"
                            :theme="item.role==='assistant'?undefined:'dark'" :auto-fold-threshold="50" />
            <!-- 显示用户对话 -->
            <scp-md-preview v-model="item.display" style="border-radius: 8px;" v-else />
          </el-col>
          <!-- endregion -->
          <!-- region: 对话右侧头像 -->
          <el-col :span="1" style="text-align: right">
            <div v-if="item.role==='user'">
              <el-tooltip v-if="$store.state.maintainer==='Y'" :show-after="1000" effect="light" placement="bottom" content="You are one of DSS maintainers">
                <el-avatar :size="36" :src="_avatar">
                  <img src="/img/avatar-admin.png" alt="admin" />
                </el-avatar>
              </el-tooltip>
              <el-avatar v-if="$store.state.maintainer!=='Y'" :size="36" :src="_avatar">
                <img src="/img/avatar.png" alt="avatar" />
              </el-avatar>
            </div>
          </el-col>
          <!-- endregion -->
        </el-row>
      </div>
    </div>
    <!-- endregion -->
  </div>
</template>
<script lang="ts" setup>
import { computed, inject, onMounted, reactive } from 'vue'
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'
import { useStore } from 'vuex'
import MossVisualization from '@/components/agt/agent/Moss/Visualization.vue'

const $randomString: any = inject('$randomString')
const $store = useStore()
const $axios: any = inject('$axios')

const props = withDefaults(
  defineProps<{
    clickShowSidebar?: Function,
    isSidebarBtnShown?: boolean,
    height?: number
  }>(), {
    clickShowSidebar: undefined,
    isSidebarBtnShown: false,
    height: 300
  }
)

const pageCtl = reactive({
  currentChat: {
    ID: '',
    SUBJECT: '',
    LOGS: []
  }
})

const mergeLogs = (logs) => {
  if (logs.length === 0) {
    return []
  }

  const result = []
  let i = 0

  let current = logs[i]
  while (i < logs.length) {
    const next = logs[i + 1]

    // 如果当前是table_confirm, 那么尝试性的从下一个节点取用户选择的结果
    if (current.type === 'table_confirm') {
      current.query = JSON.parse(current.query)

      if (next && next.type === 'text' && next.role === 'user') {
        current.query.prefer = JSON.parse(next.query)
        i++
      } else {
        for (let i = 0; i < current.query.data.items.length; i++) {
          current.query.data.items[i].prefer = []
        }
      }
      result.push(current)
      current = logs[++i]
      continue
    }

    if (current.type === 'chart') {
      current.query = JSON.parse(current.query)
    }

    if (next && current.role === next.role && current.type === next.type) {
      // 合并当前元素和下一个元素
      current.query = `\n${next.query}`
      current.reasoning += `\n${next.reasoning}`
      // 跳过下一个元素
      i += 1
    } else {
      // 不需要合并，直接保留当前元素
      result.push(current)
      current = logs[++i]
    }
  }
  return result
}

const showChatLog = (id) => {
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/query_chat_by_id',
    data: {
      id
    }
  }).then((body) => {
    pageCtl.currentChat.ID = body.ID
    pageCtl.currentChat.SUBJECT = body.SUBJECT
    const logs = mergeLogs(JSON.parse(body.LOGS))

    const display = []
    for (const index in logs) {
      const log = logs[index]
      display.push({
        id: $randomString(6),
        role: log.role,
        type: log.type,
        query: log.query,
        display: parseReasoningAndAnswer(log)
      })
    }
    pageCtl.currentChat.LOGS = display
  }).catch((error) => {
    console.log(error)
  })
}

const parseReasoningAndAnswer = (item) => {
  const reasoning = item.reasoning
  const answer = item.query
  const result = []

  if (item.images && item.images.length > 0) {
    for (let i = 0; i < item.images.length; i++) {
      result.push('![image' + i + '](' + item.images[i] + ')')
    }
  }

  if (reasoning.length > 0) {
    result.push('> ' + reasoning)
  }
  if (answer.length > 0) {
    if (reasoning.length > 0) {
      result.push('\n\n')
    }
    result.push(answer)
  }
  return result.join('')
}

const _avatar = computed(() => {
  return 'https://scp-dss.cn.schneider-electric.com/avatar/' + localStorage.getItem('username') + '.jpg'
})

defineExpose({
  showChatLog
})
</script>

<script lang="ts">
export default {
  name: 'MossChatLog'
}
</script>

<style lang="scss">
#MOSSChatLog {
  height: 100%;

  .chat-title {
    font-size: 0.6rem;
    padding: 12px 5px;
    display: flex;
    align-items: flex-start;
    height: 26px;
  }

  .chat-box {
    .chat-box-container {
      width: calc(100% - 200px);
      margin: 20px auto;
    }

    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;
    overflow: auto;

    .chat-box-table {
      padding: 10px 20px !important;
      border-radius: 8px;
      background-color: var(--scp-bg-color);
      border: 1px solid #efefef;
    }

    .el-card {
      --el-card-border-radius: 8px !important;
      --el-border-radius-base: 4px !important;
    }

    .el-card__header {
      padding: 8px 16px !important;

      .card-header {
        font-weight: bold;
        font-size: 0.55rem;
      }
    }

    .el-card__footer {
      padding: 8px 16px !important;
      text-align: right;
    }

    .align-right {
      .github-theme table tr {
        background-color: transparent !important;
      }

      padding-right: 8px;
      padding-left: 8px;

      .md-editor-previewOnly {
        float: right;
        border-radius: 8px !important;
        background-color: #2D65F7 !important;

        * {
          color: #fff !important;
        }

        svg, .md-editor-code {
          * {
            color: #333 !important;
          }
        }
      }
    }

    .align-left {
      padding-right: 8px;
      padding-left: 8px;

      .md-editor-previewOnly {
        border-radius: 8px !important;
        float: left;
        border: 1px solid #efefef;
      }
    }

    .md-editor-previewOnly {
      display: inline-block !important;
      width: auto !important;

      pre {
        margin-bottom: 5px !important;
      }

      .md-editor-preview {
        padding: 10px 20px !important;
      }
    }

    .md-editor-preview {
      blockquote {
        border-left: 1.5px solid var(--scp-border-color) !important;
        color: #818080 !important;
        margin-left: 0 !important;
        margin-bottom: 0 !important;
      }

      blockquote + p {
        margin-top: 16px;
      }

      p:last-child {
        margin-bottom: 2px !important;
      }

      img {
        max-width: 200px;
        display: block;
        margin-bottom: var(--scp-widget-margin);
      }
    }
  }
}

</style>
