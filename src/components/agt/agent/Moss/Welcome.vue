<template>
  <div id="MOSSWelcome" :style="{height: height + 'px'}">
    <div class="chat-title">
      <!-- region: 右边栏, 打开关闭侧边栏 -->
      <span style="cursor: pointer" @click="clickShowSidebar(true)" :style="{color:isSidebarBtnShown? 'transparent':'inherit'}">
          <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" data-v-f2dda92e="" style="" width="20"
               height="20" viewBox="0 0 1024 1024" class="iconify"><path
            d="M861.866667 162.133333c-17.066667-17.066667-42.666667-29.866667-68.266667-29.866666H226.133333c-25.6 0-51.2 8.533333-68.266666 29.866666S128 204.8 128 230.4v567.466667c0 25.6 8.533333 51.2 29.866667 68.266666 17.066667 17.066667 42.666667 29.866667 68.266666 29.866667h567.466667c25.6 0 51.2-8.533333 68.266667-29.866667 17.066667-17.066667 29.866667-42.666667 29.866666-68.266666V226.133333c0-25.6-8.533333-46.933333-29.866666-64zM366.933333 814.933333H226.133333c-4.266667 0-8.533333 0-12.8-4.266666-4.266667-4.266667-4.266667-8.533333-4.266666-12.8V226.133333c0-4.266667 0-8.533333 4.266666-12.8 4.266667-4.266667 8.533333-4.266667 12.8-4.266666h140.8v605.866666z m448-17.066666c0 4.266667 0 8.533333-4.266666 12.8-4.266667 4.266667-8.533333 4.266667-12.8 4.266666h-354.133334V209.066667h354.133334c4.266667 0 8.533333 0 12.8 4.266666 4.266667 4.266667 4.266667 8.533333 4.266666 12.8v571.733334z"
            fill="currentColor"></path></svg>
          &nbsp;&nbsp;
        </span>
      <!-- endregion -->
    </div>
    <!--  主功能区域 -->
    <div class="chat-container">
      <div class="chat-logo">
        <img src="/img/moss.svg" alt="MOSS" width="40%" style="max-width: 318px;min-width: 218px;"/>
      </div>
      <div class="query-box">
        <el-row>
          <el-col :span="24" style="border-bottom: 1px dotted #E5E5E5;padding-bottom: 3px;padding-top: 8px;" v-if="pageCtl.queryImages.length > 0">
            <el-badge value="x" class="item" v-for="(item, index) in pageCtl.queryImages" :key="item" style=" margin-right:18px;">
              <template #content>
                <font-awesome-icon icon="times" style="cursor: pointer" title="delete this image" @click="deleteImage(index)" />
              </template>
              <el-image
                style="width: 100px; height: 75px;border: 1px solid var(--scp-border-color-lighter)"
                :hide-on-click-modal="true"
                :src="item"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="[item]"
                show-progress
                :initial-index="4"
                fit="cover" />
            </el-badge>
          </el-col>
          <el-col :span="24" style="height: 100px">
            <el-input
              @paste="pasteTextarea"
              :disabled="pageCtl.loading.search"
              @keydown.enter.stop="quickSearch"
              @keydown.shift.enter.stop="insertEnter"
              v-model="pageCtl.query"
              style="width: 100%;"
              :rows="5"
              :max-length="6000"
              resize="none"
              type="textarea" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20" class="query-box-tips">
            &nbsp;
          </el-col>
          <el-col :span="4" class="query-box-tips">
            <font-awesome-icon icon="paper-plane" class="send-btn" @click="quickSearch"/>
          </el-col>
        </el-row>
      </div>

      <el-carousel
        v-if="pageCtl.suggestQuestions.length > 0"
        height="60px"
        style="width: 50%;margin: 20px auto;"
        direction="vertical"
        type="card"
        :interval=10000
        :autoplay="true">
        <el-carousel-item @dblclick="()=>selectQuickAccess(item)"
                          v-for="item in pageCtl.suggestQuestions" :key="item">
          <span>{{ item }}</span>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, nextTick, onMounted, reactive } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const $axios: any = inject('$axios')

const pageCtl = reactive({
  query: '',
  queryImages: [],
  loading: {
    search: false
  },
  suggestQuestions: []
})

const props = withDefaults(
  defineProps<{
    clickShowSidebar?: Function,
    clickViewChat?: Function,
    isSidebarBtnShown?: boolean,
    startQuery?: Function,
    height?: number
  }>(), {
    clickShowSidebar: undefined,
    clickViewChat: undefined,
    isSidebarBtnShown: false,
    startQuery: undefined,
    height: 300
  }
)

const deleteImage = (index) => {
  pageCtl.queryImages.splice(index, 1)
}

const selectQuickAccess = (question) => {
  pageCtl.query = question
  // 双击只做同步, 不做查询
  // props.startQuery({
  //   query: pageCtl.query,
  //   queryImages: pageCtl.queryImages
  // })
  // pageCtl.query = ''
  // pageCtl.queryImages = []
}

const quickSearch = (event) => {
  event.preventDefault()
  if (event.shiftKey === false && pageCtl.query.trim()) {
    props.startQuery({
      query: pageCtl.query,
      queryImages: pageCtl.queryImages
    })
    pageCtl.query = ''
    pageCtl.queryImages = []
  }
}

const insertEnter = (event) => {
  event.preventDefault()
  if (event.shiftKey && pageCtl.query.trim()) {
    const textarea = event.target
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    // 在光标位置插入换行符
    textarea.value = textarea.value.substring(0, start) + '\n' + textarea.value.substring(end)
    // 移动光标到换行符后面
    textarea.selectionStart = textarea.selectionEnd = start + 1
    nextTick(() => {
      const queryTextArea = document.getElementsByClassName('query-box')[0].getElementsByClassName('el-textarea__inner')[0] as HTMLPreElement
      queryTextArea.scrollTo({ top: queryTextArea.scrollHeight, behavior: 'smooth' })
    })
  }
}

const pasteTextarea = (e) => {
  const items = e.clipboardData?.items
  if (!items) return

  // 检查是否有文件
  const files = []
  for (const item of items) {
    if (item.type.startsWith('text/rtf')) {
      return
    }
    if (item.kind === 'file' && item.type.startsWith('image/')) {
      files.push(item.getAsFile())
      break
    }
  }

  if (files.length > 0) {
    // 阻止默认粘贴行为
    e.preventDefault()

    const data = new FormData()
    data.append('file', files[0])
    data.append('saveType', 'temp')

    $axios({
      method: 'post',
      url: '/system/save_image_to_local',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then((body) => {
      pageCtl.queryImages.push(body)
    }).catch((error) => {
      console.log(error)
    })
  }
}

onMounted(() => {
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/query_suggest_questions'
  }).then((body) => {
    pageCtl.suggestQuestions = body
  })
})

const setQueryMsg = (item) => {
  pageCtl.query = item
}

defineExpose({
  setQueryMsg
})
</script>

<script lang="ts">
export default {
  name: 'MossWelcome'
}
</script>

<style lang="scss">
#MOSSWelcome {
  .chat-title {
    padding: 12px 0 0 5px;
  }

  .chat-container {
    box-shadow: none !important;
    display: flex;
    flex-direction: column;
  }

  .chat-logo {
    width: 60%;
    text-align: center;
    font-size: 2.8rem;
    margin: 15% auto 50px auto;
  }

  .query-box:hover {
    border: 1px solid var(--scp-border-color-highlight);
  }

  .query-box {
    width: 60%;
    max-width: 800px;
    min-width: 500px;
    margin: 0 auto;
    border-radius: 20px;
    border: 1px solid #E5E5E5;
    box-shadow: 0 5px 16px -4px rgba(0, 0, 0, .07);
    padding: 5px;
    background-color: white;

    .el-textarea__inner {
      box-shadow: none !important;
      background-color: transparent !important;
    }

    .query-box-tips {
      height: 26px;
      line-height: 26px;
      text-align: right;
      padding-right: 10px;

      .send-btn {
        font-size: 0.6rem;
        cursor: pointer;
        color: var(--scp-text-color-secondary) !important;
        :hover {
          color: var(--scp-text-color-highlight) !important;
        }
      }
    }
  }

  .el-carousel__item {
    user-select: none;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: rgba(0, 0, 0, 0.1);
    font-weight: normal;
  }

  .el-carousel__item.is-active {
    background-color: #fff;
    color: rgba(0, 0, 0, 0.5);
  }

  .el-divider__text {
    color: var(--scp-text-color-secondary);
  }
}
</style>
