<template>
  <div id="mossDebugger">
    <el-row style="width: 100%">
      <el-col :span="7">
        <div style="overflow: auto" :style="{height: pageCtl.height + 'px'}" id="mossDebuggerFlowChat">
          <svg class="dagre" :width="svgWidth" :height="svgHeight">
            <g class="container"></g>
          </svg>
        </div>
      </el-col>
      <el-col :span="17" style="overflow: auto" :style="{height: pageCtl.height + 'px'}" id="mossDebuggerDetails">
        <el-card style="width: calc(100% - 8px); margin-bottom: 8px;margin-left: 3px" v-for="item in props.nodes" :key="item.id">
          <template #header>
            <div class="card-header">
              <span :style="{fontWeight: item.type==='step'?'bold':'normal'}">
                <font-awesome-icon icon="caret-right" v-show="item.type!=='step'"/>
                {{item.name}}
              </span>
            </div>
          </template>
          <scp-md-preview v-model="item.tooltip" style="border-radius: 8px;" :noKatex="true" :auto-fold-threshold="50" />
        </el-card>
      </el-col>
    </el-row>

    <scp-draggable-resizable w="1024" h="600" :title="pageCtl.tooltipTitle" v-model="pageCtl.viewDebugger">
        <div style="padding: var(--scp-widget-margin)">
          <scp-md-preview v-model="pageCtl.tooltip" :noKatex="true" :auto-fold-threshold="50" />
        </div>
      </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import * as d3 from 'd3'
import * as dagreD3 from 'dagre-d3'
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'
import Node from '@/components/agt/agent/Moss.vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const svgWidth = ref(280)
const svgHeight = ref(100)

const $randomString: any = inject('$randomString')
let render = {} as any
let svg = {} as any
let inner = {} as any
let g = {} as any

const props = withDefaults(defineProps<{
  nodes: Array<Node>,
  edges: Array<any>
}>(), {
  nodes: () => {
    return []
  },
  edges: () => {
    return []
  }
})

const pageCtl = reactive({
  tooltip: '',
  tooltipTitle: '',
  stepColor: '#3490dc',
  subStepColor: '#ccc',
  height: 800,
  rank: 1,
  viewDebugger: false
})

onMounted(() => {
  // eslint-disable-next-line new-cap
  render = new dagreD3.render()
  svg = d3.select('svg.dagre')
  inner = svg.select('g.container')
  g = new dagreD3.graphlib.Graph()
    .setGraph({ rankdir: 'TB', nodesep: 50, edgesep: 50, ranksep: 50 })
    .setDefaultEdgeLabel(() => ({}))

  setTimeout(() => {
    drawFlowChat()
  }, 200)
})

const drawFlowChat = () => {
  drawGraph()
  scrollChatBox()
  svgHeight.value = 70 * props.nodes.length + 200
}

watch(() => props.nodes, (val) => {
  drawFlowChat()
}, {
  deep: true
})

const drawGraph = () => {
  // Clear existing nodes and edges
  g.nodes().forEach((node) => g.removeNode(node))
  g.edges().forEach((edge) => g.removeEdge(edge))

  // Add nodes
  props.nodes.forEach((node) => {
    const strokeWidth = node.type === 'step' ? '2px' : '1px'
    const fontWeight = node.type === 'step' ? 'bold' : 'normal'
    const padding = node.type === 'step' ? 8 : 3
    const fillColor = node.type === 'step' ? pageCtl.stepColor : pageCtl.subStepColor
    const radius = node.type === 'step' ? 10 : 2

    g.setNode(node.id, {
      label: node.name,
      shape: 'rect',
      style: 'fill: #ffffff; stroke: ' + fillColor + '; stroke-width: ' + strokeWidth + ';',
      labelStyle: 'fill: ' + fillColor + '; font-size: 10px; font-weight: ' + fontWeight + ';',
      rx: radius,
      ry: radius,
      padding,
      rank: pageCtl.rank
    })
    if (node.type === 'step') {
      pageCtl.rank++
    }
  })

  // Add edges
  props.edges.forEach((edge) => {
    const mainRoute = _nodeMap.value[edge.start] === 'step' && _nodeMap.value[edge.end] === 'step'
    const strokeWidth = mainRoute ? '2px' : '1px'
    const fillColor = mainRoute ? pageCtl.stepColor : pageCtl.subStepColor

    g.setEdge(edge.start, edge.end, {
      style: 'stroke: ' + fillColor + '; fill: none; stroke-width: ' + strokeWidth + ';',
      arrowheadStyle: 'fill: ' + fillColor + '; stroke: ' + fillColor + ';',
      arrowhead: 'normal'
    })
  })

  // Render the graph
  inner.selectAll('*').remove()
  render(inner, g)

  // Set up tooltip
  inner.selectAll('g.node').on('click', (event, d) => {
    event.stopPropagation()
    const node = props.nodes.find((n) => n.id === d)
    if (node) {
      pageCtl.tooltipTitle = node.name
      pageCtl.tooltip = node.tooltip
      pageCtl.viewDebugger = true
    }
  })
}

const scrollChatBox = () => {
  const mossDebuggerFlowChat = document.getElementById('mossDebuggerFlowChat') as HTMLPreElement
  mossDebuggerFlowChat.scrollTo({ top: mossDebuggerFlowChat.scrollHeight, behavior: 'smooth' })

  const mossDebuggerDetails = document.getElementById('mossDebuggerDetails') as HTMLPreElement
  mossDebuggerDetails.scrollTo({ top: mossDebuggerDetails.scrollHeight, behavior: 'smooth' })
  pageCtl.height = window.innerHeight - 50
}

const _nodeMap = computed(() => {
  const nodeMap = {}
  for (const i in props.nodes) {
    const node = props.nodes[i]
    nodeMap[node.id] = node.type
  }
  return nodeMap
})

defineExpose({
  drawFlowChat
})
</script>

<script lang="ts">
export default {
  name: 'MossDebugger'
}
</script>

<style lang="scss">
#mossDebugger {
  position: relative;
  padding: 12px;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;

  g.node {
    cursor: pointer;
  }
}

#mossDebuggerDetails {
  .el-card {
    --el-card-border-radius: 8px !important;
  }
  .el-card__header {
    padding: 5px 12px 5px 12px !important;
    background-color: var(--scp-bg-color-highlight) !important;
    color: #fff;
  }
  .el-card__body {
    padding: 0 12px !important;

    .md-editor-preview {
      p:first-child {
        margin-top: 8px;
      }
    }
  }
}
</style>
