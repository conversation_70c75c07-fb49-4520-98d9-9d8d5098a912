<template>
  <div id="MOSSAgents" :style="{height: height + 'px'}">
    <div class="chat-title">
      <!-- region: 右边栏, 打开关闭侧边栏 -->
      <span style="cursor: pointer" @click="clickShowSidebar(true)" :style="{color:isSidebarBtnShown? 'transparent':'inherit'}">
          <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" data-v-f2dda92e="" style="" width="20"
               height="20" viewBox="0 0 1024 1024" class="iconify"><path
            d="M861.866667 162.133333c-17.066667-17.066667-42.666667-29.866667-68.266667-29.866666H226.133333c-25.6 0-51.2 8.533333-68.266666 29.866666S128 204.8 128 230.4v567.466667c0 25.6 8.533333 51.2 29.866667 68.266666 17.066667 17.066667 42.666667 29.866667 68.266666 29.866667h567.466667c25.6 0 51.2-8.533333 68.266667-29.866667 17.066667-17.066667 29.866667-42.666667 29.866666-68.266666V226.133333c0-25.6-8.533333-46.933333-29.866666-64zM366.933333 814.933333H226.133333c-4.266667 0-8.533333 0-12.8-4.266666-4.266667-4.266667-4.266667-8.533333-4.266666-12.8V226.133333c0-4.266667 0-8.533333 4.266666-12.8 4.266667-4.266667 8.533333-4.266667 12.8-4.266666h140.8v605.866666z m448-17.066666c0 4.266667 0 8.533333-4.266666 12.8-4.266667 4.266667-8.533333 4.266667-12.8 4.266666h-354.133334V209.066667h354.133334c4.266667 0 8.533333 0 12.8 4.266666 4.266667 4.266667 4.266667 8.533333 4.266666 12.8v571.733334z"
            fill="currentColor"></path></svg>
          &nbsp;&nbsp;
        </span>
      <b>Explore MOSS+</b>
      <!-- endregion -->
    </div>
    <!--  主功能区域 -->
    <div class="chat-container">
      <div v-for="(item, group) in pageCtl.availableAgents" :key="group" style="width: calc(100% - 200px);margin: 0 auto">
        <el-divider content-position="left" class="agent-group-title">{{ group }}</el-divider>
        <div class="agent-group">
          <el-card shadow="hover" v-for="item2 in item" :key="item2.AGENT_ID">
            <span class="agent-subject" title="单击访问智能体" @click="()=>clickSelectAgent(item2)">{{ item2.SUBJECT }} &nbsp;&nbsp;&nbsp;</span>
            <font-awesome-icon title="复制智能体访问链接" @click="()=>copyAgentLink(item2)" class="agent-share" icon="share-alt" />
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $copyText: any = inject('$copyText')

const pageCtl = reactive({
  availableAgents: {},
  loading: {
    search: false
  }
})

const props = withDefaults(
  defineProps<{
    clickShowSidebar?: Function,
    clickSelectAgent?: Function,
    isSidebarBtnShown?: boolean,
    height?: number
  }>(), {
    clickShowSidebar: undefined,
    clickSelectAgent: undefined,
    isSidebarBtnShown: false,
    height: 300
  }
)

const search = () => {
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/query_all_agents'
  }).then((body) => {
    pageCtl.availableAgents = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.search = false
  })
}

const copyAgentLink = (item) => {
  let url = window.location.href.split('#')[0]
  url += '#/agent/intelligent_agent/moss?aid=' + item.AGENT_ID
  $copyText(url)
  $message.success('分享链接已复制到剪切板')
}

onMounted(() => {
  search()
})

</script>

<script lang="ts">
export default {
  name: 'MossAgents'
}
</script>

<style lang="scss">
#MOSSAgents {
  width: 100%;
  display: flex;
  flex-direction: column;

  .chat-title {
    font-size: 0.6rem;
    padding: 12px 5px 0 5px;
    display: flex;
    align-items: flex-start;
    height: 26px;
  }

  .chat-container {
    display: flex;
    box-shadow: none !important;
    flex-direction: column;
    align-items: center;
    width: calc(100% - 5px);
    margin: 10px auto;

    .agent-group-title {
      .el-divider__text {
        font-weight: bold;
      }

      --el-border-color: var(--scp-border-color-lighter);
    }

    .agent-group {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      flex-direction: row;
      width: calc(100% - 200px);

      .el-card {
        border-radius: 6px;
        margin: 6px;

        --el-card-padding: 12px;

        .el-card__body {
          user-select: none;

          .agent-subject:hover {
            cursor: pointer;
            color: var(--scp-text-color-highlight);
          }

          .agent-share {
            color: rgba(0, 0, 0, 0.15);
          }

          .agent-share:hover {
            cursor: pointer;
            color: var(--scp-text-color-highlight);
          }
        }
      }
    }
  }
}
</style>
