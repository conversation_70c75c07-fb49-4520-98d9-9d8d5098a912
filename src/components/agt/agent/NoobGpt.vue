<template>
  <div class="left-sidebar" style="width: 100%;display: flex" id="noob" :style="{height: pageCtl.layout.height + 'px'}">
    <!--  主功能区域 -->
    <div :style="{width : pageCtl.layout.documentWidth}" style="display: flex;flex-direction: column; align-items: center;">
      <div class="noob-header">
        <div style="margin-bottom: 16px">&nbsp;</div>
      </div>
      <div class="chat-box" :style="{height: (pageCtl.layout.height - 170) + 'px'}"
           style="width: 100%; display: flex;flex-direction: column; align-items: center; overflow:auto;margin-bottom: 8px;">
        <el-row v-for="item in pageCtl.chatLogs" :key="item.key" style="margin-bottom: 16px" :style="{width: _clientWidth}">
          <el-col :span="1" style="text-align: left">
            <div v-if="item.role==='noob'">
              <el-avatar :size="36" :src="pageCtl.model === '狄施施'? '/img/avatar-sblv-ai.jpg': '/img/noob.png'"/>
            </div>
          </el-col>
          <el-col :span="22" :class="item.role==='noob'?'align-left':'align-right'">
            <scp-md-preview v-model="item.content" style="border-radius: 8px;" :auto-fold-threshold="_autoFoldThreshold"/>
          </el-col>
          <el-col :span="1" style="text-align: right">
            <div v-if="item.role==='user'">
              <el-tooltip v-if="$store.state.maintainer==='Y'" :show-after="1000" effect="light" placement="bottom" content="You are one of DSS maintainers">
                <el-avatar :size="36" :src="_avatar">
                  <img src="/img/avatar-admin.png" alt="admin"/>
                </el-avatar>
              </el-tooltip>
              <el-avatar v-if="$store.state.maintainer!=='Y'" :size="36" :src="_avatar">
                <img src="/img/avatar.png" alt="avatar"/>
              </el-avatar>
            </div>
          </el-col>
        </el-row>
        <el-row v-if="pageCtl.loading.search" :style="{width: _clientWidth}">
          <el-col :span="1" style="text-align: left">
            <div>
              <el-avatar :size="36" :src="pageCtl.model === '狄施施'? '/img/avatar-sblv-ai.jpg': '/img/noob.png'"/>
            </div>
          </el-col>
          <el-col :span="22" style="padding-left: 8px">
            <div
                style="height: 28px;text-align: left;background-color: #fff;border-radius: 8px;padding: 5px 12px;width: 64px;font-size: 0.65rem;font-weight: bold">
              {{ pageCtl.loading.text }}
            </div>
          </el-col>
          <el-col :span="1">&nbsp;</el-col>
        </el-row>
      </div>
      <div class="question-box" :style="{width: _clientWidth}"
           style="border-radius: 8px;border: 1px solid #E5E5E5;padding: 5px;background-color: white;margin-bottom: 8px">
        <el-row>
          <el-col :span="24" style="height: 125px">
            <el-input
                @keydown.enter.stop="quickSearch"
                @keydown.shift.enter.stop="insertEnter"
                v-model="pageCtl.question"
                style="width: 100%;"
                :rows="6"
                resize="none"
                type="textarea"
                placeholder=""/>
          </el-col>
          <el-col :span="12" style="text-align: left;color: var(--scp-text-color-lighter);font-style: italic;padding-left: 5px;font-size: 0.45rem"
                  class="question-box-tips">
            ENTER快速发送消息，SHIFT + ENTER 换行 &nbsp;@
            <el-select v-model="pageCtl.model" size="small" placeholder="Select a model" style="width: 160px">
              <el-option
                  v-for="item in ['狄施施', 'default']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="12" style="text-align: right;padding-right: 5px">
            <el-popover
                v-if="pageCtl.model === 'default'"
                ref="quickAccessPopoverRef"
                placement="top-end"
                title="Quick Access"
                trigger="click">
              <template #reference>
                <el-button round>
                  <font-awesome-icon icon="info-circle"/>
                </el-button>
              </template>
              <scp-tree-menu
                  ref="treeRef"
                  url="/intelligent_agent/noob_gpt/query_scenario_list"
                  :default-expanded-keys="['Sales Order']"
                  :node-dblclick="clickDblNode"
              ></scp-tree-menu>
            </el-popover>

            <el-button round @click="confirmResult" v-show="_showConfirmBtn" type="primary">
              <font-awesome-icon icon="check"/> &nbsp;
              我已确认
            </el-button>
            <el-button round @click="showDebug" v-show="pageCtl.model === 'default'">
              <font-awesome-icon icon="bug"/>
            </el-button>
            <el-button round :disabled="pageCtl.question.trim()===''" @click="search">
              <font-awesome-icon icon="paper-plane"/>
            </el-button>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- 调试信息 -->
    <div :style="{width : pageCtl.layout.debugWidth, position: 'relative'}" style="border-left: 1px solid var(--scp-border-color);overflow: hidden">
      <pre id="debugLogs" :style="{height: (pageCtl.layout.height - 10) + 'px'}" style="overflow: auto;border: none;padding: 5px">{{
          pageCtl.debugLogs.join('')
        }}</pre>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { useStore } from 'vuex'
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'

let client: any = {} // mqtt client
const $store = useStore()
const $axios: any = inject('$axios')
const $randomString: any = inject('$randomString')
const $dateFormatter: any = inject('$dateFormatter')
const $createMqttClient: any = inject('$createMqttClient')

const quickAccessPopoverRef = ref()

const pageCtl = reactive({
  subscribeTopic: 'scp/dss/ui/noob-gpt/' + localStorage.getItem('username')?.toLowerCase(),
  loadingTimer: {} as any,
  isAdmin: true,
  question: '',
  modelStorageKey: 'noob-gpt.model',
  model: 'default',
  loading: {
    search: false,
    text: '.'
  },
  visible: {
    debugLogs: false
  },
  chatLogs: [],
  debugLogs: [],
  sblvLogs: [],
  toBeConfirm: false,
  layout: {
    height: 0,
    documentWidth: '100%',
    debugWidth: '0%'
  }
})

const quickSearch = (event) => {
  event.preventDefault()
  if (event.shiftKey === false && pageCtl.question.trim()) {
    search()
  }
}

const insertEnter = (event) => {
  event.preventDefault()
  if (event.shiftKey && pageCtl.question.trim()) {
    const textarea = event.target
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    // 在光标位置插入换行符
    textarea.value = textarea.value.substring(0, start) + '\n' + textarea.value.substring(end)
    // 移动光标到换行符后面
    textarea.selectionStart = textarea.selectionEnd = start + 1
    nextTick(() => {
      const questionTextArea = document.getElementsByClassName('question-box')[0].getElementsByClassName('el-textarea__inner')[0] as HTMLPreElement
      questionTextArea.scrollTo({ top: questionTextArea.scrollHeight, behavior: 'smooth' })
    })
  }
}

const search = () => {
  pageCtl.loading.search = true
  pageCtl.chatLogs.push({
    key: $randomString(8),
    role: 'user',
    content: pageCtl.question
  })
  scrollChatBox()
  const model = pageCtl.model || 'default'

  let question = pageCtl.question
  pageCtl.question = ''
  if (model === '狄施施') {
    pageCtl.sblvLogs.push({
      role: 'user',
      timestamp: $dateFormatter(new Date(), 'yyyy/MM/dd hh:mm:ss'),
      content: question
    })
    question = JSON.stringify(pageCtl.sblvLogs)
  }

  $axios({
    method: 'post',
    url: '/intelligent_agent/noob_gpt/search',
    data: {
      question, model
    }
  }).then((body) => {
    if (model === '狄施施') {
      // 说明对话还没有完成
      if (body.status === 300) {
        pageCtl.sblvLogs.push({
          role: 'assistant',
          timestamp: $dateFormatter(new Date(), 'yyyy/MM/dd hh:mm:ss'),
          content: body.message
        })
        pageCtl.toBeConfirm = true
      } else {
        pageCtl.sblvLogs = []
        pageCtl.toBeConfirm = false
      }
    }
    pageCtl.chatLogs.push({
      key: $randomString(8),
      role: 'noob',
      content: body.message
    })
    scrollChatBox()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.search = false
  })
}

const scrollChatBox = () => {
  nextTick(() => {
    const chatBox = document.getElementsByClassName('chat-box')[0] as HTMLPreElement
    chatBox.scrollTo({ top: chatBox.scrollHeight, behavior: 'smooth' })
  })
}

const showDebug = () => {
  if (pageCtl.layout.documentWidth === '100%') {
    pageCtl.layout.documentWidth = '70%'
    pageCtl.layout.debugWidth = '30%'
  } else {
    pageCtl.layout.documentWidth = '100%'
    pageCtl.layout.debugWidth = '0%'
  }
}

const confirmResult = () => {
  pageCtl.toBeConfirm = false
  pageCtl.question = '我已确认'
  search()
}

watch(() => pageCtl.loading.search, (newVal) => {
  clearInterval(pageCtl.loadingTimer)
  pageCtl.loading.text = '.'
  if (newVal) {
    pageCtl.loadingTimer = setInterval(() => {
      switch (pageCtl.loading.text.length) {
        case 1:
          pageCtl.loading.text = '..'
          break
        case 2:
          pageCtl.loading.text = '...'
          break
        case 3:
          pageCtl.loading.text = '....'
          break
        case 4:
          pageCtl.loading.text = '.'
          break
      }
    }, 1200)
  }
})

watch(() => pageCtl.model, (newVal) => {
  localStorage.setItem(pageCtl.modelStorageKey, newVal)

  if (newVal === '狄施施') {
    pageCtl.chatLogs = [{
      key: $randomString(8),
      role: 'noob',
      content: '🤗您好，我是智能计划员狄施施，很高兴为您服务。 \r\n' +
          '我可以及时处理您订单相关的需求，请告诉我您要咨询的内容。 \r\n' +
          '#### 精选问题：\r\n ' +
          '- 订单 <b>加急</b> 咨询：您想加急哪些订单？请提供订单行SO，及订单行号Item。 \r\n ' +
          '- 订单 <b>取消</b> 咨询：您想取消哪些订单？请提供订单行SO，及订单行号Item。'
    }]
  } else {
    pageCtl.chatLogs = []
  }
})

const _clientWidth = computed(() => {
  const width = Math.min(document.documentElement.clientWidth * 0.88, 1366)
  if (pageCtl.layout.documentWidth === '100%') {
    return width + 'px'
  } else {
    return (width * 0.7) + 'px'
  }
})

const _avatar = computed(() => {
  return 'https://scp-dss.cn.schneider-electric.com/avatar/' + localStorage.getItem('username') + '.jpg'
})

const _autoFoldThreshold = computed(() => {
  if (pageCtl.model === '狄施施') {
    return 1
  } else {
    return 1024
  }
})

const _showConfirmBtn = computed(() => {
  return pageCtl.model === '狄施施' && pageCtl.toBeConfirm === true
})

onMounted(() => {
  if (localStorage.getItem(pageCtl.modelStorageKey)) {
    pageCtl.model = localStorage.getItem(pageCtl.modelStorageKey) + ''
  }
  pageCtl.layout.height = document.documentElement.clientHeight - 60
  window.onresize = () => {
    pageCtl.layout.height = document.documentElement.clientHeight - 60
  }
  connectMqtt()
})

const closeMqttClient = () => {
  try {
    client.end()
  } catch (e) {

  }
}

onBeforeUnmount(() => {
  closeMqttClient()
})

window.onbeforeunload = () => {
  closeMqttClient()
}

const connectMqtt = () => {
  client = $createMqttClient('scp-ui-noob-sync')
  client.on('connect', e => {
    client.subscribe(pageCtl.subscribeTopic, { qos: 2 }, (err) => {
      if (!err) {
        console.log('subscribe topic: ' + pageCtl.subscribeTopic)
        pageCtl.debugLogs.push('Server connected, subscribe topic: ' + pageCtl.subscribeTopic + '\r\n\r\n')
      }
    })
  })

  client.on('message', (topic, message) => {
    if (pageCtl.debugLogs.length > 256) {
      pageCtl.debugLogs.shift()
    }
    const messageObj = JSON.parse('' + message)
    const msgArray = []
    msgArray.push('------ ' + messageObj.time + ' ------')
    msgArray.push(messageObj.message)
    msgArray.push('\r\n')
    pageCtl.debugLogs.push(msgArray.join('\r\n'))

    nextTick(() => {
      const debugLogs = document.getElementById('debugLogs') as HTMLPreElement
      debugLogs.scrollTo({ top: debugLogs.scrollHeight, behavior: 'smooth' })
    })
  })
}

const clickDblNode = (e) => {
  quickAccessPopoverRef.value.hide()
  console.log(e)
  pageCtl.question = e.dataValue || e.label
}
</script>
<style lang="scss">
#noob {
  background-color: #F3F5FA;

  .noob-header {
    text-align: center;
  }

  .question-box {
    .el-textarea__inner {
      box-shadow: none !important;
    }

    .question-box-tips {
      .el-select__wrapper {
        box-shadow: none !important;
      }

      .el-select--small .el-select__wrapper {
        line-height: 18px !important;
        min-height: 1rem !important;
        padding: 0 !important;
      }

      .el-select .el-input__inner {
        color: var(--scp-text-color-lighter);
        font-style: italic;
        font-size: 0.45rem
      }

      .el-select__selected-item {
        color: var(--scp-text-color-lighter);
      }

      .el-select .el-input .el-select__caret {
        color: transparent !important;
      }
    }
  }

  .chat-box {
    .align-right {
      .github-theme table tr {
        background-color: transparent !important;
      }

      padding-right: 8px;
      padding-left: 8px;

      .md-editor-previewOnly {
        float: right;
        border-radius: 8px !important;
        background-color: #2D65F7 !important;

        * {
          color: #fff !important;
        }
      }
    }

    .align-left {
      padding-right: 8px;
      padding-left: 8px;

      .md-editor-previewOnly {
        float: left;
      }
    }

    .md-editor-previewOnly {
      display: inline-block !important;
      width: auto !important;
    }

    .md-editor-preview {
      padding: 10px 20px !important;

      p:last-child {
        margin-bottom: 2px !important;
      }
    }
  }
}
</style>
