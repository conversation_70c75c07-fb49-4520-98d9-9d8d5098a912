<template>
  <div class="filter-cascader-box">
    <el-cascader
        style="width: calc(100% - 1.2rem)"
        v-model="pageCtl.filterList"
        @change="pushFilterConfigToParent"
        :options="pageCtl.filterOpts"
        :placeholder="pageCtl.loading.cascader ? 'Loading...' : 'Filters'"
        :props="{ multiple: true }"
        :collapse-tags="true"
        :filter-method="cascaderFilter"
        clearable
        filterable
        collapse-tags-tooltip
        class="scp-cascader"/>
    <div class="filter-cascader-icon" @click="showMoreFilters">
      +{{ _filterChainLength }}
    </div>
  </div>

  <scp-draggable-resizable w="1024px" h="520px" v-model="pageCtl.visible" title="More Filters" :save="applyFilters" save-text="Apply"
                           :before-close="fetchFilterConfigFromParent">
    <div class="filter-cascader-win">
      <el-row style="height: 100%;">
        <el-col :span="12" class="left-slide">
          <el-row style="margin-bottom: 8px">
            <el-col :span="18">
              <!-- 筛选字段 -->
              <el-select ref="fieldRef" v-model="pageCtl.prepare.fields" placeholder="Filter Field" style="width: calc(100% - 15px)" default-first-option
                         @change="clearDropdown"
                         filterable collapse-tags clearable multiple collapse-tags-tooltip :reserve-keyword="false" :multiple-limit="3">
                <el-option
                    v-for="item in pageCtl.fieldOpts"
                    :key="item.name"
                    :label="item.name"
                    :value="item.name">
                  <span style="float: left">{{ item.name }}</span>
                  <span style="float: right;color: var(--scp-text-color-lighter);font-size: 0.4rem;">{{ item.type }}</span>
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="6">
              <!-- 操作符 -->
              <el-select v-model="pageCtl.prepare.operator" placeholder="Operator" style="width: calc(100% - 15px)">
                <el-option
                    v-for="item in _operatorOpts"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-col>
          </el-row>

          <el-row style="margin-bottom: 8px" v-if="pageCtl.prepare.operator.indexOf('NULL') === -1">
            <el-col :span="24">
              <el-input v-model="pageCtl.prepare.text" v-if="pageCtl.prepare.operator==='IN' || pageCtl.prepare.operator==='NOT IN'" type="textarea"
                        :placeholder="pageCtl.valuePlaceHolder" @keydown="preventTextareaTab"
                        style="width: calc(100% - 15px);height: auto !important;" :rows="16"/>
              <el-select v-model="pageCtl.prepare.value" v-if="pageCtl.prepare.operator!=='IN' && pageCtl.prepare.operator!=='NOT IN'" ref="valueRef"
                         :remote-method="(keyword)=>changeSearch(keyword)" filterable clearable remote :loading="pageCtl.loading.value"
                         :remote-show-suffix="true" :allow-create="_fieldsTypes[0] === 'NUMBER'"
                         @focus="()=>focusSearch(null)" multiple :multiple-limit="800" collapse-tags
                         :placeholder="pageCtl.loading.value? 'Loading...' : pageCtl.valuePlaceHolder"
                         style="width: calc(100% - 15px);height: auto !important;">
                <el-option
                    v-for="item in pageCtl.valueOpts"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
              <div class="clearfix"/>
            </el-col>
          </el-row>

          <el-row style="padding-top: 8px">
            <el-col :span="24" style="text-align: right;padding-right: 15px">
              <el-button @click="addToFilter">
                <font-awesome-icon icon="search-plus"/>
              </el-button>
            </el-col>
          </el-row>

        </el-col>
        <el-col :span="12" class="right-slide">
          <div class="filter-cascader-card-box" :key="'parent_' + index" v-for="(item, index) in pageCtl.filterChain">
            <div class="filter-cascader-card-box-and">
              <span :class="item.joiner === 'AND'? 'and': 'or'" v-if="index > 0" @click="switchJoiner(item)">{{ item.joiner }}</span>
              <!-- 如果当前element不是OR, 但是下一个是OR, 则显示括号 -->
              <span class="bracket" v-if="item.nextOr && item.currentOr === false">(</span>
              <el-card shadow="never" class="existing-card" @dblclick="prepareModify(item, index)" :data-value="index + ''"
                draggable="true" @dragstart.stop="onFilterDragStart" @dragover.stop="onFilterDragOver" @drop.stop="onFilterDragDrop">
                <div class="existing-card-content" v-html="displayFilter(item.fields, item.operator, item.value, item.text)"/>
                <font-awesome-icon icon="times" class="existing-card-close" @click="()=>delFilter(index)"/>
              </el-card>
              <!-- 如果当前element是OR, 但是下一个不是OR, 则显示后半个括号 -->
              <span class="bracket" v-if="item.nextOr === false && item.currentOr">)</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </scp-draggable-resizable>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive, ref, watch } from 'vue'
import ScpDraggableResizable from '@/components/starter/components/DraggableResizable.vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { useRoute } from 'vue-router'

const $route = useRoute()
const $axios: any = inject('$axios')
const $deepClone: any = inject('$deepClone')
const $message: any = inject('$message')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')

const fieldRef = ref()
const valueRef = ref()

/*
 * Filter组件和父组件的数值传递并不是实时的, 而是由以下几个事件触发
 *
 * 下面几种情况会触发Filter读取父组件配置的事件
 * 1. 页面加载时
 * 2. 用户打开Filter窗口时
 * 3. 父组件内容发生变化时 (variant触发)
 * 4. 未Apply Filter, 并关闭窗口时
 *
 * 以下几种情况会触发Filter回写父组件配置的事件
 * 1. Apply Filter
 * 2. Cascader内容发生变化
 *
 * 为了避免循环依赖, 有的事件使用@change触发, 有的使用watch触发
 */

// 显示More Filter的窗口
const showMoreFilters = () => {
  fetchFilterConfigFromParent()
  pageCtl.visible = true
}

// 从父组件获取filter配置信息
const fetchFilterConfigFromParent = () => {
  pageCtl.filterChain = $deepClone(props.modelValue.filter || [])
  pageCtl.filterList = $deepClone(props.modelValue.cascader || [])
}

// 将组件的配置推送至父组件
const pushFilterConfigToParent = () => {
  emmits('update:modelValue', {
    cascader: pageCtl.filterList,
    filter: pageCtl.filterChain
  })
}

// 适用当前Filter
const applyFilters = () => {
  addToFilter(false)
  pushFilterConfigToParent()
  pageCtl.visible = false
  if (props.afterApply) {
    props.afterApply.apply(null)
  }
}

// 删除Filter中的某一个条目
const delFilter = (index) => {
  pageCtl.filterChain.splice(index, 1)
  rebuildFilterChain()
}

const prepareModify = (item, index) => {
  pageCtl.predModifyIndex = index
  nextTick(() => {
    pageCtl.prepare.fields = $deepClone(item.fields)
    nextTick(() => {
      pageCtl.prepare.operator = item.operator
      nextTick(() => {
        pageCtl.prepare.value = $deepClone(item.value)
        pageCtl.prepare.text = item.text + ''
      })
    })
  })
}

// 动态计算用户选中的Field对应的类型
const _fieldsTypes = computed(() => {
  const fields = pageCtl.prepare.fields
  const types = []
  for (let i = 0; i < fields.length; i++) {
    const element = pageCtl.fieldOpts.filter(e => e.name === fields[i]) as any
    if (element.length > 0) {
      types.push(element[0].type)
    } else {
      types.push('VARCHAR')
    }
  }
  return types
})

// 只有用户选择了VARCHAR和DATE类型的字段, 并且运算符不为IN的时候, 才可以启动联想
// 因为IN的textarea组件不支持联想功能
const changeSearch = (keywords) => {
  if (keywords) {
    focusSearch(keywords)
  }
}

const focusSearch = (keywords) => {
  if (pageCtl.prepare.fields.length === 1 && _fieldsTypes.value[0] !== 'NUMBER') {
    keywords = keywords || ''
    clearTimeout(pageCtl.timeout)
    pageCtl.loading.value = true
    pageCtl.timeout = setTimeout(() => {
      $axios({
        method: 'post',
        url: '/system/filter/query_suggestion_by_keywords',
        data: {
          keywords,
          url: $route.path,
          field: pageCtl.prepare.fields[0],
          type: _fieldsTypes.value[0],
          tables: props.filterBase
        }
      }).then((body) => {
        pageCtl.valueOpts = body || []
        // valueRef.value.dropMenuVisible = true
      }).catch((error) => {
        console.log(error)
      }).finally(() => {
        pageCtl.loading.value = false
      })
    }, 650)
  }
}

// 添加条件至准备区
const addToFilter = (enableWarning) => {
  if (enableWarning === undefined) {
    enableWarning = true
  }
  if (pageCtl.prepare.fields.length === 0) {
    if (enableWarning) {
      $message.error('Please select the field to filter') // 请选择需要筛选的字段
    }
    return
  }
  if (pageCtl.prepare.operator === '') {
    if (enableWarning) {
      $message.error('Please select the operator') // 请选择操作符
    }
    return
  }

  if (pageCtl.prepare.operator.indexOf('NULL') === -1) {
    if (pageCtl.prepare.operator.indexOf('IN') !== -1 && pageCtl.prepare.text === '') {
      if (enableWarning) {
        $message.error('Please enter the value to query') // 请输入需要查询的值
      }
      return
    } else if (pageCtl.prepare.operator.indexOf('IN') === -1 && pageCtl.prepare.value.length === 0) {
      if (enableWarning) {
        $message.error('Please enter the value to query') // 请输入需要查询的值
      }
      return
    }
  }

  const element = {
    fields: pageCtl.prepare.fields,
    types: $deepClone(_fieldsTypes.value),
    operator: pageCtl.prepare.operator,
    value: $deepClone(pageCtl.prepare.value),
    text: pageCtl.prepare.text
  }

  // 替换元素
  // 当pageCtl.predModifyIndex !== -1, 说明用户双击了修改卡片
  // pageCtl.predModifyIndex < pageCtl.filterChain.length, 判断数组是否越界, 没其他意思
  if (pageCtl.predModifyIndex !== -1 && pageCtl.predModifyIndex < pageCtl.filterChain.length) {
    pageCtl.filterChain[pageCtl.predModifyIndex].fields = element.fields
    pageCtl.filterChain[pageCtl.predModifyIndex].types = element.types
    pageCtl.filterChain[pageCtl.predModifyIndex].operator = element.operator
    pageCtl.filterChain[pageCtl.predModifyIndex].value = element.value
    pageCtl.filterChain[pageCtl.predModifyIndex].text = element.text

    pageCtl.predModifyIndex = -1
  } else {
    pageCtl.filterChain.push({
      joiner: 'AND',
      ...element
    })
  }

  rebuildFilterChain()

  pageCtl.prepare.fields = []
  pageCtl.prepare.value = []
  pageCtl.prepare.text = ''
  pageCtl.prepare.operator = ''
}

const rebuildFilterChain = () => {
  for (let i = 0; i < pageCtl.filterChain.length; i++) {
    const element = pageCtl.filterChain[i]
    element.currentOr = (element.joiner === 'OR')
    element.nextOr = false
    if (i < pageCtl.filterChain.length - 1) {
      const nextElemnt = pageCtl.filterChain[i + 1]
      element.nextOr = (nextElemnt.joiner === 'OR')
    }
  }
}

/**
 * 1. cascaderBase绑定级联选择器的表
 * 2. 如果不是表, 可以用一个URL和Param来指定一个数据源
 * 3. 也可以使用cascaderBaseOpts直接指定一个数据源
 */

const pageCtl = reactive({
  timeout: {},
  visible: false,
  loading: {
    cascader: false as boolean,
    value: false
  },
  predModifyIndex: -1,
  prepare: {
    fields: [],
    operator: '',
    value: [],
    text: ''
  },
  fieldOpts: [], // 字段的opts
  valueOpts: [], // 字段对应value的opts
  filterChain: [] as Array<any>,
  filterList: [], // 级联选择器的value
  filterOpts: [], // 级联选择器的opts
  valuePlaceHolder: 'Please select the field to filter' // 请选择需要筛选的字段
})

// @ts-ignore
const props = withDefaults(
  defineProps<{
      modelValue: any, // 双向绑定
      cascaderBase?: string, // 绑定级联选择器的表
      cascaderBaseUrl?: string, // 如果级联选择器有定制元素, 也可以传递一个URL
      cascaderBaseParams?: any, // 传递URL时, 如果有参数, 可以写在这里
      cascaderBaseOpts?: any, // 绑定级联选择器的选项
      filterBase?: Array<any>,
      loading?: boolean,
      afterApply?: Function,
      afterCascaderLoaded?: Function // 级联选择器加载时调用
    }>(),
  {
    modelValue: () => {
    },
    cascaderBase: '',
    cascaderBaseUrl: '',
    cascaderBaseParams: () => {

    },
    filterBase: () => [],
    loading: false,
    afterApply: undefined,
    afterCascaderLoaded: undefined,
    cascaderBaseOpts: () => []
  }
)

const emmits = defineEmits(['update:modelValue'])

// 以相对友好的方式在准备区显示用户已经配置好的Filter
const displayFilter = (fields, operator, value, text) => {
  const result = []
  if (fields.length === 1) {
    result.push(fields[0])
  } else {
    result.push('(' + fields.join(',') + ')')
  }
  result.push('<span class="filter-cascader-highlight-operator">' + operator + '</span>')

  if (operator === 'IN' || operator === 'NOT IN') {
    let vs = text.split('\n').filter(e => !!e)
    let suffix = ''
    if (vs.length > 3) {
      suffix = ', +' + $thousandBitSeparator(vs.length - 3)
      vs = vs.slice(0, 3)
    }
    const temp = []
    for (let i = 0; i < vs.length; i++) {
      let vs2 = vs[i].split('\t')
      while (vs2.length < fields.length) {
        vs2.push('NULL')
      }
      if (vs2.length > fields.length) {
        vs2 = vs2.slice(0, fields.length)
      }
      if (vs2.length > 1) {
        temp.push('(' + vs2.join(',') + ')')
      } else {
        temp.push(vs2[0])
      }
    }
    result.push('(' + temp.join(',') + suffix + ')')
  } else {
    let vs = value
    let suffix = ''
    if (value.length > 3) {
      suffix = ', +' + $thousandBitSeparator(value.length - 3)
      vs = value.slice(0, 3)
    }
    result.push(vs.join(', ') + suffix)
  }
  return result.join(' ')
}

// 查询外层的级联选择器
const initCascaderBase = () => {
  if (props.cascaderBase) {
    pageCtl.loading.cascader = true
    $axios({
      method: 'post',
      url: '/system/filter/query_cascader_opts',
      data: {
        table: props.cascaderBase
      }
    }).then((body) => {
      pageCtl.filterOpts = body || []
      if (props.afterCascaderLoaded) {
        props.afterCascaderLoaded(body || [])
      }
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.cascader = false
    })
  } else if (props.cascaderBaseUrl) {
    pageCtl.loading.cascader = true
    $axios({
      method: 'post',
      url: props.cascaderBaseUrl,
      data: props.cascaderBaseParams
    }).then((body) => {
      pageCtl.filterOpts = body || []
      if (props.afterCascaderLoaded) {
        props.afterCascaderLoaded(body || [])
      }
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.cascader = false
    })
  } else if (props.cascaderBaseOpts) {
    pageCtl.filterOpts = props.cascaderBaseOpts
  }
}

// 初始化More选择器, 如果用户没有指定底表, 则自动根据URL判断
const initFilterBase = () => {
  pageCtl.loading.cascader = true
  $axios({
    method: 'post',
    url: '/system/filter/query_columns_by_tablename',
    data: {
      url: $route.path,
      tables: props.filterBase
    }
  }).then((body) => {
    pageCtl.fieldOpts = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.cascader = false
  })
}

onMounted(() => {
  fetchFilterConfigFromParent()
  initCascaderBase()
  initFilterBase()
})

// 当用户选择了任意值时, 下拉框消失, 因为大部分情况下, 用户都只会用一个字段查询
// 放在watch里面执行, 会有延迟, 导致页面出现闪屏的现象
const clearDropdown = () => {
  fieldRef.value.dropMenuVisible = false
}

// 用户选择的字段不同, 操作符默认值和提示信息也不同
watch(() => pageCtl.prepare.fields, (newVal) => {
  pageCtl.prepare.operator = ''
  pageCtl.prepare.value = []
  pageCtl.prepare.text = ''
  pageCtl.valueOpts = []
  newVal = newVal || []
  if (newVal.length === 1) {
    const element = pageCtl.fieldOpts.filter(e => e.name === newVal[0]) as any
    if (element.length > 0) {
      if (element[0].type === 'VARCHAR2') {
        pageCtl.valuePlaceHolder = 'Please enter the search content' // 请输入查询内容
      } else if (element[0].type === 'NUMBER') {
        pageCtl.valuePlaceHolder = 'Please enter a valid number; otherwise, no data will be returned' // 请输入合法数字, 否则将不会返回任何数据
      } else if (element[0].type === 'DATE') {
        pageCtl.valuePlaceHolder = 'Please enter a date in YYYY/MM/DD format; otherwise, no data will be returned' // 请输入YYYY/MM/DD格式日期, 否则将不会返回任何数据
      }
    }
    pageCtl.prepare.operator = '='
  } else if (newVal.length > 1) {
    pageCtl.prepare.operator = 'IN'
    pageCtl.valuePlaceHolder = 'Please enter a set of data; use the TAB key to separate multiple columns' // 请输入一组数据, 多个数据列请使用TAB键分割
  } else {
    pageCtl.valuePlaceHolder = 'Please select the field to filter' // 请选择需要筛选的字段
  }
})

// 父组件的modelValue发生了变化, 仅更新_filterChainLength的显示
watch(() => props.modelValue, () => {
  fetchFilterConfigFromParent()
}, {
  deep: true
})

watch(() => props.loading, (newVal) => {
  pageCtl.loading.cascader = newVal
})

watch(() => props.cascaderBaseOpts, (newVal) => {
  pageCtl.filterOpts = newVal
})

// 默认情况下, 在textarea中无法使用tab, 而我们多个查询条件需要用tab分割
// 所以用这个方法来实现tab的输入
const preventTextareaTab = (e) => {
  if (e && e.key === 'Tab') {
    pageCtl.prepare.text += '\t'
    if (e.preventDefault) {
      e.preventDefault()
    } else {
      window.event.returnValue = false
    }
  }
}

const cascaderFilter = (node, val) => {
  return !!(node && node.text && node.text.toUpperCase().indexOf(val.toUpperCase()) !== -1)
}

const switchJoiner = (item) => {
  if (item.joiner === 'AND') {
    item.joiner = 'OR'
  } else if (item.joiner === 'OR') {
    item.joiner = 'AND'
  }
  rebuildFilterChain()
}

const filterDragCtl = reactive({
  dragIndex: 0
})

const onFilterDragStart = (e) => {
  filterDragCtl.dragIndex = e.target.dataset.value
}

// 覆盖虚拟列的DragOver事件, 否则无法获取@drop事件
const onFilterDragOver = (e) => {
  e.preventDefault()
}

// 松开拖拽, 将前后两个列的index进行互换, 实现拖拽功能
const onFilterDragDrop = (e) => {
  let element = e.target
  let maxLevel = 5
  while (element.dataset.value === undefined && element.parentElement && (maxLevel--) > 0) {
    element = element.parentElement
  }
  if (element && element.dataset.value !== undefined) {
    const array = pageCtl.filterChain
    const targetIdx = parseInt(element.dataset.value)
    const currentIdx = parseInt(filterDragCtl.dragIndex)

    const targetElement = array[targetIdx]
    array[targetIdx] = array[currentIdx]
    array[currentIdx] = targetElement
  }

  if (pageCtl.filterChain.length > 0) {
    pageCtl.filterChain[0].joiner = 'AND'
    rebuildFilterChain()
  }
}

// 可用操作符根据用户填写的字段变化而变化
const _operatorOpts = computed(() => {
  const selectFields = pageCtl.prepare.fields as Array<string>
  if (selectFields.length > 1) {
    return [{
      label: 'In list', // 在列表中
      value: 'IN'
    }, {
      label: 'Not in list', // 不在列表中
      value: 'NOT IN'
    }]
  } else if (selectFields.length === 1) {
    const element = pageCtl.fieldOpts.filter(e => e.name === selectFields[0]) as any
    if (element.length > 0) {
      if (element[0].type === 'VARCHAR2') {
        return [{
          label: 'Equals', // 等于
          value: '='
        }, {
          label: 'Not Equals', // 不等于
          value: '!='
        }, {
          label: 'In List', // 在列表中
          value: 'IN'
        }, {
          label: 'Not In List', // 不在列表中
          value: 'NOT IN'
        }, {
          label: 'Contains', // 包含
          value: 'LIKE'
        }, {
          label: 'Does Not Contain', // 不包含
          value: 'NOT LIKE'
        }, {
          label: 'Starts With', // 以..开始
          value: 'START WITH'
        }, {
          label: 'Is Empty', // 为空
          value: 'IS NULL'
        }, {
          label: 'Is Not Empty', // 不为空
          value: 'IS NOT NULL'
        }]
      } else if (element[0].type === 'NUMBER' || element[0].type === 'DATE') {
        return [{
          label: 'Equals', // 等于
          value: '='
        }, {
          label: 'Not Equals', // 不等于
          value: '!='
        }, {
          label: 'In List', // 在列表中
          value: 'IN'
        }, {
          label: 'Not In List', // 不在列表中
          value: 'NOT IN'
        }, {
          label: 'Greater Than', // 大于
          value: '>'
        }, {
          label: 'Greater Than or Equal To', // 大于等于
          value: '>='
        }, {
          label: 'Less Than', // 小于
          value: '<'
        }, {
          label: 'Less Than or Equal To', // 小于等于
          value: '<='
        }, {
          label: 'Is Empty', // 为空
          value: 'IS NULL'
        }, {
          label: 'Is Not Empty', // 不为空
          value: 'IS NOT NULL'
        }]
      }
    }
  }
  return []
})

// 显示在前台, 告诉用户当前已经选择了多少Filter
const _filterChainLength = computed(() => {
  let length = 0
  for (let i = 0; i < pageCtl.filterChain.length; i++) {
    const e = pageCtl.filterChain[i]
    if (e) {
      if (e.children) {
        length += e.children.length
      } else {
        length += 1
      }
    }
  }
  return length
})
</script>

<script lang="ts">
export default {
  name: 'ScpFilter'
}
</script>

<style lang="scss">

.filter-cascader-highlight-operator {
  color: #d73a49;
  font-weight: bold;
}

.filter-cascader-win {
  padding: 10px;
  height: calc(100% - 20px);
  overflow: auto;

  .left-slide {
    border-right: 1px dotted var(--scp-border-color-lighter);
    width: calc(100% - 400px);
    max-width: calc(100% - 400px);
    flex: none;
  }

  .right-slide {
    font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;
    height: 100%;
    padding: 0 var(--scp-widget-margin);
    overflow: auto;
    width: 400px;
    max-width: 400px;
    flex: none;
  }

  .filter-cascader-card-box {
    display: flex;
    align-content: center;
    justify-content: left;
    flex-wrap: wrap;
    margin-bottom: 12px;
    cursor: pointer;

    .filter-cascader-card-box-or {
      justify-content: left;
      flex-wrap: wrap;
      display: flex;

      .existing-card {
        width: 280px;
      }

      .existing-card:not(:last-child) {
        margin-bottom: 5px;
      }
    }

    .filter-cascader-card-box-and {
      justify-content: space-between;
      flex-wrap: wrap;
      display: flex;

      .existing-card {
        width: 320px;
      }
    }

    .bracket {
      user-select: none;
      font-family: Consolas, Courier New, monospace;
      font-size: 1.3rem;
      line-height: 1;
    }

    .or, .and {
      user-select: none;
      color: #d73a49;
      line-height: 34px;
      text-align: center;
      font-weight: bold;
      padding: 0 5px;
    }

    .or:hover, .and:hover {
      color: var(--scp-text-color-highlight);
    }
  }

  .existing-card {
    display: flex;

    .el-card__body {
      font-size: 0.45rem;
      padding: 5px;
      height: 24px;
      line-height: 24px;
      width: 100%;
      display: flex;
      align-items: center;

      .existing-card-content {
        width: calc(100% - 20px);
        display: inline-block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .existing-card-close {
        color: var(--scp-text-color-lighter);
        width: 10px;
        cursor: pointer;
      }

      .existing-card-close:hover {
        color: var(--scp-text-color-error);
      }
    }
  }

  .el-select-tags-wrapper.has-prefix {
    margin-left: 4px !important;
  }
}

.filter-cascader-box {
  display: flex;

  .filter-cascader-icon {
    cursor: pointer;
    color: var(--scp-text-color-secondary);
    font-size: 0.45rem;
    display: inline-block;
    text-align: center;
    box-shadow: -1px -1px 0 0 var(--el-input-border-color, var(--el-border-color)) inset,
    -1px 1px 0 0 var(--el-input-border-color, var(--el-border-color)) inset;
    height: var(--el-component-size-small);
    line-height: var(--el-component-size-small);
    width: 1.2rem;
  }

  .filter-cascader-icon:hover {
    svg {
      color: var(--scp-text-color-highlight) !important;
    }
  }
}
</style>
