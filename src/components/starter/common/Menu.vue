<template>
  <!-- click heatmap -->
  <div id="clickMap" v-show="clickData.visible">
    <div style="height: 1.85rem !important; background-color: var(--scp-bg-color)">
      <el-row class="search-box" style="padding: 10px">
        <el-col :span="5">
          <scp-cascader v-model="clickData.userField" :options="clickData.userOpts"/>
        </el-col>
        <el-col :span="5">
          <el-date-picker
              v-model="clickData.dateRange"
              type="daterange"
              format="YYYY/MM/DD"
              value-format="YYYY/MM/DD"
              unlink-panels
              range-separator="To"
              start-placeholder="Start date"
              end-placeholder="End date"/>
        </el-col>
        <el-col :span="5">
          <el-select v-model="clickData.url" placeholder="Quick Access" @change="(val) => clickMenuChange(val)" filterable>
            <el-option-group
                v-for="menu1 in _clickMenuData"
                :key="menu1['name']"
                :label="menu1['name']"
            >
              <el-option
                  v-for="menu2 in menu1.children"
                  :key="menu2['name']"
                  :label="menu2['name']"
                  :value="menu2.url"/>
            </el-option-group>
          </el-select>
        </el-col>
        <el-col :span="1">
          <el-button size="small" @click="showClick" :loading="clickData.loading">
            <font-awesome-icon icon="search"/>
          </el-button>
        </el-col>

        <el-col :offset="7" :span="1" style="text-align: right;line-height: 2">
          <div class="message-close">
            <font-awesome-icon icon="times" @click="() => {clickData.visible = false; clickData.mapVisible = false}" style="cursor: pointer"/>
          </div>
        </el-col>
      </el-row>
    </div>
    <div id="heatmap" v-show="clickData.mapVisible" :style="{height: _height + 'px', width: _width + 'px'}">
      <div id="tooltip"></div>
    </div>
  </div>

  <!-- menu -->
  <div id="SCPMenu" style="height: 100%">
    <header style="height: 1.85rem !important;">
      <div class="header">
        <!-- header logo -->
        <h2 class="logo hand" @click="$router.replace('/home/<USER>')">
          <img src="/img/logo-new.svg" alt="DSS"/>
        </h2>
        <!-- header menu area -->
        <div class="site-menu">
          <ul class="menu-primary">
            <li @click="goToMenu(menuData.homeURL)" class="hand">
              <font-awesome-icon icon="home"/>
              Home
            </li>
            <li v-for="item in menuData.menus" :key="item['name']" @mouseover="level1MenuMouseover(item['name'])"
                @mouseleave="level1MenuMouseleave">
              <font-awesome-icon :icon="item['ico']"/>
              {{ item['name'] }}
              <font-awesome-icon icon="caret-down" style="font-size: 0.5rem;"/>
            </li>
          </ul>
        </div>
        <!-- header info area -->
        <div class="header-operation">
          <ul class="header-operation-primary">
            <li v-show="$store.state.currentPage" class="page-title">
              <span v-for="(item,index) in $store.state.currentPage" :key="index">
                &nbsp;<font-awesome-icon icon="angle-right" v-show="!!item"/>&nbsp;
                <span v-show="!!item">{{ item }}</span>
              </span>
            </li>
            <li>
              <el-divider direction="vertical"/>
            </li>
            <li @click="$router.replace('/system/mail')">
              <el-tooltip class="item" effect="light" :show-after="500" content="Message">
                <el-badge :value="$store.state.mail" class="item" :hidden="$store.state.mail === 0">
                  <font-awesome-icon icon="envelope" size="lg" class="hand"/>
                </el-badge>
              </el-tooltip>
            </li>
            <li>
              <el-divider direction="vertical"/>
            </li>
            <li @click="goToMenu('https://scp-dss.cn.schneider-electric.com/#/public_documentation?id=0fd8fda6da33')">
              <el-badge :value="$store.state.changeLogs" class="item" :hidden="true">
                <el-tooltip class="item" effect="light" :show-after="500" content="User Guide">
                  <font-awesome-icon icon="book" size="lg" class="hand"/>
                </el-tooltip>
              </el-badge>
            </li>
            <li v-show="false">
              <el-divider direction="vertical"/>
            </li>
            <li v-show="false" @click="$router.replace('/system/change_log')">
              <el-tooltip class="item" effect="light" :show-after="500" content="Update Logs">
                <el-badge :value="$store.state.changeLogs" class="item" :hidden="$store.state.changeLogs === 0">
                  <font-awesome-icon icon="code-branch" size="lg" class="hand"/>
                </el-badge>
              </el-tooltip>
            </li>
            <li v-show="clickData.clickShow">
              <el-divider direction="vertical"/>
            </li>
            <li v-show="clickData.clickShow" @click="()=>{clickData.visible = true; showClick()}">
              <el-badge :value="$store.state.changeLogs" class="item" :hidden="true">
                <el-tooltip class="item" effect="light" :show-after="500" content="Click Data Heatmap">
                  <font-awesome-icon icon="computer-mouse" size="lg" class="hand"/>
                </el-tooltip>
              </el-badge>
            </li>
            <li v-show="clickData.clickShow">
              <el-divider direction="vertical"/>
            </li>
            <li v-show="clickData.clickShow" @click="()=>{broadcast.visible = true;}">
              <el-badge :value="$store.state.changeLogs" class="item" :hidden="true">
                <el-tooltip class="item" effect="light" :show-after="500" content="Create Broadcast">
                  <font-awesome-icon icon="fa-bullhorn" size="lg" class="hand"/>
                </el-tooltip>
              </el-badge>
            </li>
            <li>
              <el-divider direction="vertical"/>
            </li>
            <li style="vertical-align: middle;line-height: 1">
              <el-tooltip v-if="$store.state.maintainer==='Y'" :show-after="1000" effect="light" placement="bottom" content="You are one of DSS maintainers">
                <el-avatar :size="20" :src="_avatar">
                  <img src="/img/avatar-admin.png" alt="admin"/>
                </el-avatar>
              </el-tooltip>
              <el-avatar v-if="$store.state.maintainer!=='Y'" :size="20" :src="_avatar">
                <img src="/img/avatar.png" alt="avatar"/>
              </el-avatar>
              &nbsp;&nbsp;
            </li>
            <li>
              <el-dropdown class="hand">
                <span class="el-dropdown-link">
                  {{ menuData.username }}&nbsp;&nbsp;<font-awesome-icon icon="chevron-down"/>
                </span>
                <template v-slot:dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="goToMenu('/users/my_favourites')">My Favorites</el-dropdown-item>
                    <el-dropdown-item @click="goToMenu('/users/mail_signature')">Mail Signature</el-dropdown-item>
                    <!-- <el-dropdown-item @click="toggleTheme" v-if="$store.state.maintainer==='Y'">Switch Theme</el-dropdown-item> -->
                    <el-dropdown-item @click="logout">Logout</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </li>
            <li>
              <el-divider direction="vertical"/>
            </li>
          </ul>
        </div>
        <!-- header selogo area -->
        <div class="header-right hand" @click="$router.replace('/users/my_favourites')">
          <img src="/img/logo-green.svg" alt="GSC China SCP Decision Support System"/>
        </div>
      </div>
    </header>

    <div class="menu-body" v-show="menuData.menuShow" @mouseover="level1MenuMouseover(null)"
         @mouseleave="level1MenuMouseleave">
      <div class="fake-background"></div>
      <el-row class="menu-container">
        <!-- 弹出的下拉菜单, 左侧的目录 -->
        <el-col class="menu-column-left" :span="4">
          <ul>
            <li v-for="item in _subMenusLeft" :key="item">
              <div class="menu-title" @mouseover="level2MenuMouseover(item)">
                {{ item }}
                <font-awesome-icon icon="fire" style="color: var(--scp-text-color-error)" v-show="item === 'Hot'"/>
                <div class="menu-bottom-liner"></div>
              </div>
            </li>
          </ul>
        </el-col>
        <!-- 弹出的下拉菜单, 右侧的目录 -->
        <el-col class="menu-column-right" :span="19" style="padding-left: 0.5rem">
          <div v-show="menuData.menuSearchShow">
            <el-input class="menu-search-input" ref="menuSearchRef" v-model="menuData.menuSearch" size="default"
                      clearable
                      :style="{width: menuData.menuSearchWidth + 'px'}">
              <template v-slot:prefix>
                <font-awesome-icon icon="search"/>
              </template>
            </el-input>
          </div>
          <ul id="newMenuSubLeft">
            <li v-for="item in _subMenusRight" :key="item['name']">
              <div class="menu-item" @click="goToMenu(item['url'])">
                <font-awesome-icon class="icon" :icon="item['ico'] || 'chalkboard'"/>
                <p>{{ item['name'] }}</p>
                <el-tooltip class="item" effect="light" content="Send to Homepage" placement="top" :show-after="1500">
                  <font-awesome-icon icon="paper-plane" class="send-to-homepage"
                                     @click.stop="sendToHomepage(item['url'])"/>
                </el-tooltip>
              </div>
            </li>
          </ul>
        </el-col>
        <el-col :span="1" style="padding-left: 5px">
          <div class="menu-search" @click="showSearchInput">
            <font-awesome-icon :icon="menuData.menuSearchShow ? 'search-minus' : 'search'" class="menu-search-btn"
                               size="lg"/>
          </div>
          <div class="menu-close" @click="menuData.menuShow = false">
            <font-awesome-icon icon="times" class="menu-close-btn" size="lg"/>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>

  <scp-broadcast-create v-model="broadcast.visible"/>

  <!-- body -->
  <router-view/>
  <!-- online suvery
  <el-dialog
      title="DSS Online Survey"
      :close-on-click-modal="false"
      :show-close="false"
      v-model="surveyData.visible"
      width="850px">
    <div>Thanks for using DSS system which has served you <b>{{ $thousandBitSeparator(surveyData.viewCount) }}</b> times
      in the past 6 months.
    </div>
    <div>In order to help you complete your work better, we are looking forward to your feedback on the following
      questions.
    </div>
    <div>How much time can you <b style="font-size: 14px;color: var(--scp-text-color-error)">SAVE</b> with the following functions?</div>
    <hr>
    <div style="height: 450px;overflow: auto">
      <el-form ref="formRef" :model="surveyData.form" label-width="180px" class="no-margin" :rules="surveyData.rules">
        <el-row>
          <el-col :span="24">
            <el-form-item label="Select a statistical unit" prop="timeUnit">
              <el-select v-model="surveyData.form['timeUnit']" placeholder="Select a statistical unit">
                <el-option
                    v-for="item in [{label: 'minute(s) per day', value: 'MIN_IN_DAY'}, {label: 'minute(s) per week', value: 'MIN_IN_WEEK'}]"
                    :key="item['value']"
                    :label="item['label']"
                    :value="item['value']">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-for="item in surveyData.surveyData" :key="item['MENU_CODE']">
          <el-row>
            <el-col :span="15">
              <el-form-item :label="item['NAME']" :prop="item['MENU_CODE']">
                <el-input-number v-model="surveyData.form[item['MENU_CODE']]" controls-position="right" :min="0"
                                 :max="100" :step="0.1"/>
                <i style="font-size: 10px;color: #aaa"> {{ _timeUnit }}</i>
              </el-form-item>
            </el-col>
            <el-col :span="9" style="line-height: 36px" class="survey-comments">
              <el-input v-model="surveyData.form[item['MENU_CODE'] + '_COMMENTS']" style="width: calc(100% - 30px);"
                        placeholder="Your Suggestion"></el-input>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <template v-slot:footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitSurvey">Save</el-button>
        </span>
    </template>
  </el-dialog>
  -->
</template>

<script lang="ts" setup>
import { useStore } from 'vuex'
import { computed, inject, nextTick, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { FormInstance } from 'element-plus'
import store from '@/store'
import h337 from 'heatmap.js'
import ScpBroadcastCreate from '@/components/starter/components/BroadcastCreate.vue'

const $store = useStore()
const $router = useRouter()
const $route = useRoute()
const $axios: any = inject('$axios')
const $rem2Px: any = inject('$rem2Px')
const $message: any = inject('$message')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $dateFormatter: any = inject('$dateFormatter')

interface Menu {
  name: string,
  parentName: String,
  url: string,
  icon: string,
  children: Array<Menu>
}

const _height = computed(() => {
  return window.innerHeight - 60
})
const _width = computed(() => {
  return window.innerWidth
})

const broadcast = reactive({
  visible: false
})

// region click heatmap
const clickData = reactive({
  loading: false,
  clicks: [] as any,
  dateRange: [] as any,
  userField: [] as any,
  userOpts: [] as any,
  url: $route.path,
  clickShow: false,
  visible: false,
  mapVisible: false,
  heatmapInstance: [] as any
})
const initHeatMap = () => {
  clickData.heatmapInstance.push(h337.create({
    container: document.getElementById('heatmap'),
    radius: 30,
    maxOpacity: 0.8,
    minOpacity: 0.02,
    blur: 0.75,
    gradient: {
      // enter n keys between 0 and 1 here
      // for gradient color customization
      '.01': '#da5b00',
      '.2': '#e31684',
      '.5': '#f80b0b',
      '.95': '#f8d0d0'
    }
  }))
  clickData.heatmapInstance.push(h337.create({
    container: document.getElementById('heatmap'),
    radius: 30,
    maxOpacity: 0.8,
    minOpacity: 0.02,
    blur: 0.75,
    gradient: {
      // enter n keys between 0 and 1 here
      // for gradient color customization
      '.01': '#6223ec',
      '.2': '#3e82ff',
      '.5': '#122ffa',
      '.95': '#b3e7ff'
    }
  }))

  $axios({
    method: 'post',
    url: '/system/query_click_show_auth',
    data: { userid: localStorage.getItem('username') }
  }).then((body) => {
    clickData.clickShow = body
  })
  $axios({
    method: 'post',
    url: '/system/query_user_opts'
  }).then((body) => {
    clickData.userOpts = body.userOpts
    clickData.userField = body.userField
  })
}
const showClick = () => {
  clickData.loading = true
  $axios({
    method: 'post',
    url: '/system/query_click_data',
    data: {
      url: $route.path,
      users: clickData.userField,
      dateRange: clickData.dateRange
    }
  }).then((body) => {
    const showLclicks = [] as any
    const showRclicks = [] as any
    const bodyWrap = document.getElementsByClassName('body-wrapper')[0]
    if (body) {
      for (const c of body) {
        if (c.type === 'click') {
          showLclicks.push({
            x: Math.floor(c.clickX * _width.value / 100),
            y: Math.floor(c.clickY * _height.value / 100 - bodyWrap.scrollTop),
            value: 1
          })
        } else if (c.type === 'auxclick') {
          showRclicks.push({
            x: Math.floor(c.clickX * _width.value / 100),
            y: Math.floor(c.clickY * _height.value / 100 - bodyWrap.scrollTop),
            value: 1
          })
        }
      }
    }
    clickData.heatmapInstance[0].setData({
      min: 0,
      max: 20,
      data: showLclicks
    })
    clickData.heatmapInstance[1].setData({
      min: 0,
      max: 20,
      data: showRclicks
    })
    const click0 = clickData.heatmapInstance[0].getData().data
    const click1 = clickData.heatmapInstance[1].getData().data
    for (const c0 of click0) {
      if (c0.value > clickData.heatmapInstance[0].getData().max) {
        clickData.heatmapInstance[0].setDataMax(c0.value)
      }
    }
    for (const c1 of click1) {
      if (c1.value > clickData.heatmapInstance[1].getData().max) {
        clickData.heatmapInstance[1].setDataMax(c1.value)
      }
    }
    clickData.mapVisible = true

    nextTick(() => {
      // region tooltip
      const wrapper = document.getElementById('heatmap')
      const tooltip = document.getElementById('tooltip')
      tooltip.style.position = 'absolute'
      tooltip.style.right = '0'
      tooltip.style.bottom = '0'
      tooltip.style.top = 'auto'
      tooltip.style.left = 'auto'
      tooltip.style.display = 'block-inline'
      if (wrapper && tooltip) {
        wrapper.onmousemove = (ev) => {
          const x = ev.clientX
          const y = ev.clientY - 60
          tooltip.innerHTML = '<p>lclick: ' + clickData.heatmapInstance[0].getValueAt({ x, y }) + '</p><p>rclick: ' +
              clickData.heatmapInstance[1].getValueAt({ x, y }) + '</p>'
        }
      }
      // endregion
    })
  }).finally(() => {
    clickData.loading = false
  })
}
const clickMenuChange = (val) => {
  goToMenu(val)
  setTimeout(showClick, 2000)
}

const _avatar = computed(() => {
  return 'https://scp-dss.cn.schneider-electric.com/avatar/' + localStorage.getItem('username') + '.jpg'
})

const _clickMenuData = computed(() => {
  const data = []
  for (let i = 0; i < menuData.menus.length; i++) {
    data.push(...menuData.menus[i].children)
  }
  return data
})
// endregion

// region menu
const menuData = reactive({
  menuSearch: '',
  menuSearchShow: false,
  menuSearchWidth: 500,
  homeURL: '',
  menus: [] as Array<Menu>, // 所有的菜单
  subMenus: [] as Array<Menu>, // 显示的下拉菜单中所有的菜单
  subMenusLeft: [] as Array<string>, // 显示在左边的菜单
  subMenusRight: [] as Array<Menu>, // 显示在右侧的菜单
  username: '',
  menuShow: false
})

let showTimer: any = null
let hiddenTimer: any = null

const goToMenu = (url) => {
  menuData.menuShow = false
  menuData.menuSearchShow = false
  // 以https网址开头的所有带端口号的网页, 全部以嵌入的方式显示, 但是不带端口号的, 以新窗口打开
  if (url.indexOf('https://scp-dss.cn.schneider-electric.com') !== -1 && url.indexOf('https://scp-dss.cn.schneider-electric.com/') !== 0) {
    $router.push({ path: '/embed', query: { u: url } })
    let clickMenu = {} as Menu
    for (let i = 0; i < menuData.subMenusRight.length; i++) {
      if (menuData.subMenusRight[i].url === url) {
        clickMenu = menuData.subMenusRight[i]
      }
    }
    store.commit('setCurrentPage', [clickMenu.parentName, clickMenu.name])
  } else if (url.indexOf('http') === 0) {
    window.open(url, '_blank')
  } else {
    nextTick(() => {
      if ($router.currentRoute.value.path !== url) {
        localStorage.setItem('planned-page', url) // 跳转之前将需要跳转的地址存下来, planned-page: 将要跳转的页面
        $router.push(url)
      }
    })
  }
}

const level1MenuMouseover = (name) => {
  clearTimeout(hiddenTimer)
  showTimer = setTimeout(() => {
    if (name) {
      let subMenus = [] as Array<Menu>
      const left: string[] = []
      for (let i = 0; i < menuData.menus.length; i++) {
        if (menuData.menus[i].name === name) {
          subMenus = menuData.menus[i].children
          break
        }
      }
      for (let i = 0; i < subMenus.length; i++) {
        left.push(subMenus[i].name)
      }

      menuData.subMenus = subMenus
      menuData.subMenusLeft = left
      if (subMenus.length > 0) {
        menuData.subMenusRight = subMenus[0].children
      }
      nextTick(() => {
        menuData.menuShow = true
      })
    } else {
      menuData.menuShow = true
    }
  }, 100)
}

const level1MenuMouseleave = () => {
  clearTimeout(showTimer)
  hiddenTimer = setTimeout(() => {
    menuData.menuShow = false
    menuData.menuSearchShow = false
  }, 100)
}

const level2MenuMouseover = (name) => {
  let result = [] as Array<Menu>
  for (let i = 0; i < menuData.subMenus.length; i++) {
    if (menuData.subMenus[i].name === name) {
      result = menuData.subMenus[i].children
      break
    }
  }
  menuData.subMenusRight = result
}

const menuSearchRef = ref<HTMLInputElement>()
const showSearchInput = () => {
  const ul = document.getElementById('newMenuSubLeft') as HTMLUListElement
  if (ul) {
    const totalWidth = ul.clientWidth
    let widgetWidth = 0
    if (ul.childNodes && ul.childNodes.length > 0) {
      const li = ul.childNodes[0] as HTMLLIElement
      widgetWidth = li.clientWidth
      menuData.menuSearchWidth = totalWidth - (totalWidth % widgetWidth) - $rem2Px(0.8)
    }
    menuData.menuSearchShow = !menuData.menuSearchShow

    if (menuData.menuSearchShow) {
      nextTick(() => {
        if (menuSearchRef.value) {
          menuSearchRef.value.focus()
        }
      })
    }
  }
}
const sendToHomepage = (url) => {
  $axios({
    method: 'post',
    url: '/send_to_homepage',
    data: {
      url
    }
  }).then(() => {
    menuData.homeURL = url
    menuData.menuShow = false
    $message.success('Homepage modified.')
  }).catch((error) => {
    console.log(error)
  })
}

const _subMenusLeft = computed(() => {
  if (menuData.menuSearchShow && menuData.menuSearch) {
    const search = [] as Array<String>
    for (let i = 0; i < menuData.subMenus.length; i++) {
      const sm = menuData.subMenus[i]
      for (let j = 0; j < sm.children.length; j++) {
        const c = sm.children[j]
        if ((c.name || '').toLowerCase().indexOf(menuData.menuSearch.toLowerCase()) !== -1) {
          if (search.indexOf(sm.name) === -1) {
            search.push(sm.name)
          }
        }
      }
    }
    return search
  } else {
    return menuData.subMenusLeft
  }
})

const _subMenusRight = computed(() => {
  if (menuData.menuSearchShow && menuData.menuSearch) {
    const search = [] as Array<Menu>
    const searchNames = [] as Array<string>
    for (let i = 0; i < menuData.subMenus.length; i++) {
      const sm = menuData.subMenus[i]
      for (let j = 0; j < sm.children.length; j++) {
        const c = sm.children[j]
        if ((c.name || '').toLowerCase().indexOf(menuData.menuSearch.toLowerCase()) !== -1) {
          if (searchNames.indexOf(c.name) === -1) {
            search.push(c)
            searchNames.push(c.name)
          }
        }
      }
    }
    return search
  } else {
    return menuData.subMenusRight
  }
})
// endregion

// region survey
const formRef = ref<FormInstance>()
const surveyData = reactive({
  visible: false,
  form: {
    timeUnit: ''
  },
  surveyData: [],
  viewCount: 0,
  rules: {}
})

const submitSurvey = () => {
  if (!formRef.value) {
    return false
  }
  formRef.value?.validate((valid) => {
    if (valid) {
      $axios({
        method: 'post',
        url: '/system/submit_survey',
        data: {
          form: surveyData.form
        }
      }).then(function () {
        $message.success('Thanks for your participation.')
        surveyData.visible = false
      }).catch((error) => {
        console.log(error)
      })
      return true
    } else {
      $message.error('Please confirm your input')
      return false
    }
  })
  return true
}
const _timeUnit = computed(() => {
  if (surveyData.form.timeUnit === 'MIN_IN_DAY') {
    return 'minute(s) per day'
  } else if (surveyData.form.timeUnit === 'MIN_IN_WEEK') {
    return 'minute(s) per week'
  } else {
    return ''
  }
})
// endregion

const queryNotification = () => {
  if (localStorage.getItem('token')) {
    $axios({
      method: 'post',
      url: '/system/query_notification',
      data: {
        cachable: 'Y'
      }
    }).then((body) => {
      $store.commit('setMail', body.mail)
      $store.commit('setChangeLogs', body.changeLog)
      $store.commit('setMaintainer', body.maintainer)
    }).catch((error) => {
      console.log(error)
    })
  }
}
const logout = () => {
  $axios({
    method: 'post',
    url: '/logout'
  }).then(() => {
    localStorage.removeItem('token')
    $router.push('/login')
  }).catch((error) => {
    console.log(error)
  })
}

onMounted(() => {
  // 从localStorage中加载主题
  const theme = localStorage.getItem('theme')
  if (theme) {
    $store.commit('setTheme', theme)
    document.getElementsByTagName('html')[0].className = theme
  }

  const start = new Date()
  const end = new Date()
  start.setDate(start.getDate() - 7)
  end.setDate(end.getDate() + 1)
  clickData.dateRange = [$dateFormatter(start, 'yyyy/MM/dd'), $dateFormatter(end, 'yyyy/MM/dd')]

  // 加载菜单和主页
  menuData.username = localStorage.getItem('name') + ''
  $axios({
    method: 'post',
    url: '/query_menu_list'
  }).then((body) => {
    menuData.menus = body.menuList
    menuData.homeURL = body.homeURL
  }).catch((error) => {
    console.log(error)
  })
})

onMounted(() => {
  initHeatMap()
  queryNotification()
})
</script>

<style lang="scss">
#SCPMenu {
  .el-badge__content {
    font-size: 0.3rem !important;
    height: 12px !important;
    line-height: 12px !important;
    padding: 0 4px !important;
    top: 12px;
  }

  .message-close {
    float: right;
    position: absolute;
    right: 0;
    top: 1.9rem;
    cursor: pointer;
    font-size: 0.5rem;
    text-align: center;
    width: 1rem;
    background-color: var(--scp-bg-color);
    color: transparent;
  }

  .message-close:hover {
    color: var(--scp-text-color-error);
  }

  .menu-search-input {
    margin: 0.5rem 0.5rem 0 0.4rem;
  }

  .survey-comments {
    .el-input__inner {
      font-size: 0.4rem;
    }
  }

  .el-divider--vertical {
    margin: 0 0.5rem;
    height: 0.5rem;
  }

  @media print {
    .site-menu {
      display: none;
    }

    .header {
      position: unset !important;
    }

    .header-operation {
      display: none;
    }
  }
}
</style>

<style lang="scss" scoped>
.header {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  border-bottom: 1px solid var(--scp-border-color);
  background-color: var(--scp-bg-color);
  z-index: 1001;
  height: 1.85rem;
  color: var(--scp-text-color-primary);

  .logo {
    position: fixed;
    margin: 0 !important;
    width: 4.5rem;
    text-align: center;
    height: 1.85rem;
    line-height: 1.85rem;

    img {
      height: 1.6rem !important;
      line-height: 1;
      vertical-align: middle;
    }
  }

  .site-menu {
    top: 0;
    left: 3.5rem;
    position: fixed;

    .menu-primary {
      li {
        display: inline-block;
        font-size: 0.6rem;
        height: 1.85rem;
        line-height: 1.85rem;
        margin-left: 0.8rem;
        cursor: pointer;
      }
    }
  }

  .header-operation {
    position: fixed;
    right: 7.5rem;

    .header-operation-primary {
      height: 1.85rem;
      line-height: 1.85rem;
      display: inline-block;
      font-size: 0.45rem;

      .page-title {
        color: var(--scp-text-color-secondary)
      }

      li {
        display: inline-block;

        .el-dropdown {
          vertical-align: middle !important;
        }
      }
    }
  }

  .header-right {
    position: fixed;
    padding-right: 0.6rem;
    right: 0;
    height: 1.85rem;
    line-height: 1.85rem;

    img {
      width: 7rem;
    }
  }
}

.menu-body {
  box-shadow: 0 12px 10px 0 rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 1.9rem;
  left: 0;
  right: 0;
  height: 15rem;
  z-index: 2001;
  background-color: var(--scp-bg-color);
  font-size: 0.5rem;

  .fake-background {
    position: absolute;
    height: 100%;
    width: 50%;
    background: var(--scp-bg-color-fill-lighter);
  }

  .menu-container {
    width: calc(100% - 16rem);
    margin: auto;
    height: 100%;

    .menu-column-left {
      height: 100%;
      overflow: auto;
      background-color: var(--scp-bg-color-fill-lighter);
      padding-top: 0.4rem;

      ul {
        li {
          margin: 0.2rem 0;
          cursor: pointer;
          font-weight: bold;
          font-size: 0.5416666rem;

          .menu-title {
            display: inline-block;
            position: relative;

            .menu-bottom-liner {
              width: 100%;
              border-bottom: 2px solid var(--scp-text-color-highlight);
              transform: scaleX(0);
              transform-origin: left;
              transition: transform .15s ease-in-out;
            }
          }
        }

        li {
          .menu-title:hover {
            color: var(--scp-text-color-highlight);

            .menu-bottom-liner {
              transform: scaleX(1);
            }
          }
        }
      }
    }

    .menu-column-right {
      height: 100%;
      overflow: auto;
      background-color: var(--scp-bg-color);

      ul {
        li {
          display: inline-block;

          .menu-item {
            width: 6.5rem;
            border: 1px solid var(--scp-border-color-lighter);
            text-align: center;
            padding: 0.55rem 0 0.4rem 0;
            box-shadow: 0 0 10px var(--scp-border-color-lighter);
            margin: 0.4rem;
            line-height: 1.5;
            cursor: pointer;
            position: relative;

            .icon {
              font-size: 0.8rem;
            }

            p {
              font-size: 0.45rem;
              margin: 0.15rem 0 0 0 !important;
            }

            .send-to-homepage {
              position: absolute;
              right: 4px;
              top: 2px;
              color: transparent;
            }

            .send-to-homepage:hover {
              color: #fff;
            }
          }

          .menu-item:hover {
            background-color: var(--scp-text-color-highlight);
            color: #fff;
          }
        }
      }
    }

    .menu-search {
      margin-top: 0.5rem;
      display: inline-block;
      float: left;

      .menu-search-btn:hover {
        color: var(--scp-text-color-highlight);
        cursor: pointer;
      }
    }

    .menu-close {
      margin-top: 0.5rem;
      display: inline-block;
      float: right;

      .menu-close-btn:hover {
        color: var(--scp-text-color-error);
        cursor: pointer;
      }
    }
  }
}

#clickMap {
  position: absolute !important;
  z-index: 1100;
  background: rgba(117, 117, 117, 0.22);
  height: 100%;
  width: 100%;
}

#heatmap {
  position: absolute !important;
  z-index: 1111;
  background: rgba(117, 117, 117, 0.22);
}

#tooltip {
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, .8);
  color: white;
  font-size: 14px;
  padding: 5px;
  line-height: 18px;
}

.hand:hover {
  color: var(--scp-bg-color-highlight);
}
</style>
