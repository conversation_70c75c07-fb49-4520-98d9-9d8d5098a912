<template>
  <div class="body-wrapper" ref="bodyRef" :style="{height: height + 'px'} ">
    <div>
      <el-row id="SCPBody" v-show="_message !== '' && msgData.messageShow">
        <el-col :span="23" class="marques-container">
          <div class="marquee">
            <p v-html="_message" style="padding-top: 0.208333rem"></p>
          </div>
        </el-col>
        <el-col :span="1" style="">
          <div class="message-close">
            <font-awesome-icon icon="times" @click="closeMarques"/>
          </div>
        </el-col>
      </el-row>
    </div>
    <router-view/>

    <!-- 公共View Details模块 -->
    <scp-draggable-resizable v-model="$store.state.viewDetails.visible" h="450px" w="60vw" :title="$store.state.viewDetails.title">
      <template v-slot="{ height }">
        <scp-table
            ref="publicViewDetailsRef"
            :max-height="height - 150"
            :lazy="true"
            :params="$store.state.viewDetails.params"
            :url="$store.state.viewDetails.url"
            :download-url="$store.state.viewDetails.durl"
            :columns="$store.state.viewDetails.columns"/>
      </template>
    </scp-draggable-resizable>

    <!-- 公共View Chart模块 -->
    <scp-draggable-resizable v-model="$store.state.viewChart.visible" h="90vh" w="90vw" :title="$store.state.viewChart.title" :resizestop="resizeChart">
      <template v-slot="{ height }">
        <chart ref="publicViewChartRef" :height="height - 100" :option="$store.state.viewChart.opts"/>
      </template>
    </scp-draggable-resizable>

    <scp-popup v-model="$route.fullPath"/>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, inject, ref, reactive, computed, watch, nextTick } from 'vue'
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import ScpPopup from '@/components/starter/components/Popup.vue'

const $axios: any = inject('$axios')
const $store: any = useStore()

// region temp hidden
const $route = useRoute()
const $router = useRouter()
const $watermark: any = inject('$watermark')

const publicViewDetailsRef = ref()
const publicViewChartRef = ref()
const height = ref(300)

const marquesCtlKey = 'marques-data'

const shouldShowMarques = () => {
  const today = new Date().toISOString().split('T')[0] // 获取今天的日期，格式为 YYYY-MM-DD
  const storedData = localStorage.getItem(marquesCtlKey) // 获取存储的数据

  if (!storedData) {
    // 如果没有存储数据，初始化为今天的日期和0次
    localStorage.setItem(marquesCtlKey, `${today}:0`)
    return true // 今天第一次显示
  }

  const [storedDate, storedCount] = storedData.split(':') // 解析存储的数据
  const count = parseInt(storedCount)

  if (storedDate !== today) {
    // 如果存储的日期不是今天，重置为今天的日期和0次
    localStorage.setItem(marquesCtlKey, `${today}:0`)
    return true // 今天第一次显示
  }

  // 如果今天已经显示了3次，不再显示
  if (count >= 2) {
    return false
  }

  // 否则，显示窗口
  return true
}

const closeMarques = () => {
  msgData.messageShow = false
  const storedData = localStorage.getItem(marquesCtlKey) + ''
  const [storedDate, storedCount] = storedData.split(':')
  const count = parseInt(storedCount)
  localStorage.setItem(marquesCtlKey, `${storedDate}:${count + 1}`)
}

const adjustHeight = () => {
  height.value = window.innerHeight - 50
}

window.addEventListener('resize', () => {
  adjustHeight()
  adjustZoom()
})

const adjustZoom = () => {
  // @ts-ignore
  if (document.documentElement.style.zoom) {
    // @ts-ignore
    height.value = height.value / document.documentElement.style.zoom
  }
}

const bodyRef = ref()
const pageCtl = reactive({
  clicks: [] as any
})

// endregion

// region notification
interface systemMsg {
  type: string,
  message: string
}

const msgData = reactive({
  messageShow: true,
  systemMsg: [] as Array<systemMsg>
})

const _message = computed(() => {
  if (msgData.systemMsg.length > 0) {
    let html = ''
    for (let i = 0; i < msgData.systemMsg.length; i++) {
      const msg = msgData.systemMsg[i]
      if (msg.type === 'error') {
        html += '<span class="message-danger">' + msg.message + '</span>'
      } else {
        html += '<span class="message-info">' + msg.message + '</span>'
      }
    }
    return html
  } else {
    return ''
  }
})
const syncSystemMessage = () => {
  // 如果没有token, 说明未登录, 就不再请求了
  if (localStorage.getItem('token')) {
    $axios({
      method: 'post',
      url: '/query_system_notification'
    }).then((body) => {
      if (body.length > 0) {
        if (shouldShowMarques()) {
          msgData.systemMsg = body
        }
      } else {
        msgData.systemMsg = []
      }
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      setTimeout(() => {
        syncSystemMessage()
      }, 600000)
    })
  }
}
// endregion

const handleClick = (type: string, event) => {
  const bodyRect = bodyRef.value.getBoundingClientRect()
  const offsetX = event.pageX
  const offsetY = event.pageY - bodyRef.value.offsetTop + bodyRef.value.scrollTop

  const bodyWidth = bodyRect.width
  const bodyHeight = bodyRect.height

  const relativeX = (offsetX / bodyWidth) * 100
  const relativeY = (offsetY / bodyHeight) * 100

  pageCtl.clicks.push({
    type,
    clickX: relativeX,
    clickY: relativeY
  })
  if (pageCtl.clicks.length === 64) {
    saveClicks()
  }
}
const saveClicks = () => {
  if (pageCtl.clicks.length !== 0 && localStorage.getItem('token')) {
    $axios({
      method: 'post',
      url: '/system/save_click_data',
      data: {
        url: $route.path,
        user: localStorage.getItem('username'),
        clicks: pageCtl.clicks
      }
    })
  }
  pageCtl.clicks = []
}
$router.beforeEach(() => {
  $store.commit('setViewDetailsVisible', false)
  saveClicks()
})

watch(() => $store.state.viewDetails.id, () => {
  if ($store.state.viewDetails.visible) {
    nextTick(() => {
      publicViewDetailsRef.value.clearAndSearch()
    })
  }
})

const resizeChart = () => {
  nextTick(() => {
    publicViewChartRef.value.resize()
  })
}

watch(() => $store.state.viewChart.visible, (newVal) => {
  resizeChart()
})

onBeforeRouteUpdate(() => {
  adjustHeight()
  adjustZoom()
})

watch(() => height.value, (newVal) => {
  $store.commit('setPageHeight', newVal)
})

onMounted(() => {
  adjustHeight()
  bodyRef.value.addEventListener('click', (event) => {
    handleClick('click', event)
  }, true)
  bodyRef.value.addEventListener('auxclick', (event) => {
    handleClick('auxclick', event)
  }, true)

  // 如果检测到页面有zoom, 根据zoom调整高度
  setTimeout(() => {
    adjustZoom()
  }, 3000)

  const _watermark = $route.query._watermark
  if (_watermark) {
    $watermark({ watermark_txt: _watermark })
  }

  setTimeout(() => {
    syncSystemMessage()
  }, 15000)
})
window.onbeforeunload = () => {
  saveClicks()
}
</script>
<style lang="scss">
@media print {
  .body-wrapper {
    height: auto !important;
  }
}

#SCPBody {
  .marques-container {
    height: 1.2rem;
    line-height: 1.2rem;
    overflow: hidden;
    @keyframes marquee {
      0% {
        transform: translateX(100%);
      }
      100% {
        transform: translateX(-100%);
      }
    }

    .marquee {
      animation: marquee 60s linear infinite;
      font-size: 0.5rem;
    }
  }

  .marques-container:hover {
    .marquee {
      animation-play-state: paused;
    }
  }

  .message-danger {
    color: var(--scp-text-color-error);
    font-weight: bold;
    padding-right: 10rem;
  }

  .message-info {
    color: var(--scp-text-color-highlight);
    font-weight: bold;
    padding-right: 10rem;
  }

  .message-close {
    display: flex;
    width: calc(100% - var(--scp-widget-margin));
    height: calc(100% - 0.208333rem);
    align-items: flex-end;
    justify-content: flex-end;
    cursor: pointer;
    font-size: 0.5rem;
    background-color: var(--scp-bg-color);
    color: var(--scp-text-color-lighter);
  }

  .message-close:hover {
    color: var(--scp-text-color-error);
  }
}
</style>
