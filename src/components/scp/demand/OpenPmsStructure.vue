<template>
  <div class="left-sidebar" id="openPmsStructure">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['OPEN_PMS_STRUCTURE_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.valueType" size="small">
              <el-option
                  v-for="item in ['Net Net Price', 'Quantity', 'Line', 'Weight']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['report4DateRange','report4DateRangeDuedate','report4Duedate','report5DateColumns']"
            />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14" v-loading="pageCtl.loading.report2">
            <div class="subscript-container subscript-container-left" style="height: 350px">
              <scp-subscript id="PMS1" ref="report2SubscriptRef"/>
              <div class="front">
                <chart v-contextmenu:report2ContextmenuRef :height="350" :option="_report2Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Treemap Level Settings</div>
                  <el-row>
                    <el-col :span="6">Level1</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level1" size="small" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level2</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level2" size="small" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level3</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level3" size="small" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level4</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level4" size="small" placeholder="Select..." clearable
                                 filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level5</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level5" size="small" placeholder="Select..." clearable
                                 filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display level</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.leafDepth" size="small" placeholder="Select...">
                        <el-option
                            v-for="item in [1, 2]"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.report2Tooltips"
                                     style="height:calc(100% - 70px);overflow: auto">
                    <el-checkbox
                        v-for="item in pageCtl.report2TooltipsOpts"
                        :key="item"
                        :value="item"
                        :label="item"
                    ></el-checkbox>
                  </el-checkbox-group>

                  <div class="box-footer">
                    <el-button @click="report2SubscriptRef.toggleView()">Back</el-button>
                    <el-button
                        type="primary"
                        @click="async () => {
                          await report2SubscriptRef.toggleView()
                          await searchReport2()
                          await nextTick()
                          report2SubscriptRef?.resize()
                        }">Search
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="10" v-loading="pageCtl.loading.report3">
            <div class="subscript-container subscript-container-right" style="height: 350px">
              <scp-subscript id="PMS3" ref="report3SubRef"/>
              <div class="front">
                <chart ref="report3Ref" :height="350" :option="_report3Opt"/>
              </div>
              <div class="back">
                <div class="box">
                  <div class="box-title">
                    Filter For Open PMS
                  </div>
                  <el-row>
                    <el-col :span="6">Stack By</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.report3GroupBy">
                        <el-option v-for="item in _pivotColumns"
                                   :value="item"
                                   :key="item"
                                   :label="item"/>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="box-footer">
                  <el-button
                      @click="report3SubRef.toggleView()">
                    Back
                  </el-button>
                  <el-button
                      type="primary"
                      @click="async () => {
                          await report3SubRef.toggleView()
                          await searchReport3()
                          await nextTick()
                          report3SubRef?.resize()
                        }">Search
                  </el-button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row class="search-box">
          <el-col :span="24">
            <div class="subscript-container">
              <scp-subscript id="PMS3"/>
              <el-row style="margin-bottom: var(--scp-widget-margin)">
                <el-col :span="4">
                  <el-select
                      v-model="pageCtl.conditions.report4ViewType" size="small"
                      style="width: var(--scp-input-width) !important;" filterable>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="3" v-show="pageCtl.conditions.report4DateType === 'BY_DATE'">
                  <el-select
                      v-model="pageCtl.conditions.report4SelectedType"
                      style="width: var(--scp-input-width) !important;"
                      size="small">
                    <el-option label="View by Day" value="VIEW_BY_DAY"/>
                    <el-option label="View by Week" value="VIEW_BY_WEEK"/>
                    <el-option label="View by month" value="VIEW_BY_MONTH"/>
                    <el-option label="View by quarter" value="VIEW_BY_QUARTER"/>
                    <el-option label="View by year" value="VIEW_BY_YEAR"/>
                  </el-select>
                </el-col>
                <el-col :span="6" v-show="pageCtl.conditions.report4DateType === 'BY_DATE'">
                  <el-date-picker
                      size="small"
                      v-model="pageCtl.conditions.report4DateRange"
                      type="daterange"
                      unlink-panels
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      range-separator="to"
                      :clearable="false"
                      start-placeholder="Start date"
                      end-placeholder="End date"
                      :picker-options="{}">
                  </el-date-picker>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report4ShowType" size="small">
                    <el-option label="View by value" value="VIEW_BY_VALUE"/>
                    <el-option label="View by percentage" value="VIEW_BY_PERCENT"/>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport4">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <chart ref="report4Ref" :height="360" :option="_report4Opt" v-loading="pageCtl.loading.report4"/>
            </div>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report5">
            <div class="subscript-container">
              <scp-subscript id="DOST"/>
              <el-row class="search-box">
                <el-col :span="5">
                  <el-select v-model="pageCtl.conditions.report5Columns" size="small" placeholder="Columns" filterable
                             clearable collapse-tags multiple>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport5">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-table
                  url="/demand/open_pms_structure/query_report5"
                  :params="_conditions"
                  :lazy="true"
                  :pagging="false"
                  :fixed-columns-left="_report5Columns.length"
                  :max-height="375"
                  :show-total="true"
                  :context-menu-items="pageCtl.report5ContextItems"
                  :after-select="afterReport5Select"
                  ref="report5Ref"
                  :columns="pageCtl.report5Columns"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <v-contextmenu ref="report2ContextmenuRef">
      <v-contextmenu-item @click="viewParent" v-show="pageCtl.selectedParentLevel !== ''">
        View {{ pageCtl.selectedParentLevel }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="viewCurrent">
        View {{ pageCtl.selectedCurrentLevel }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, nextTick } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $toFixed: any = inject('$toFixed')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $axios: any = inject('$axios')
const $join: any = inject('$join')
const $deepClone: any = inject('$deepClone')
const $isEmpty: any = inject('$isEmpty')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const report2ContextmenuRef = ref()
const $toolbox: any = inject('$echarts.toolbox')
const $tooltip: any = inject('$echarts.tooltip')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report2SubscriptRef = ref()
const report3SubRef = ref()
const report3Ref = ref()
const report4Ref = ref()
const report5Ref = ref()

const viewReport5Details = () => {
  $viewDetails({
    url: '/demand/open_pms_structure/query_report5_details',
    durl: '/demand/open_pms_structure/download_report5_details',
    params: _conditions.value,
    title: 'View Details [' + $join(...pageCtl.report5DetailsType) + ']'
  })
}

const pageCtl = reactive({
  selectedParentLevel: '',
  selectedCurrentLevel: '',
  filterOpts: [],
  dateColumnOpts: [],
  report2TooltipsOpts: ['OPEN_PMS_QTY'],
  conditions: {
    $scpFilter: {
      cascader: [
        ['ABNORMAL_DATA_LABEL', 'N'],
        ['IS_DELETED', '0']
      ],
      filter: []
    },
    scope: '',
    subScope: [],
    dateColumn: '',
    dateColumnRange: [],
    valueType: 'Net Net Price',
    leafDepth: 1,
    level1: 'ENTITY',
    level2: 'PRODUCT_LINE',
    level3: 'PRODUCT_LINE',
    level4: 'PRODUCT_LINE',
    level5: 'PRODUCT_LINE',
    report2Tooltips: [],
    selectedTreePath: '',
    selectedLegend: {},
    report4SeriesType: 'line',
    report4DateType: 'BY_DATE',
    report4DateRange: [] as any,
    report4SelectedType: 'VIEW_BY_DAY',
    report4ShowType: 'VIEW_BY_VALUE',
    report4Duedate: '',
    report4ViewType: 'PMS_PENDING_DAYS_RANGE',
    report4DateRangeDuedate: [] as any,
    report4SelectedTypeDuedate: 'VIEW_BY_DAY',
    report4DetailsDateRangeDuedate: [],
    report5Columns: ['CLUSTER_NAME', 'ENTITY'],
    report5ViewType: 'VIEW_BY_DAY',
    report5DateColumns: [],
    report3GroupBy: 'PMS_PENDING_DAYS_RANGE'
  },
  loading: {
    filter: false,
    report2: false,
    report3: false,
    report4: false,
    report5: false
  },
  report2Data: [],
  report3Data: [],
  report4Data: {} as any,
  report5ContextItems: {
    view_details: {
      name: 'View details',
      callback: viewReport5Details
    },
    view_split0: { name: '---------' }
  },
  report5Columns: [],
  report3DetailsType: '' as any,
  report4DetailsType: '',
  report5DetailsType: []
})

onMounted(() => {
  initPage()

  report3Ref.value.chart().on('legendselectchanged', (params) => {
    pageCtl.conditions.selectedLegend = params.selected
  })

  report3Ref.value.chart().on('dblclick', (params) => {
    if (params.componentType === 'title') {
      const selectResult = [] as any
      let item = ''
      for (item in pageCtl.conditions.selectedLegend) {
        if (pageCtl.conditions.selectedLegend[item].toString() === 'true'.toString()) {
          selectResult.push(item)
        }
      }
      if (selectResult.length === 0) {
        pageCtl.report3DetailsType = ''
      } else {
        pageCtl.report3DetailsType = selectResult
      }
    } else {
      pageCtl.report3DetailsType = [params.name]
    }

    $viewDetails({
      url: '/demand/open_pms_structure/query_report3_details',
      durl: '/demand/open_pms_structure/download_report3_details',
      params: _conditions.value,
      title: 'View Details [' + (pageCtl.report3DetailsType ? pageCtl.report3DetailsType : 'Total') + ']'
    })
  })

  report4Ref.value.chart().on('dblclick', (params) => {
    pageCtl.report4DetailsType = params.name

    $viewDetails({
      url: '/demand/open_pms_structure/query_report4_details',
      durl: '/demand/open_pms_structure/download_report4_details',
      params: _conditions.value,
      title: 'View Details [' + pageCtl.report4DetailsType + ']'
    })
  })
  report4Ref.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      pageCtl.conditions.report4SeriesType = obj.currentType
    }
  })
})

const initPage = () => {
  const now = new Date()
  const start = new Date(now.getTime() - 86400000 * 60)
  pageCtl.conditions.report4DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(now, 'yyyy/MM/dd')
  ]
  const end = new Date(now.getTime() + 86400000 * 60)
  pageCtl.conditions.report4DateRangeDuedate = [
    $dateFormatter(now, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.conditions.report4Duedate = $dateFormatter(now, 'yyyy/MM/dd')

  $axios({
    method: 'post',
    url: '/demand/open_pms_structure/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.CASCADER
    pageCtl.dateColumnOpts = body.dateColumns
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  })
}

const search = () => {
  pageCtl.conditions.selectedTreePath = ''
  searchReport2()
  searchReport3()
  searchReport4()
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/demand/open_pms_structure/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    calculateValues(body)
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/demand/open_pms_structure/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const searchReport4 = () => {
  searchReport5()
  pageCtl.loading.report4 = true
  $axios({
    method: 'post',
    url: '/demand/open_pms_structure/query_report4',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report4Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report4 = false
  })
}

const searchReport5 = () => {
  report5Ref.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/demand/open_pms_structure/query_report5_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report5DateColumns = body
    pageCtl.report5Columns = parseReport5Columns()
    nextTick(() => {
      report5Ref.value.search()
    })
  }).catch((error) => {
    console.log(error)
  })
}

const parseReport5Columns = () => {
  const result = [] as any
  for (let i = 0; i < _report5Columns.value.length; i++) {
    result.push({
      data: _report5Columns.value[i]
    })
  }

  for (let i = 0; i < pageCtl.conditions.report5DateColumns.length; i++) {
    const column = pageCtl.conditions.report5DateColumns[i]
    result.push({
      title: column,
      data: '\'' + column + '\'_TOTAL',
      type: 'numeric'
    })
  }
  return result
}

const afterReport5Select = (r, rc, c) => {
  const selected = [] as any
  if (c.indexOf('\'_TOTAL') !== -1) {
    selected.push(c.replace(/'/g, '').replace(/_TOTAL/g, ''))
  } else {
    selected.push('')
  }
  for (let i = 0; i < _report5Columns.value.length; i++) {
    if (r[_report5Columns.value[0]] === 'Total') {
      selected.push('Total')
    } else {
      selected.push(r[_report5Columns.value[i]] || '')
    }
  }
  pageCtl.report5DetailsType = selected
}

const viewParent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedParentLevel
  searchReport3()
  searchReport4()
}

const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedCurrentLevel
  searchReport3()
  searchReport4()
}

const sumValues = (node) => {
  // 如果有子节点，则递归计算子节点的 value 之和
  if (node.children && node.children.length > 0) {
    let sum = 0
    node.children.forEach(child => {
      sum += sumValues(child)
    })
    node.value = sum // 将子节点的和赋值给当前节点
    node.children.sort((a, b) => b.value - a.value)
  }
  return node.value || 0
}

const calculateValues = (tree) => {
  tree.forEach(node => sumValues(node))
  tree.sort((a, b) => b.value - a.value)
}

const _report2Opt = computed(() => {
  return {
    title: {
      text: 'Open PMS Structure by Category'
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: $tooltip({
      callback: (e) => {
        pageCtl.selectedCurrentLevel = e.selectedCurrentLevel
        pageCtl.selectedParentLevel = e.selectedParentLevel
      }
    }, pageCtl.report2Data),
    series: [{
      name: 'PMS',
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report2Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})

const camelCaseStartPlaceholder = (word: string) => {
  const words = word.split('_')
  const camelCaseWords = words.map((word, index) => {
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  })
  return camelCaseWords.join(' ')
}

const _report3Opt = computed(() => {
  const colorSettings = {
    '0-1D': '#448a1a',
    '1-2D': '#539017',
    '2-3D': '#629512',
    '3-4D': '#749d10',
    '4-7D': '#87a40c',
    '7-14D': '#c6b601',
    '14-30D': '#e1a506',
    '1-2M': '#e1990a',
    '2-3M': '#d7671f',
    '3-6M': '#d25924',
    '>6M': '#cc4c27'
  }
  const colors = [] as any
  const data = $deepClone(pageCtl.report3Data)
  if (pageCtl.conditions.report3GroupBy === 'DELIVERY_RANGE') {
    for (let i = 0; i < data.length; i++) {
      colors.push(colorSettings[data[i].name])
      data[i].label = { color: colorSettings[data[i].name] }
    }
  }
  return {
    color: colors,
    title: {
      text: 'Open PMS Structure by ' + camelCaseStartPlaceholder(pageCtl.conditions.report3GroupBy) +
          (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : ''),
      triggerEvent: true
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any

        tip.push('<div style="width:9.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('(')
        tip.push(params.percent)
        tip.push('%)')
        tip.push('</span>')
        tip.push('</div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: {
      top: 'center',
      right: '10%',
      width: '25%'
    },
    series: [
      {
        type: 'pie',
        radius: '65%',
        center: ['40%', '55%'],
        data
      }
    ]
  }
})

const _report4Opt = computed(() => {
  const series = [] as any
  const legend = [] as any
  const colorMap = {
    '0-1D': '#448a1a',
    '1-2D': '#539017',
    '2-3D': '#629512',
    '3-4D': '#749d10',
    '4-7D': '#87a40c',
    '7-14D': '#c6b601',
    '14-30D': '#e1a506',
    '1-2M': '#e1990a',
    '2-3M': '#d7671f',
    '3-6M': '#d25924',
    '>6M': '#cc4c27'
  }

  let yAxisData = pageCtl.report4Data

  // 转换数字为百分比
  if (pageCtl.conditions.report4ShowType === 'VIEW_BY_PERCENT') {
    yAxisData = {}
    const stackTotal = {}
    const xAxis = pageCtl.report4Data.xAxis

    for (const key in pageCtl.report4Data) {
      if (pageCtl.report4Data.hasOwnProperty(key) && key !== 'xAxis') {
        for (let i = 0; i < xAxis.length; i++) {
          const x = xAxis[i]
          stackTotal[x] = (stackTotal[x] || 0) + pageCtl.report4Data[key][i]
        }
      }
    }

    for (const key in pageCtl.report4Data) {
      if (pageCtl.report4Data.hasOwnProperty(key) && key !== 'xAxis') {
        yAxisData[key] = []
        for (let i = 0; i < xAxis.length; i++) {
          const total = stackTotal[xAxis[i]]
          const v = pageCtl.report4Data[key][i]
          if (v && total) {
            yAxisData[key].push($toFixed(v * 100 / total, 3))
          } else {
            yAxisData[key].push(null)
          }
        }
      }
    }
  }

  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: pageCtl.conditions.report4SeriesType,
        smooth: false,
        stack: 'sum',
        itemStyle: {
          color: colorMap[key]
        },
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }

  let title = 'Evolution of Open PMS Structure by Date'
  title += (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')

  return {
    title: {
      text: title
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:9.5rem;">')
          const marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          if (pageCtl.conditions.report4ShowType === 'VIEW_BY_PERCENT') {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        if (pageCtl.conditions.report4ShowType === 'VIEW_BY_PERCENT') {
          tip.push('%')
        }
        tip.push('</span></div>')

        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: legend }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: pageCtl.conditions.report4SeriesType === 'line' ? false : [0, '10%'],
      data: pageCtl.report4Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    toolbox: $toolbox({ opts: ['line', 'stack', 'no-details'] }),
    series
  }
})

const _report5Columns = computed(() => {
  if (pageCtl.conditions.report5Columns.length > 0) {
    return pageCtl.conditions.report5Columns
  } else {
    return ['CLUSTER_NAME', 'ENTITY']
  }
})

const _conditions = computed(() => {
  const cond = $deepClone(pageCtl.conditions)
  cond.report5Columns = _report5Columns.value
  cond.report3DetailsType = pageCtl.report3DetailsType
  cond.report4DetailsType = pageCtl.report4DetailsType
  cond.report5DetailsType = pageCtl.report5DetailsType
  return cond
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts, 'SOLD_TO', 'MATERIAL')
})
</script>
