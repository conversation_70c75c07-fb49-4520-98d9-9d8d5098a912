<template>
  <div class="left-sidebar" id="PmsStructure" style="width: 100%">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['PMS_STRUCTURE_V']" :after-apply="search"/>
          </el-col>
          <el-col :span="2">
            <el-select
                style="width: 100% !important;"
                v-model="pageCtl.conditions.dateColumn"
                size="small">
              <el-option
                  v-for="item in pageCtl.dateColumnOpts"
                  :key="item"
                  :value="item"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-tooltip placement="bottom" effect="light">
              <div>
                <el-date-picker
                    v-model="pageCtl.conditions.overviewDateRange"
                    type="daterange"
                    unlink-panels
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    range-separator="to"
                    :clearable="false"
                    start-placeholder="Start date"
                    end-placeholder="End date"
                ></el-date-picker>
              </div>
              <template #content>
                {{ pageCtl.conditions.dateColumn }}
              </template>
            </el-tooltip>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.resultType">
              <el-option
                  v-for="item in pageCtl.conditions.resultTypeOpts"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"/>
            </el-select>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['overviewDateRange', 'report2DetailsDateRangeDuedate', 'report3SelectedDate']"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-loading="pageCtl.loading.report1">
            <div class="subscript-container subscript-container-left" style="height: 350px;">
              <scp-subscript id="PMSA" ref="report1SubRef"/>
              <div class="front">
                <chart v-contextmenu:report1ContextmenuRef :height="350" :option="_report1Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Treemap Level Settings</div>
                  <el-row>
                    <el-col :span="6">Level1</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level1" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level2</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level2" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :value="item"
                            :label="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level3</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level3" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :label="item"
                            :value="item"
                            :key="item"
                        />
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level4</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level4" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :value="item"
                            :label="item"
                        />
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level5</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level5" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display level</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.leafDepth" placeholder="Select...">
                        <el-option
                            v-for="item in [1, 2]"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.report1Tooltips"
                                     style="height: calc(100% - 70px); overflow: auto">
                    <el-checkbox
                        v-for="item in pageCtl.report1TooltipsOpts"
                        :key="item"
                        :label="item"
                        :value="item"/>
                  </el-checkbox-group>
                  <div class="box-footer">
                    <el-button
                        @click="report1SubRef.toggleView()">
                      Back
                    </el-button>
                    <el-button
                        type="primary"
                        @click="report1SubRef.toggleView(); searchReport1()">
                      Search
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12" v-loading="pageCtl.loading.report2">
            <div class="subscript-container subscript-container-right" style="height: 350px;">
              <scp-subscript id="PMSB" ref="report2SubRef"/>
              <div class="front">
                <chart ref="report2ChartRef" :height="350" :option="_report2Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">
                    Filter For PMS
                  </div>
                  <el-row>
                    <el-col :span="6">xAixs</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.report2ShowType">
                        <el-option label="View By Value" value="VIEW_BY_VALUE"/>
                        <el-option label="View By Percentage" value="VIEW_BY_PERCENT"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">yAixs</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.report2SelectedType">
                        <el-option label="View By Day" value="VIEW_BY_DAY"/>
                        <el-option label="View By Week" value="VIEW_BY_WEEK"/>
                        <el-option label="View By Month" value="VIEW_BY_MONTH"/>
                        <el-option label="View By Quarter" value="VIEW_BY_QUARTER"/>
                        <el-option label="View By Year" value="VIEW_BY_YEAR"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Stack By</el-col>
                    <el-col :span="18">
                      <el-select
                          v-model="pageCtl.conditions.report2ViewType" filterable>
                        <el-option
                            v-for="item in _pivotColumns.concat(['PMS_CREATED_DATE']).sort()"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">
                      Avg Points
                    </el-col>
                    <el-col :span="18">
                      <el-select
                          v-model="pageCtl.conditions.report2AvgPoints" filterable>
                        <el-option
                            v-for="item in pageCtl.conditions.report2AvgPointsOpts"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6"></el-col>
                    <el-col :span="18"></el-col>
                    <el-col :span="6"></el-col>
                    <el-col :span="18"></el-col>
                  </el-row>
                </div>
                <div class="box-footer">
                  <el-button
                      @click="report2SubRef.toggleView()">
                    Back
                  </el-button>
                  <el-button
                      type="primary"
                      @click="report2SubRef.toggleView();searchReport2()">
                    Search
                  </el-button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report3">
            <div class="subscript-container">
              <scp-subscript id="PMSC" ref="report3SubRef"/>
              <el-row class="search-box">
                <el-col :span="5">
                  <el-select v-model="pageCtl.conditions.report3SelectedColumns" placeholder="Columns"
                             multiple collapse-tags clearable filterable>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report3ViewType">
                    <el-option label="View By Day" value="VIEW_BY_DAY"/>
                    <el-option label="View By Week" value="VIEW_BY_WEEK"/>
                    <el-option label="View By Month" value="VIEW_BY_MONTH"/>
                    <el-option label="View By Quarter" value="VIEW_BY_QUARTER"/>
                    <el-option label="View By Year" value="VIEW_BY_YEAR"/>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-table
                  ref="report3TableRef"
                  url="/demand/pms_structure/query_report3"
                  :params="pageCtl.conditions"
                  :lazy="true"
                  :show-total="true"
                  :pagging="false"
                  :fixed-columns-left="2"
                  :max-height="375"
                  :context-menu-items="pageCtl.report3ContextItems"
                  :after-select="afterReport3Select"
                  :columns="pageCtl.report3Columns">
              </scp-table>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>

  <v-contextmenu ref="report1ContextmenuRef">
    <v-contextmenu-item @click="viewParent" v-show="pageCtl.selectedParentLevel !== ''">
      View {{ pageCtl.selectedParentLevel }}
    </v-contextmenu-item>
    <v-contextmenu-item @click="viewCurrent">
      View {{ pageCtl.selectedCurrentLevel }}
    </v-contextmenu-item>
  </v-contextmenu>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $shortenNumber: any = inject('$shortenNumber')
const $px2Rem: any = inject('$px2Rem')
const $axios: any = inject('$axios')
const $dateFormatter: any = inject('$dateFormatter')
const $toFixed: any = inject('$toFixed')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $join: any = inject('$join')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')
const $tooltip: any = inject('$echarts.tooltip')
const $downloadFile: any = inject('$downloadFile')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report1ContextmenuRef = ref()
const report1SubRef = ref()
const report2ChartRef = ref()
const report2SubRef = ref()
const report3TableRef = ref()

const _report1Opt = computed(() => {
  return {
    title: {
      text: pageCtl.report1TitleText
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: $tooltip({
      callback: (e) => {
        pageCtl.selectedCurrentLevel = e.selectedCurrentLevel
        pageCtl.selectedParentLevel = e.selectedParentLevel
      }
    }, pageCtl.report1Data),
    series: [{
      name: pageCtl.report1RootName,
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report1Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})

const unitToDays = {
  D: 1,
  W: 7,
  M: 30,
  Y: 365
}

const parseTimeUnit = (str) => {
  const match = str.match(/(\d+)([DWMY])/)
  if (match) {
    const value = parseInt(match[1], 10)
    const unit = match[2]
    return value * unitToDays[unit]
  }
  return null
}

const sortList = (list) => {
  list.sort((a, b) => {
    const aIsTimeUnit = parseTimeUnit(a) !== null
    const bIsTimeUnit = parseTimeUnit(b) !== null

    if (aIsTimeUnit && bIsTimeUnit) {
      return parseTimeUnit(a) - parseTimeUnit(b)
    }

    if (aIsTimeUnit && !bIsTimeUnit) {
      return -1
    }

    if (!aIsTimeUnit && bIsTimeUnit) {
      return 1
    }

    return a.localeCompare(b)
  })
  return list
}

const saveReport2DisplayData = (dict) => {
  pageCtl.conditions.report2DisplayData = dict
}

const downloadReport2PageData = () => {
  $downloadFile('/demand/pms_structure/download_report2_display_data', pageCtl.conditions)
}

const _report2Opt = computed(() => {
  const report2DisplayData: any = {}
  const series: any = []
  const legend: any = []
  let yAxisData = pageCtl.report2Data
  if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
    yAxisData = {} as any
    const stackTotal = {}
    const xAxis = pageCtl.report2Data.xAxis
    for (const key in pageCtl.report2Data) {
      if (pageCtl.report2Data.hasOwnProperty(key) && key !== 'xAxis') {
        for (let i = 0; i < xAxis.length; i++) {
          const x = xAxis[i]
          stackTotal[x] = (stackTotal[x] || 0) + pageCtl.report2Data[key][i]
        }
      }
    }
    for (const key in pageCtl.report2Data) {
      if (pageCtl.report2Data.hasOwnProperty(key) && key !== 'xAxis') {
        yAxisData[key] = []
        for (let i = 0; i < xAxis.length; i++) {
          const total = stackTotal[xAxis[i]]
          const v = pageCtl.report2Data[key][i]
          if (v && total) {
            yAxisData[key].push($toFixed(v * 100 / total, 3))
          } else {
            yAxisData[key].push(null)
          }
        }
      }
    }
  }
  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: pageCtl.conditions.report2SeriesType,
        smooth: false,
        stack: 'sum',
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }
  stepLineDisplay(0)
  report2DisplayData.yAxisData = yAxisData
  const avgLegendName = 'AVG of ' + (pageCtl.conditions.report2AvgPoints > 0 ? pageCtl.conditions.report2AvgPoints + ' ' : '') + 'Points'
  const movAvgLegendName = 'Moving AVG of ' + (pageCtl.conditions.report2AvgPoints > 0 ? pageCtl.conditions.report2AvgPoints + ' ' : '') + 'Points'
  if (legend[0] === 'PMS_CREATED_DATE' && legend.length === 1) {
    stepLineDisplay(1)
    const avgTempData: any = []
    for (let j = 0; j < series.length; j++) {
      const data = series[0].data
      const newData: any = []
      let total = 0
      let l = 0
      for (let k = 0; k < series[0].data.length; k++) {
        if (l < pageCtl.conditions.report2AvgPoints) {
          total += data[k]
          l++
        } else {
          for (let p = 0; p < l; p++) {
            newData.push(total / l)
          }
          l = 1
          total = data[k]
        }
      }
      if (l > 0) {
        for (let p = 0; p < l; p++) {
          newData.push(total / l)
        }
      }
      avgTempData.push(newData)
    }
    legend.push(avgLegendName)
    series.push({
      name: avgLegendName,
      type: pageCtl.conditions.report2SeriesType,
      data: avgTempData[0],
      color: 'green',
      lineStyle: {
        type: 'dashed',
        color: 'green'
      }
    })
    report2DisplayData.avgTempData = avgTempData[0]

    // 添加moving average line
    const movAvgTempData: any = []
    for (let j = 0; j < series.length; j++) {
      const data = series[j].data // 当前系列的数据
      const newData: any = [] // 存储计算后的移动平均值
      const N = pageCtl.conditions.report2AvgPoints // 移动平均的窗口大小

      for (let k = 0; k < data.length; k++) {
        // 检查是否有足够的点数来计算移动平均
        if (N === 0) {
          newData.push(data[k])
        } else if (k + N <= data.length) {
          let total = 0
          // 计算从当前点开始的N个点的总和
          for (let p = 0; p < N; p++) {
            total += data[k + p]
          }
          // 计算平均值并存入新数组
          newData.push(total / N)
        } else {
          // 如果剩余点数不足N，则置空
          newData.push(null)
        }
      }

      // 将计算后的移动平均数据存入临时数组
      movAvgTempData.push(newData)
    }
    legend.push(movAvgLegendName)
    series.push({
      name: movAvgLegendName,
      type: pageCtl.conditions.report2SeriesType,
      data: movAvgTempData[0],
      color: '#ed7d31',
      lineStyle: {
        type: 'dashed',
        color: '#ed7d31'
      }
    })
    report2DisplayData.movAvgTempData = movAvgTempData[0]
    // 仅在PMS Create Date中生效
    saveReport2DisplayData(report2DisplayData)
  }
  const titleMap = {
    VIEW_BY_DAY: 'Date',
    VIEW_BY_WEEK: 'Week',
    VIEW_BY_MONTH: 'Month',
    VIEW_BY_QUARTER: 'Quarter',
    VIEW_BY_YEAR: 'Year'
  }
  sortList(legend)
  const newLegend = {}
  for (const t in pageCtl.conditions.report2LegendSelected) {
    const regexMov = /^Moving AVG of (\d+) Points$/
    const regexAvg = /^AVG of (\d+) Points$/
    if (regexAvg.test(t)) {
      newLegend[avgLegendName] = pageCtl.conditions.report2LegendSelected[t]
    } else if (regexMov.test(t)) {
      newLegend[movAvgLegendName] = pageCtl.conditions.report2LegendSelected[t]
    } else {
      newLegend[t] = pageCtl.conditions.report2LegendSelected[t]
    }
  }
  return {
    title: {
      text: pageCtl.report2TitleText + (titleMap[pageCtl.conditions.report2SelectedType] || 'Date') + (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:9.5rem;">')
          const marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
          tip.push('%')
        }
        tip.push('</span></div>')

        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({
      data: legend,
      selected: newLegend
    }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: pageCtl.conditions.report2SeriesType === 'line' ? false : [0, '10%'],
      data: pageCtl.report2Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    toolbox: $toolbox({
      opts: ['line', 'stack', 'no-details'],
      custom: {
        myTool1: {
          show: pageCtl.report2StepLineDisplay,
          title: 'Average Points Plus',
          // 阿里图标库直接使用symbol引入自动生成路径 ，看path路径
          icon: 'path://M675.328 117.717333A425.429333 425.429333 0 0 0 512 85.333333C276.352 85.333333 85.333333 276.352 85.333333 512s191.018667 426.666667 426.666667 426.666667 426.666667-191.018667 426.666667-426.666667c0-56.746667-11.093333-112-32.384-163.328a21.333333 21.333333 0 0 0-39.402667 16.341333A382.762667 382.762667 0 0 1 896 512c0 212.074667-171.925333 384-384 384S128 724.074667 128 512 299.925333 128 512 128c51.114667 0 100.8 9.984 146.986667 29.12a21.333333 21.333333 0 0 0 16.341333-39.402667zM490.666667 490.666667h-149.482667A21.269333 21.269333 0 0 0 320 512c0 11.861333 9.493333 21.333333 21.184 21.333333H490.666667v149.482667c0 11.690667 9.557333 21.184 21.333333 21.184 11.861333 0 21.333333-9.493333 21.333333-21.184V533.333333h149.482667A21.269333 21.269333 0 0 0 704 512c0-11.861333-9.493333-21.333333-21.184-21.333333H533.333333v-149.482667A21.269333 21.269333 0 0 0 512 320c-11.861333 0-21.333333 9.493333-21.333333 21.184V490.666667z',
          iconStyle: {
            color: 'var(--scp-text-color-secondary)',
            borderColor: 'var(--scp-text-color-secondary)',
            borderWidth: 0.5,
            borderType: 'solid'
          },
          onclick: () => {
            pageCtl.conditions.report2AvgPoints += 1
          }
        },
        myTool2: {
          show: pageCtl.report2StepLineDisplay,
          title: 'Average Points Minus',
          // 阿里图标库直接使用symbol引入自动生成路径 ，看path路径
          icon: 'path://M675.328 117.717333A425.429333 425.429333 0 0 0 512 85.333333C276.352 85.333333 85.333333 276.352 85.333333 512s191.018667 426.666667 426.666667 426.666667 426.666667-191.018667 426.666667-426.666667c0-56.746667-11.093333-112-32.384-163.328a21.333333 21.333333 0 0 0-39.402667 16.341333A382.762667 382.762667 0 0 1 896 512c0 212.074667-171.925333 384-384 384S128 724.074667 128 512 299.925333 128 512 128c51.114667 0 100.8 9.984 146.986667 29.12a21.333333 21.333333 0 0 0 16.341333-39.402667zM320 512c0-11.776 9.493333-21.333333 21.184-21.333333h341.632c11.690667 0 21.184 9.472 21.184 21.333333 0 11.776-9.493333 21.333333-21.184 21.333333H341.184A21.205333 21.205333 0 0 1 320 512z',
          iconStyle: {
            color: 'var(--scp-text-color-secondary)',
            borderColor: 'var(--scp-text-color-secondary)',
            borderWidth: 0.5,
            borderType: 'solid'
          },
          onclick: () => {
            if (pageCtl.conditions.report2AvgPoints >= 1) {
              pageCtl.conditions.report2AvgPoints -= 1
            }
          }
        },
        myTool3: {
          show: pageCtl.report2StepLineDisplay,
          title: 'Download Page Data',
          // 阿里图标库直接使用symbol引入自动生成路径 ，看path路径
          icon: 'path://M495.789763 889.064197a21.271931 21.271931 0 0 0 16.91503 8.457515h0.384432a21.271931 21.271931 0 0 0 15.121012-6.279064l384.432486-384.432487a21.400075 21.400075 0 0 0 0-30.242022 22.040796 22.040796 0 0 0-30.754599 0L532.823426 824.992116V21.784508a21.784508 21.784508 0 0 0-43.569015 0v802.438743L142.624453 476.568139a20.759354 20.759354 0 0 0-29.729446 0 21.400075 21.400075 0 0 0 0.256288 30.242022zM1003.881367 981.584282H21.400075a21.143787 21.143787 0 1 0 0 42.287574h982.481292a21.143787 21.143787 0 1 0 0-42.287574z',
          iconStyle: {
            color: 'var(--scp-text-color-secondary)',
            borderColor: 'var(--scp-text-color-secondary)',
            borderWidth: 0.5,
            borderType: 'solid'
          },
          onclick: () => {
            downloadReport2PageData()
          }
        }
      }
    }),
    series
  }
})

const _report3SelectedColumns = computed(() => {
  if (pageCtl.conditions.report3SelectedColumns.length > 0) {
    return pageCtl.conditions.report3SelectedColumns
  } else {
    return ['BU']
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts, 'MATERIAL', 'SOLD_TO')
})

const stepLineDisplay = (enter) => {
  pageCtl.report2StepLineDisplay = enter === 1
}

const viewReport3Details = () => {
  let title = $join(
    pageCtl.conditions.report3SelectedDate,
    pageCtl.conditions.report3SelectedValues
  )
  if (title) {
    title = ' [' + title + ']'
  }

  $viewDetails({
    title: 'View Details' + title,
    url: '/demand/pms_structure/query_report3_details',
    durl: '/demand/pms_structure/download_report3_details',
    params: pageCtl.conditions
  })
}

const pageCtl = reactive({
  loading: {
    filter: false,
    report1: false,
    report2: false,
    report3: false
  },
  report1Data: [],
  report1RootName: 'PMS Structure',
  report1TitleText: 'PMS Structure by Category',
  dateColumnOpts: [],
  filterOpts: [],
  selectedParentLevel: '',
  selectedCurrentLevel: '',
  report1TooltipsOpts: ['NET_NET_VALUE_RMB', 'LINE'] as any,
  report2Data: [] as any,
  report2StepLineDisplay: false,
  report2TitleText: 'Evolution of PMS Structure by ',
  report3Columns: [],
  report3ContextItems: {
    view_details: {
      name: 'View details',
      callback: viewReport3Details
    },
    view_split0: { name: '---------' }
  },
  orderInTakeReport1TooltipsOpts: ['NET_NET_VALUE_RMB', 'LINE'],
  billingReport1TooltipsOpts: ['NET_NET_VALUE_RMB', 'LINE'],
  conditions: {
    dateColumn: 'PMS_CREATED_DATE',
    $scpFilter: {
      cascader: [
        ['IS_DELETED', '0']
      ],
      filter: []
    },
    scope: 'SECI',
    subScope: [],
    level1: 'ENTITY',
    level2: 'ENTITY',
    level3: 'ENTITY',
    level4: 'ENTITY',
    level5: 'ENTITY',
    leafDepth: 1,
    overviewDateRange: [] as any,
    resultType: 'Line',
    medianPosition: 50,
    resultTypeOpts: [
      { value: 'Line', label: 'Line' },
      { value: 'Quantity', label: 'Quantity' },
      { value: 'Net Net Value', label: 'Net Net Value' }
    ],
    report1Tooltips: [],
    report2ShowType: 'VIEW_BY_VALUE',
    report2SelectedType: 'VIEW_BY_DAY',
    report2ViewType: 'PMS_CREATED_DATE',
    report2SelectedValue: '',
    report2DetailsDateRangeDuedate: [] as any,
    report2LegendSelected: {
      'AVG of Points': false,
      'Moving AVG of Points': false
    },
    report2SeriesType: 'line',
    report2AvgPoints: 0,
    selectedTreePath: '',
    report2AvgPointsOpts: [0],
    report3SelectedColumns: ['BU'],
    report3ColumnNames: [],
    report3ViewType: 'VIEW_BY_DAY',
    report3SelectedDate: '',
    report3SelectedValues: [],
    report2DisplayData: []
  },
  report2: {
    report2DisplayData: []
  }
})

onMounted(() => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1)
  const start = new Date(end.getTime() - 86400000 * 31)
  pageCtl.conditions.overviewDateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  report2ChartRef.value.chart().on('dblclick', (obj) => {
    pageCtl.conditions.report2SelectedValue = obj.name
    pageCtl.conditions.report2DetailsDateRangeDuedate = [obj.name, obj.name]

    $viewDetails({
      title: 'View Details [' + [pageCtl.conditions.report2SelectedType, obj.name].join(', ') + ']',
      url: '/demand/pms_structure/query_report2_details',
      durl: '/demand/pms_structure/download_report2_details',
      params: pageCtl.conditions
    })
  })
  report2ChartRef.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      pageCtl.conditions.report2SeriesType = obj.currentType
    }
  })
  report2ChartRef.value.chart().on('legendselectchanged', (obj) => {
    pageCtl.conditions.report2LegendSelected = obj.selected
  })
  initFilter()
})

watch(() => pageCtl.report2Data, () => {
  if (pageCtl.report2Data.xAxis) {
    pageCtl.conditions.report2AvgPointsOpts = [0]
    for (let i = 1; i <= pageCtl.report2Data.xAxis.length; i++) {
      pageCtl.conditions.report2AvgPointsOpts.push(i)
    }
  }
  if (pageCtl.conditions.report2AvgPoints > pageCtl.conditions.report2AvgPointsOpts.length - 1) {
    pageCtl.conditions.report2AvgPoints = pageCtl.conditions.report2AvgPointsOpts.length - 1
  }
})

watch(() => pageCtl.conditions.scope, () => {
  pageCtl.conditions.subScope = []
})

const search = () => {
  pageCtl.conditions.selectedTreePath = ''
  searchReport1()
  searchReport2()
  searchReport3()
}

const initFilter = () => {
  $axios({
    method: 'post',
    url: '/demand/pms_structure/query_filters',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.dateColumnOpts = body.dateColumns
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  })
}

const sumValues = (node) => {
  // 如果有子节点，则递归计算子节点的 value 之和
  if (node.children && node.children.length > 0) {
    let sum = 0
    node.children.forEach(child => {
      sum += sumValues(child)
    })
    node.value = sum // 将子节点的和赋值给当前节点
    node.children.sort((a, b) => b.value - a.value)
  }
  return node.value || 0
}

const calculateValues = (tree) => {
  tree.forEach(node => sumValues(node))
  tree.sort((a, b) => b.value - a.value)
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/demand/pms_structure/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    calculateValues(body)
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/demand/pms_structure/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/demand/pms_structure/query_report3_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report3ColumnNames = body
    pageCtl.report3Columns = parseReport3Columns()
    report3TableRef.value.search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const parseReport3Columns = () => {
  const result = [] as any
  for (let i = 0; i < _report3SelectedColumns.value.length; i++) {
    result.push({
      data: _report3SelectedColumns.value[i],
      render: (hotInstance, td, row, column, prop, value) => {
        if (value === 'Total') {
          td.style.fontWeight = 'bold'
        }
        td.innerHTML = value
      }
    })
  }
  for (let i = 0; i < pageCtl.conditions.report3ColumnNames.length; i++) {
    result.push({
      title: pageCtl.conditions.report3ColumnNames[i],
      data: '\'' + pageCtl.conditions.report3ColumnNames[i] + '\'_TOTAL',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        td.innerHTML = value ? $thousandBitSeparator(value, 0) : '0'
      }
    })
  }
  return result
}

const afterReport3Select = (r, rc, c) => {
  if (c.indexOf('\'') !== -1) {
    pageCtl.conditions.report3SelectedDate = c.split('\'')[1]
  } else {
    pageCtl.conditions.report3SelectedDate = ''
  }
  const selected = [] as any
  for (let i = 0; i < _report3SelectedColumns.value.length; i++) {
    let v = r[_report3SelectedColumns.value[i]]
    if (v === 'Total') {
      v = ''
    }
    selected.push(v)
  }
  pageCtl.conditions.report3SelectedValues = selected
}

const viewParent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedParentLevel
  searchReport2()
  searchReport3()
}

const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedCurrentLevel
  searchReport2()
  searchReport3()
}

</script>

<style lang="scss">
#PmsStructure {
  .medianInput {
    .el-input__inner {
      text-align: center;
    }
  }
}
</style>
