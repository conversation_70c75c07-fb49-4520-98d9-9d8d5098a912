<template>
  <div class="left-sidebar" id="weChatNotification">
    <div class="widget">
      <div class="widget-body">
        <el-container @contextmenu.prevent="">
          <el-aside width="280px" style="border-right: 1px solid var(--scp-border-color);margin-right:10px;min-height: 450px"
                    class="left-container">
            <scp-tree-menu
                ref="treeRef"
                url="/toolbox/wechat_notification/query_wechat_notification_list"
                :new-click="showNewNoticeWin"
                :node-click="clickNode"
            ></scp-tree-menu>
          </el-aside>
          <el-main style="padding: 1px">
            <div v-show="pageCtl.selectedId === ''">
              <h1 style="text-align: center;font-size: 1.6rem; color: var(--scp-text-color-lighter);height:350px;line-height: 350px">WeChat
                Notification</h1>
            </div>
            <el-tabs v-model="pageCtl.currentTab" v-show="pageCtl.selectedId !== ''">
              <el-tab-pane label="Last Execution" name="first">
                <div v-loading="pageCtl.loading.report1">
                  <h5 style="margin: 5px 15px 0 0;font-size: .583rem;display: inline;">{{
                      pageCtl.report1Data['NAME']
                    }}</h5>
                  <el-tag :type="pageCtl.report1Data['STATUS'] === 'Inactive' ? 'danger' : ''" effect="dark"
                          style="margin-right: 5px">
                    {{ pageCtl.report1Data['STATUS'] || '&nbsp;' }}
                  </el-tag>
                  <el-tag :type="(pageCtl.report1Data['LAST_EXEC_RESULT_CODE'] === '0' || typeof pageCtl.report1Data['LAST_EXEC_RESULT_CODE'] === 'undefined') ? '' : 'danger'" effect="dark"
                          style="margin-right: 5px"
                          v-show="_showLastExecLogs">
                    {{ (pageCtl.report1Data['LAST_EXEC_RESULT_CODE'] === '0' || typeof pageCtl.report1Data['LAST_EXEC_RESULT_CODE'] === 'undefined') ? 'No Errors' : 'Interrupted' }}
                  </el-tag>
                  <el-tag type="" effect="dark" style="margin-right: 5px" v-show="_showLastExecLogs">{{
                      pageCtl.report1Data['LAST_EXEC_TIME'] || '&nbsp;'
                    }}
                  </el-tag>
                  <el-tag type="" effect="dark" style="margin-right: 5px">{{
                      pageCtl.report1Data['CORN'] || '&nbsp;'
                    }}
                  </el-tag>
                  <el-tag type="" effect="dark">maintain by {{
                      pageCtl.report1Data['USER_NAME']
                    }}({{ pageCtl.report1Data['SESA_CODE'] }})
                  </el-tag>
                  <hr style="margin: 10px 0 15px 0">
                  <pre style="height: 280px; overflow: auto">{{ pageCtl.report1Data['LOG_FIELD'] }}</pre>
                </div>
                <hr>
                <scp-table
                    ref="report2Ref"
                    url="/toolbox/wechat_notification/query_report2"
                    :lazy="true"
                    :params="{id: pageCtl.selectedId}"
                    :columns="pageCtl.report2TableColumn"
                    :editable="false"
                />
                <hr style="margin-bottom: 1px; margin-top: 3px; opacity: 0">
              </el-tab-pane>
              <el-tab-pane label="Job Info" name="second">
                <div class="new-notice" style="height: var(--scp-input-width);overflow: auto">
                  <el-row>
                    <el-col :span="2" class="title">Job Name</el-col>
                    <el-col :span="12" class="content">
                      <el-input v-model="pageCtl.modifyNotice.name"></el-input>
                    </el-col>
                    <el-col :span="10" class="input-tips">
                      任务名, 尽量用最少的字描述任务内容
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Groups</el-col>
                    <el-col :span="4" class="content">
                      <el-autocomplete
                          class="inline-input"
                          v-model="pageCtl.modifyNotice.groups"
                          :maxlength="30"
                          :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                          placeholder="Group"
                          show-word-limit
                      ></el-autocomplete>
                    </el-col>
                    <el-col :span="18" class="input-tips">
                      任务分组, 可以使用 <b>.</b> 进行层级分类, 如Level.Leve2.Leve3, 这样会生成三层目录
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Status</el-col>
                    <el-col :span="4" class="content">
                      <el-select v-model="pageCtl.modifyNotice.status">
                        <el-option
                            v-for="item in ['Active','Inactive']"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="18" class="input-tips">
                      任务状态, 如果暂时不使用的任务可以设置为Inactive, 确定不再使用的任务, 建议直接删除
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Trigger</el-col>
                    <el-col :span="3" class="content">
                      <el-select v-model="pageCtl.modifyNotice.month" placeholder="Month" filterable collapse-tags
                                 multiple style="width: 100%">
                        <el-option
                            v-for="item in ['*', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="3">
                      <el-select v-model="pageCtl.modifyNotice.dayType" placeholder="Day Type" class="search-group-left"
                                 @change="pageCtl.modifyNotice.day = []"
                                 style="width: 100%">
                        <el-option
                            v-for="item in ['DAY_OF_MONTH', 'DAY_OF_WEEK', 'WORKING_DAY']"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="3" class="content">
                      <el-select v-model="pageCtl.modifyNotice.day" :placeholder="pageCtl.modifyNotice.dayType"
                                 filterable collapse-tags multiple
                                 class="search-group-right" style="width: 100%">
                        <el-option
                            v-for="item in _dayRangeOfModify"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="3" class="content">
                      <el-select v-model="pageCtl.modifyNotice.hour" placeholder="Hour" filterable collapse-tags
                                 multiple style="width: 100%">
                        <el-option
                            v-for="item in _hourRange"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="3" class="content">
                      <el-select v-model="pageCtl.modifyNotice.minute" placeholder="Hour" filterable collapse-tags
                                 multiple style="width: 100%">
                        <el-option
                            v-for="item in _minuteRange"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6" class="input-tips">
                      任务触发配置, 精确到每10分钟
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Sleep</el-col>
                    <el-col :span="4" class="content">
                      <el-input-number v-model="pageCtl.modifyNotice.sleep" controls-position="right" :min="0"
                                       :max="300" style="width: 100%"/>
                    </el-col>
                    <el-col :span="18" class="input-tips">
                      抓取等待, 单位秒, 爬虫无法确认页面何时加载完毕, 只能预设一个等待时间, 在等待后, 执行截图操作,
                      大部分页面15秒足够
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Zoom</el-col>
                    <el-col :span="4" class="content">
                      <el-input-number v-model="pageCtl.modifyNotice.zoom" controls-position="right" :min="0.67"
                                       :max="1.2" :step="0.01" style="width: 100%"/>
                    </el-col>
                    <el-col :span="18" class="input-tips">
                      默认抓取分辨率是1920*1080, 但有些页面内容较多, 可以通过调整zoom的值来缩放页面, 以达到可以在一个页面显示所有内容的目的
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Webhook</el-col>
                    <el-col :span="10" class="content">
                      <el-input v-model="pageCtl.modifyNotice.wechatWebhook" placeholder="WeChat Webhook" style="width: 100%"/>
                    </el-col>
                    <el-col :span="12" class="input-tips">
                      微信WebHook
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">URL</el-col>
                    <el-col :span="10" class="content">
                      <el-input v-model="pageCtl.modifyNotice.url" style="width: 100%"/>
                    </el-col>
                    <el-col :span="12" class="input-tips">
                      需要截图发送的链接, 可以使用DSS的Variant Sharing, 仅支持DSS
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Area ID</el-col>
                    <el-col :span="6" class="content">
                      <el-select v-model="pageCtl.modifyNotice.areaid" placeholder="Area ID" filterable clearable
                                 style="width: 100%">
                        <el-option
                            v-for="item in pageCtl.reportIDList"
                            :key="item['REPORT_ID']"
                            :label="item['REPORT_ID'] + ' [' + item['TITLE'] +']'"
                            :value="item['REPORT_ID']">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="16" class="input-tips">
                      如果需要发送页面某一张报表的截图, 可以选择对应的报表ID. 如果需要整页截图, 此项留空. 报表ID可在角标处查看
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Message</el-col>
                    <el-col :span="12" class="content">
                      <el-input v-model="pageCtl.modifyNotice.msg" style="width: 100%"/>
                    </el-col>
                    <el-col :span="10" class="input-tips">
                      需要随着图片一起发送的文字, 可以不填
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" style="height: 20px;" class="title">Remarks</el-col>
                    <el-col :span="21" class="content">
                      <el-input type="textarea" v-model="pageCtl.modifyNotice.remarks"
                                style="height: auto !important;"></el-input>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="23" style="text-align: right">
                      <el-popconfirm title="确定删除任务?这个操作不可恢复"
                                     iconColor="var(--scp-text-color-error)"
                                     @confirm="deleteNotice"
                                     confirmButtonType="danger"
                                     cancelButtonType="text"
                                     confirmButtonText='确定'
                                     cancelButtonText='取消'>
                        <template #reference>
                          <el-button type="danger" :loading="pageCtl.loading.delete">
                            <font-awesome-icon icon="times"/>&nbsp;
                            Delete
                          </el-button>
                        </template>
                      </el-popconfirm>
                      <el-popconfirm title="确定要执行任务? 这大概需要30秒"
                                     iconColor="orange"
                                     style="margin-left:10px;margin-right:10px"
                                     @confirm="runJob"
                                     confirmButtonType="warning"
                                     confirmButtonText='确定'
                                     cancelButtonText='取消'>
                        <template #reference>
                          <el-button type="primary" :loading="pageCtl.loading.runJob"
                                     :disabled="pageCtl.mqttConnected === false">
                            <font-awesome-icon icon="play" style="font-size: 90%;"/>&nbsp;
                            Run
                          </el-button>
                        </template>
                      </el-popconfirm>
                      <el-button type="primary" @click="modifyCurrentNotice">
                        <font-awesome-icon icon="save"/>&nbsp;
                        Save
                      </el-button>
                    </el-col>
                    <el-col :span="1">&nbsp;</el-col>
                  </el-row>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-main>
        </el-container>
      </div>
    </div>

    <scp-draggable-resizable id="newNotice" w="1200" h="580" v-model="pageCtl.visible.newNotice"
                             title="New WeChat Notification" :save="saveNewNotice">
      <template v-slot="{height}">
        <div class="new-notice" :style="{'height':(height - 100) + 'px'}" style="overflow: auto">
          <el-row>
            <el-col :span="2" class="title">Job Name</el-col>
            <el-col :span="12" class="content">
              <el-input v-model="pageCtl.newNotice.name"></el-input>
            </el-col>
            <el-col :span="10" class="input-tips">
              任务名, 尽量用最少的字描述任务内容
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Groups</el-col>
            <el-col :span="4" class="content">
              <el-autocomplete
                  class="inline-input"
                  v-model="pageCtl.newNotice.groups"
                  :maxlength="30"
                  :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                  placeholder="Group"
                  show-word-limit
              ></el-autocomplete>
            </el-col>
            <el-col :span="18" class="input-tips">
              任务分组, 可以使用 <b>.</b> 进行层级分类, 如Level.Leve2.Leve3, 这样会生成三层目录
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Status</el-col>
            <el-col :span="4" class="content">
              <el-select v-model="pageCtl.newNotice.status">
                <el-option
                    v-for="item in ['Active','Inactive']"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="18" class="input-tips">
              任务状态, 如果暂时不使用的任务可以设置为Inactive, 确定不再使用的任务, 建议直接删除
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Trigger</el-col>
            <el-col :span="4" class="content">
              <el-select v-model="pageCtl.newNotice.month" placeholder="Month" filterable collapse-tags multiple
                         style="width: 100%">
                <el-option
                    v-for="item in ['*', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="3">
              <el-select v-model="pageCtl.newNotice.dayType" placeholder="Day Type"
                         @change="pageCtl.newNotice.day = []" class="search-group-left"
                         style="width: 100%;">
                <el-option
                    v-for="item in ['DAY_OF_MONTH', 'DAY_OF_WEEK', 'WORKING_DAY']"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="3" class="content">
              <el-select v-model="pageCtl.newNotice.day" :placeholder="pageCtl.newNotice.dayType" filterable
                         collapse-tags multiple class="search-group-right"
                         style="width: 100%">
                <el-option
                    v-for="item in _dayRangeOfNew"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="3" class="content">
              <el-select v-model="pageCtl.newNotice.hour" placeholder="Hour" filterable collapse-tags multiple
                         style="width: 100%">
                <el-option
                    v-for="item in _hourRange"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="3" class="content">
              <el-select v-model="pageCtl.newNotice.minute" placeholder="Minute" filterable collapse-tags multiple
                         style="width: 100%">
                <el-option
                    v-for="item in _minuteRange"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="6" class="input-tips">
              任务触发配置, 精确到每10分钟
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Sleep</el-col>
            <el-col :span="4" class="content">
              <el-input-number v-model="pageCtl.newNotice.sleep" controls-position="right" :min="10" :max="90"
                               style="width: 100%"/>
            </el-col>
            <el-col :span="18" class="input-tips">
              抓取等待, 单位秒, 爬虫无法确认页面何时加载完毕, 只能预设一个等待时间, 在等待后, 执行截图操作, 大部分页面15秒足够
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Zoom</el-col>
            <el-col :span="4" class="content">
              <el-input-number v-model="pageCtl.newNotice.zoom" controls-position="right" :min="0.67" :max="1.2"
                               :step="0.01" style="width: 100%"/>
            </el-col>
            <el-col :span="18" class="input-tips">
              默认抓取分辨率是1920*1080, 但有些页面内容较多, 可以通过调整zoom的值来缩放页面, 以达到可以在一个页面显示所有内容的目的
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">WebHook</el-col>
            <el-col :span="10" class="content">
              <el-input v-model="pageCtl.newNotice.wechatWebhook" placeholder="WeChat WebHook" style="width: 100%"/>
            </el-col>
            <el-col :span="12" class="input-tips">
              微信ID, 可以是个人, 也可以是一个群
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">URL</el-col>
            <el-col :span="10" class="content">
              <el-input v-model="pageCtl.newNotice.url" style="width: 100%"/>
            </el-col>
            <el-col :span="12" class="input-tips">
              需要截图发送的链接, 可以使用DSS的Variant Sharing, 仅支持DSS
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Area ID</el-col>
            <el-col :span="6" class="content">
              <el-select v-model="pageCtl.newNotice.areaid" placeholder="Area ID" filterable clearable
                         style="width: 100%">
                <el-option
                    v-for="item in pageCtl.reportIDList"
                    :key="item['REPORT_ID']"
                    :label="item['REPORT_ID'] + ' [' + item['TITLE'] +']'"
                    :value="item['REPORT_ID']">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="16" class="input-tips">
              如果需要发送页面某一张报表的截图, 可以选择对应的报表ID. 如果需要整页截图, 此项留空. 报表ID可在角标处查看
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Message</el-col>
            <el-col :span="12" class="content">
              <el-input v-model="pageCtl.newNotice.msg" style="width: 100%"/>
            </el-col>
            <el-col :span="10" class="input-tips">
              需要随着图片一起发送的文字, 可以不填
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Remarks</el-col>
            <el-col :span="21" class="content">
              <el-input :style="{'padding-bottom':'2px'}" type="textarea"
                        v-model="pageCtl.newNotice.remarks"></el-input>
            </el-col>
          </el-row>
        </div>
      </template>
    </scp-draggable-resizable>

  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onBeforeUnmount, onMounted, reactive, ref } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'

const $message: any = inject('$message')
const $axios: any = inject('$axios')
const $startWith: any = inject('$startWith')
const $randomString: any = inject('$randomString')
const $createMqttClient: any = inject('$createMqttClient')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')
const report2Ref = ref()
const treeRef = ref()

const pageCtl = reactive({
  mqttConnected: false,
  subscribeTopic: 'scp/dss/ui/wechat-notification/' + (localStorage.getItem('username') || '').toLowerCase(),
  publishTopic: 'scp/dss/wechat-notification/execute',
  client: {} as any,
  existsGroup: [] as any,
  reportIDList: [],
  currentTab: 'first',
  loading: {
    report1: false,
    delete: false,
    runJob: false
  },
  visible: {
    tree: true,
    newNotice: false
  },
  newNotice: {
    name: '',
    groups: '',
    dayType: 'DAY_OF_MONTH',
    month: ['*'],
    day: [],
    hour: [],
    minute: [],
    status: 'Active',
    remarks: '',
    sleep: 15,
    zoom: 1,
    url: '',
    wechatWebhook: '',
    msg: '',
    areaid: ''
  },
  modifyNotice: {
    id: '',
    name: '',
    groups: '',
    dayType: 'DAY_OF_MONTH',
    month: ['*'],
    day: [],
    hour: [],
    minute: [],
    status: 'Active',
    remarks: '',
    sleep: 15,
    zoom: 1,
    url: '',
    wechatWebhook: '',
    msg: '',
    areaid: ''
  },
  report1Data: {} as any,
  selectedId: '',
  selectedName: '',
  report2TableColumn: [
    { data: 'SEND_TIME' },
    { data: 'SEND_TO' },
    { data: 'RESULT_CODE' },
    { data: 'RESULT_TEXT' }
  ]
})

const _showLastExecLogs = computed(() => {
  return pageCtl.report1Data.LAST_EXEC_RESULT_CODE !== ''
})
const _dayRangeOfNew = computed(() => {
  const result = ['*']
  if (pageCtl.newNotice.dayType === 'DAY_OF_MONTH') {
    for (let i = 1; i <= 31; i++) {
      result.push(i + '')
    }
  } else if (pageCtl.newNotice.dayType === 'DAY_OF_WEEK') {
    for (let i = 1; i <= 7; i++) {
      result.push(i + '')
    }
  } else if (pageCtl.newNotice.dayType === 'WORKING_DAY') {
    return []
  }
  return result
})
const _dayRangeOfModify = computed(() => {
  const result = ['*']
  if (pageCtl.modifyNotice.dayType === 'DAY_OF_MONTH') {
    for (let i = 1; i <= 31; i++) {
      result.push(i + '')
    }
  } else if (pageCtl.modifyNotice.dayType === 'DAY_OF_WEEK') {
    for (let i = 1; i <= 7; i++) {
      result.push(i + '')
    }
  } else if (pageCtl.modifyNotice.dayType === 'WORKING_DAY') {
    return []
  }
  return result
})
const _hourRange = computed(() => {
  const result = [] as any
  for (let i = 0; i <= 23; i++) {
    result.push(i + '')
  }
  return result
})

const _minuteRange = computed(() => {
  const result = [] as any
  for (let i = 0; i <= 50; i = i + 10) {
    result.push(i + '')
  }
  return result
})

const connectMqtt = () => {
  pageCtl.client = $createMqttClient('scp-ui-wechat-notification')
  pageCtl.client.on('connect', () => {
    pageCtl.client.subscribe(pageCtl.subscribeTopic, { qos: 2 }, (err) => {
      if (!err) {
        pageCtl.mqttConnected = true
        console.log('subscribe topic: ' + pageCtl.subscribeTopic)
      } else {
        pageCtl.mqttConnected = false
        $message.error('mqtt connect failed, ' + err)
      }
    })
  })
  pageCtl.client.on('message', (topic, message) => {
    const messageStr = '' + message
    if ($startWith(messageStr, '[error]') === true) {
      $message.error(messageStr)
    } else {
      $message.success(messageStr)
      search()
    }
    pageCtl.loading.runJob = false
  })
}

const initPage = () => {
  $axios({
    method: 'post',
    url: '/toolbox/wechat_notification/init_page'
  }).then((body) => {
    pageCtl.existsGroup = body.existsGroup
    pageCtl.reportIDList = body.reportIDList
  }).catch((error) => {
    console.log(error)
  })
}

const clickNode = (e) => {
  pageCtl.selectedId = e.key
  pageCtl.selectedName = e.label
  search()
}

const search = () => {
  searchReport1()
  searchReport2()
}
const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/toolbox/wechat_notification/query_report1',
    data: {
      id: pageCtl.selectedId
    }
  }).then((body) => {
    pageCtl.report1Data = body

    pageCtl.modifyNotice.id = body.id
    pageCtl.modifyNotice.name = body.NAME
    pageCtl.modifyNotice.groups = (body.GROUP_NAME || '')
    pageCtl.modifyNotice.dayType = body.DAY_TYPE
    pageCtl.modifyNotice.month = JSON.parse(body.MONTH)
    pageCtl.modifyNotice.day = JSON.parse(body.DAY)
    pageCtl.modifyNotice.hour = JSON.parse(body.HOUR)
    pageCtl.modifyNotice.minute = JSON.parse(body.MINUTE)
    pageCtl.modifyNotice.status = body.STATUS
    pageCtl.modifyNotice.remarks = body.REMARKS
    pageCtl.modifyNotice.sleep = body.sleep
    pageCtl.modifyNotice.zoom = body.zoom
    pageCtl.modifyNotice.url = body.url
    pageCtl.modifyNotice.wechatWebhook = body.wechat_webhook
    pageCtl.modifyNotice.msg = body.msg
    pageCtl.modifyNotice.areaid = body.areaid
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}
const searchReport2 = () => {
  nextTick(() => {
    report2Ref.value.search()
  })
}

// region operate_notice_job
const showNewNoticeWin = () => {
  pageCtl.visible.newNotice = true
}
const saveNewNotice = () => {
  if (pageCtl.newNotice.name === '') {
    $message.error('Job name invalid')
  } else if (pageCtl.newNotice.url === '') {
    $message.error('URL invalid')
  } else if (pageCtl.newNotice.wechatWebhook === '') {
    $message.error('WeChat Webhook invalid')
  } else if (pageCtl.modifyNotice.groups === '') {
    $message.error('Groups cannot be empty')
  } else {
    $axios({
      method: 'post',
      url: '/toolbox/wechat_notification/save_notice',
      data: pageCtl.newNotice
    }).then(() => {
      initPage()
      loadTree()
      pageCtl.visible.newNotice = false
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.report1 = false
    })
  }
}
const modifyCurrentNotice = () => {
  if (pageCtl.modifyNotice.name === '') {
    $message.error('Job name invalid')
  } else if (pageCtl.modifyNotice.url === '') {
    $message.error('URL invalid')
  } else if (pageCtl.modifyNotice.wechatWebhook === '') {
    $message.error('WeChat Webhook invalid')
  } else if (pageCtl.modifyNotice.groups === '') {
    $message.error('Groups cannot be empty')
  } else {
    $axios({
      method: 'post',
      url: '/toolbox/wechat_notification/modify_notice',
      data: pageCtl.modifyNotice
    }).then(() => {
      initPage()
      loadTree()
      search()
      $message.success('Job saved')
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.report1 = false
    })
  }
}
const deleteNotice = () => {
  pageCtl.loading.delete = true
  $axios({
    method: 'post',
    url: '/toolbox/wechat_notification/delete_notice',
    data: {
      id: pageCtl.selectedId
    }
  }).then(() => {
    initPage()
    loadTree()
    pageCtl.selectedId = ''
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.delete = false
  })
}
const runJob = () => {
  pageCtl.loading.runJob = true
  const content = { topic: pageCtl.subscribeTopic, id: pageCtl.selectedId }
  pageCtl.client.publish(pageCtl.publishTopic, JSON.stringify(content))
}
const loadTree = () => {
  treeRef.value.search()
}
// endregion

onMounted(() => {
  initPage()
  connectMqtt()
})

const closeMqttClient = () => {
  try {
    pageCtl.client.end()
  } catch (e) {

  }
}

onBeforeUnmount(() => {
  closeMqttClient()
})

onBeforeRouteLeave((to, from, next) => {
  closeMqttClient()
  next()
})

window.onbeforeunload = () => {
  closeMqttClient()
}

</script>

<style lang="scss">
#weChatNotification {
  .new-notice {
    padding-top: 15px;
    padding-left: 10px;

    .title {
      text-align: right;
      padding-right: 8px;
      font-weight: bold;
      line-height: 2;
    }

    .content {
      padding-right: 5px;
    }

    .el-row {
      margin-bottom: 12px;
    }
  }

  .input-tips {
    padding-left: 5px;
    font-style: italic;
    color: var(--scp-text-color-secondary);
    line-height: 2;
  }
}
</style>
