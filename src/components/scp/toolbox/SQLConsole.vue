<template>
  <div class="left-sidebar" id="SQLConsole">
    <div class="widget">
      <div class="widget-body">
        <el-container @contextmenu.prevent="">
          <el-aside width="280px" style="border-right: 1px solid var(--scp-border-color);margin-right:10px;" :style="{height: _initHeight + 'px'}" class="left-container" v-show="pageCtl.visible.tree">
            <scp-tree-menu
              ref="treeRef"
              url="/toolbox/sql_console/query_table_list"
              :node-click="clickNode"
            ></scp-tree-menu>
          </el-aside>
          <el-main style="padding: 0" :style="{height:_initHeight+'px'}">
            <scp-ace-editor v-model="pageCtl.conditions.sql" lang="sql" :editor-init="editorInit" style="height: 550px" :enableLiveAutocompletion="pageCtl.aceOptions.enableLiveAutocompletion"></scp-ace-editor>
            <el-button @click="toggleTable" style="margin-top:20px">
              <font-awesome-icon :icon="pageCtl.treeClass"/>
            </el-button>
            <el-button @click="clearSQL" style="margin-top:20px">
              <font-awesome-icon icon="eraser"/>
            </el-button>
            <el-button @click="formatSQL" style="margin-top:20px" :loading="pageCtl.loading.format">
              <font-awesome-icon icon="align-left"/>&nbsp;
              Format SQL
            </el-button>
            <el-button @click="search" style="margin-top:20px" :loading="pageCtl.loading.execute" type="primary">
              <font-awesome-icon icon="search"/>&nbsp;
              Execute SQL
            </el-button>
            <div v-if="pageCtl.errorMessage.length > 0">
              <hr>
              <pre style="color: var(--scp-text-color-error)">{{ pageCtl.errorMessage }}</pre>
            </div>
            <hr>
            <el-tabs v-model="pageCtl.activeTab">
              <el-tab-pane label="Results" name="first">
                <scp-table
                  :max-height="300"
                  ref="report1Ref"
                  :lazy="true"
                  url="/toolbox/sql_console/query_report1"
                  download-url="/toolbox/sql_console/download_report1"
                  :params="pageCtl.conditions"
                  :columns="pageCtl.columns"
                  :download-specify-column="false"
                  v-loading="pageCtl.loading.execute"
                  :after-search="afterReport1Search"
                  :editable="false"
                />
                <hr style="margin-bottom: 0; margin-top: 5px">
              </el-tab-pane>
              <el-tab-pane label="Query History" name="second">
                <div v-show="pageCtl.queryHistoryData.length > 0">
                  <el-table :data="_queryHistoryData" :style="{width: pageCtl.width + 'px'}">
                    <el-table-column type="index" width="50"/>
                    <el-table-column prop="state" label="State" width="100">
                      <template v-slot="scope">
                        <font-awesome-icon icon="check" style="color: var(--scp-text-color-success)" v-show="scope.row.state === '200'"/>
                        <font-awesome-icon icon="times" style="color: var(--scp-text-color-error)" v-show="scope.row.state === '500'"/>
                      </template>
                    </el-table-column>
                    <el-table-column prop="started" label="Started" width="180"/>
                    <el-table-column prop="duration" label="Duration" width="180">
                      <template v-slot="scope">
                        {{ secondsToHms(scope.row.duration) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="rows" label="Rows" width="100">
                      <template v-slot="scope">
                        {{ $thousandBitSeparator(scope.row.rows) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="sql" label="SQL">
                      <template v-slot="scope">
                        <scp-ace-editor v-model="scope.row.sql" lang="sql" width="auto" style="height: 100px" :readOnly="pageCtl.aceOptions2.readOnly"></scp-ace-editor>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <div v-show="pageCtl.queryHistoryData.length === 0">
                  No stored results found, you need to re-run your query
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-main>
        </el-container>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import keywords from '@/assets/js/oracle-keywords'
import { computed, inject, onBeforeMount, reactive, ref } from 'vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'

const $axios:any = inject('$axios')
const $dateFormatter:any = inject('$dateFormatter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const report1Ref = ref()
const treeRef = ref()

const _initHeight = computed(() => {
  return document.documentElement.clientHeight - 80
})

const pageCtl = reactive({
  activeTab: 'first',
  width: 0,
  conditions: {
    sql: ''
  },
  queryLog: {
    state: '',
    started: '',
    duration: '' as any,
    rows: 0,
    sql: ''
  },
  queryHistoryData: [] as any,
  columns: [],
  errorMessage: '',
  loading: {
    execute: false,
    format: false
  },
  visible: {
    tree: false
  },
  treeClass: 'caret-right',
  cachekey: 'sql-console.history',
  aceOptions: {
    enableLiveAutocompletion: true
  },
  aceOptions2: {
    readOnly: true
  },
  completions: []
})

const _queryHistoryData = computed(() => {
  const result = [] as any
  for (let i = pageCtl.queryHistoryData.length - 1; i >= 0; i--) {
    result.push(pageCtl.queryHistoryData[i])
  }
  return result
})

const search = () => {
  pageCtl.errorMessage = ''
  pageCtl.loading.execute = true
  pageCtl.queryLog.started = $dateFormatter(new Date(), 'yyyy/MM/dd hh:mm:ss')
  pageCtl.queryLog.duration = new Date().getTime()
  pageCtl.queryLog.sql = pageCtl.conditions.sql
  $axios({
    method: 'post',
    url: '/toolbox/sql_console/query_report1_headers',
    data: pageCtl.conditions
  }).then((body) => {
    if (typeof body === 'string') {
      pageCtl.errorMessage = body
      pageCtl.loading.execute = false
      report1Ref.value.clearData()
      addToHistory('500')
    } else {
      const columns = [] as any
      for (let i = 0; i < body.length; i++) {
        columns.push({ data: body[i] })
      }
      pageCtl.columns = columns
      report1Ref.value.search()
    }
  }).catch((error) => {
    addToHistory('500')
    console.log(error)
  })
}

const addToHistory = (state) => {
  pageCtl.queryLog.state = state
  pageCtl.queryLog.rows = report1Ref.value.getTotalCount()
  pageCtl.queryLog.duration = (new Date().getTime() - pageCtl.queryLog.duration) / 1000.0
  pageCtl.queryHistoryData.push(pageCtl.queryLog)
  while (pageCtl.queryHistoryData.length > 10) {
    pageCtl.queryHistoryData.shift()
  }
  localStorage.setItem(pageCtl.cachekey, JSON.stringify(pageCtl.queryHistoryData))
  pageCtl.queryLog = {
    state: '',
    started: '',
    duration: 0,
    rows: 0,
    sql: ''
  }
}

const formatSQL = () => {
  pageCtl.loading.format = true
  $axios({
    method: 'post',
    url: '/home/<USER>/format_sql',
    data: {
      scripts: pageCtl.conditions.sql
    }
  }).then((body) => {
    if (body) {
      pageCtl.conditions.sql = body
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.format = false
  })
}

const clearSQL = () => {
  pageCtl.conditions.sql = ''
}

const afterReport1Search = () => {
  pageCtl.loading.execute = false
  addToHistory('200')
}

const editorInit = (editor) => {
  editor.commands.addCommand({
    name: 'formatter',
    bindKey: { win: 'Ctrl-Shift-F', mac: 'Command-Shift-F' },
    exec: () => formatSQL()
  })
  // 加入自定义语法提示
  editor.completers = [{
    getCompletions: (editor, session, pos, prefix, callback) => {
      setCompletions(editor, session, pos, prefix, callback)
    }
  }]
}

const setCompletions = (editor, session, pos, prefix, callback) => {
  // 这里只是举例子，具体实现要看自己需求
  if (prefix.length === 0) {
    return callback(null, [])
  } else {
    return callback(null, pageCtl.completions)
  }
}

const toggleTable = () => {
  pageCtl.visible.tree = !pageCtl.visible.tree
  if (pageCtl.visible.tree) {
    pageCtl.treeClass = 'caret-left'
  } else {
    pageCtl.treeClass = 'caret-right'
  }
}

const clickNode = (e) => {
  const table = e.key
  if (pageCtl.conditions.sql) {
    pageCtl.conditions.sql = pageCtl.conditions.sql + '\nSELECT * FROM ' + table
  } else {
    pageCtl.conditions.sql = 'SELECT * FROM ' + table
  }
}

const secondsToHms = (seconds) => {
  const ms = (seconds - Math.floor(seconds)).toFixed(3)
  seconds = Math.floor(seconds)
  const h = Math.floor(seconds / 3600)
  const m = Math.floor(seconds % 3600 / 60)
  const s = Math.floor(seconds % 3600 % 60)

  const hDisplay = h > 9 ? '' + h : '0' + h
  const mDisplay = m > 9 ? '' + m : '0' + m
  const sDisplay = s > 9 ? '' + s : '0' + s
  const msDisplay = ms.split('.')[1]
  return hDisplay + ':' + mDisplay + ':' + sDisplay + '.' + msDisplay
}

onBeforeMount(() => {
  pageCtl.width = document.documentElement.clientWidth - 50
  pageCtl.queryHistoryData = JSON.parse(localStorage.getItem(pageCtl.cachekey) || '[]')
  $axios({
    method: 'post',
    url: '/toolbox/sql_console/query_table_cols'
  }).then((body) => {
    const tables = body.tables
    const cols = body.cols
    const completions = [] as any

    for (const i in tables) {
      completions.push({ caption: tables[i], meta: 'table', value: 'SCPA.' + tables[i] })
    }

    for (const i in cols) {
      completions.push({ caption: cols[i], meta: 'column', value: cols[i] })
    }

    const keys = Object.keys(keywords)
    for (const index in keys) {
      const key = keys[index]
      const list = keywords[key]
      for (const i in list) {
        const value = list[i]
        completions.push({ caption: value, meta: key, value })
      }
    }
    completions.sort((e1, e2) => e1.caption.localeCompare(e2.caption))
    pageCtl.completions = completions
  }).catch((error) => {
    console.log(error)
  })
})

</script>

<style lang="scss">
.ace_autocomplete {
  width: 400px !important;
}

#SQLConsole {
  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-fill-lighter) !important;
  }
}
</style>
