<template>
  <div class="left-sidebar" id="approvalApplicationForm">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
              <scp-cascader v-model="pageCtl.appID" :options="pageCtl.groupOpts" :placeholder-text="pageCtl.cascader.label"
                            :multiple=pageCtl.cascader.multiple style="width: 100%" :show-copy="false"/>
          </el-col>
          <el-col :span="3">
            &nbsp;&nbsp;
            <el-button @click="composeMail" size="small" :loading="pageCtl.loading.compose">
              <font-awesome-icon :icon="['far', 'pen-to-square']"/>&nbsp;
              Compose Mail
            </el-button>
          </el-col>
          <el-col :span="16">
            <span style="float: right;padding-right: 5px;cursor: pointer; font-size: 12px" v-show="pageCtl.isAdmin">
              <font-awesome-icon :icon="pageCtl.loading.config ? 'fa-gears' : 'fa-gears'" @click="queryConfig" :spin="pageCtl.loading.config"/>
            </span>
          </el-col>
        </el-row>
        <div class="subscript-container" v-loading="pageCtl.loading.afterChange">
          <scp-subscript id="TAPF" ref="appfRef"/>
          <scp-datagrid ref="tableRef"
                        :bindTo="pageCtl.bindTo"
                        :defaultSortColumn="pageCtl.settings.orderBy"
                        :afterChanged="afterChanged"
                        :customer-context-menu="pageCtl.customerContextMenu"
                        :enable-upload="pageCtl.settings['enableUpload']"
                        :upload-module="pageCtl.settings['uploadModule'] || ['INSERT-IGNORE', 'MERGE', 'REPLACE-ALL', 'REPLACE-MY-DATA']"
                        :where="pageCtl.settings.where"
                        :numericPrecision="4"
                        :column-order="pageCtl.settings.columnsOrder"/>
        </div>
        <h3 style="text-align: left;">Memo:</h3>
        <div class="subscript-container" >
          <iframe ref="myIframe" class="iframe-content" :srcdoc="pageCtl.config.memo"></iframe>
        </div>
      </div>
    </div>

    <!-- config-->
    <scp-draggable-resizable w="70vw" h="600px" v-model="pageCtl.visible.config" title="Configuration"
                             :save="saveConfig" :save-loading="pageCtl.loading.saving"
                             :delete="deleteConfig" delete-confirm-text="删除的数据将无法恢复, 确定删除吗?"
                             :draft="previewMail"
                             close-warning-text="未保存的数据将会丢失, 确定关闭吗?"
    >
      <template v-slot="{height}">
        <el-row class="search-box" style="padding-top: 5px; padding-left: 5px">
          <el-col :span="3">
            <el-input style="width: var(--scp-input-width) !important;" type="text" size="small" placeholder="Group" v-model="pageCtl.config.group"/>
          </el-col>
          <el-col :span="6">
            <el-input style="width: var(--scp-input-width) !important;" type="text" size="small" placeholder="Name" v-model="pageCtl.config.name"/>
          </el-col>
          <el-col :span="6">
            <el-input style="width: var(--scp-input-width) !important;" type="text" size="small" placeholder="Bind To" v-model="pageCtl.config.bindTo"/>
          </el-col>
          <el-col :span="4">
            <el-select
                class="send-message"
                v-model="pageCtl.config.module"
                size="small"
                multiple
                filterable
                clearable
                collapse-tags
                placeholder="Enable Modules">
              <el-option
                  v-for="item in ['INSERT-IGNORE', 'MERGE', 'REPLACE-ALL', 'REPLACE-MY-DATA']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-radio-group v-model="pageCtl.config.operation" size="small" @change="operationChange">
              <el-radio-button value="Update">Update</el-radio-button>
              <el-radio-button value="Create">Create</el-radio-button>
            </el-radio-group>
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 5px">
          <el-col :span="20" style="margin: 5px">
            <el-input type="text" size="small" placeholder="Columns Order" v-model="pageCtl.config.columnsOrder"/>
          </el-col>
          <el-col :span="20" style="margin: 5px">
            <el-input type="text" size="small" placeholder="Where SQL" v-model="pageCtl.config.where"/>
          </el-col>
          <el-col :span="2" style="margin: 5px;padding-top:3px">
            <el-tooltip class="item" effect="light" :content="pageCtl.tooltips" placement="bottom-end">
              <el-tag type="info" size="small" style="cursor: pointer;font-size: 12px;padding: 0 9px">
                <font-awesome-icon icon="list-ul"/>
              </el-tag>
            </el-tooltip>
          </el-col>
          <el-col :span="10" style="margin: 5px">
            <el-input type="text" size="small" placeholder="Order By" v-model="pageCtl.config.orderBy"/>
          </el-col>
          <el-col :span="6" style="margin: 5px">
              <el-select
                  v-model="pageCtl.config.excelType"
                  size="small"
                  placeholder="Excel type">
                <el-option
                    v-for="item in ['xlsx', 'xls']"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
          </el-col>
        </el-row>
        <h4 style="margin: 5px">Data SQL</h4>
        <div v-for="(data, index) in pageCtl.config.data" :key="index">
          <el-row style="margin-bottom: 5px">
            <el-col :span="23">
              <scp-ace-editor v-model="pageCtl.config.data[index]" lang="sql" style="border: 1px dotted var(--scp-border-color)" :style="{height: (height - 150) + 'px'}"/>
            </el-col>
            <el-col :span="1">
              <el-tooltip effect="light" content="Format SQL" placement="right" :show-after="1000">
                <el-tag type="info" size="small" style="cursor: pointer;font-size: 10px;padding: 0 9px" @click="formatSQL(index)">
                  <font-awesome-icon icon="align-left"/>
                </el-tag>
              </el-tooltip>
              <div style="height: 5px;"></div>
              <el-tooltip effect="light" content="Run SQL" placement="right" :show-after="1000">
                <el-tag type="info" size="small" style="cursor: pointer;font-size: 10px" @click="testSql(pageCtl.config.data[index])">
                  <font-awesome-icon :icon="pageCtl.loading.execute ? 'spinner' : 'play'" :spin="pageCtl.loading.execute"/>
                </el-tag>
              </el-tooltip>
              <div style="height: 5px;"></div>
              <el-tooltip effect="light" content="New Line" placement="right" :show-after="1000">
                <el-tag type="info" size="small" style="cursor: pointer;font-size: 10px" @click="newLine()">
                  <font-awesome-icon icon="plus"/>
                </el-tag>
              </el-tooltip>
              <div style="height: 5px;"></div>
              <el-tooltip effect="light" content="Delete Line" placement="right" :show-after="1000">
                <el-tag type="info" size="small" style="cursor: pointer;font-size: 10px" @click="delLine(index)" v-show="pageCtl.config.data.length > 1">
                  <font-awesome-icon icon="minus"/>
                </el-tag>
              </el-tooltip>
            </el-col>
          </el-row>
        </div>
        <h4 style="margin: 5px">Attachement SQL</h4>
        <div v-for="(data, index) in pageCtl.config.attachement" :key="index">
          <el-col :span="19" style="margin: 5px">
            <el-input type="text" size="small" placeholder="Attachement Name" v-model=" pageCtl.config.attachementName[index] "></el-input>
          </el-col>
        <el-row style="margin-bottom: 5px">
          <el-col :span="23">
            <scp-ace-editor v-model="pageCtl.config.attachement[index]" lang="sql" width="100%" style="border: 1px dotted var(--scp-border-color)"
                            :style="{ height: (height - 150) + 'px'}"></scp-ace-editor>
          </el-col>
          <el-col :span="1">
            <el-tooltip effect="light" content="Format SQL" placement="right" :show-after="1000">
              <el-tag type="info" size="small" style="cursor: pointer;font-size: 12px;padding: 0 9px" @click="formatSQLattachement(index)">
                <font-awesome-icon icon="align-left"/>
              </el-tag>
            </el-tooltip>
            <div style="height: 5px;"></div>
            <el-tooltip effect="light" content="Run SQL" placement="right" :show-after="1000">
              <el-tag type="info" size="small" style="cursor: pointer;font-size: 10px" @click="testSql(pageCtl.config.attachement[index])">
                <font-awesome-icon :icon="pageCtl.loading.execute ? 'spinner' : 'play'" :spin="pageCtl.loading.execute"/>
              </el-tag>
            </el-tooltip>
            <div style="height: 5px;"></div>
            <el-tooltip effect="light" content="New Line" placement="right" :show-after="1000">
              <el-tag type="info" size="small" style="cursor: pointer;font-size: 10px" @click="newLine2()">
                <font-awesome-icon icon="plus"/>
              </el-tag>
            </el-tooltip>
            <div style="height: 5px;"></div>
            <el-tooltip effect="light" content="Delete Line" placement="right" :show-after="1000">
              <el-tag type="info" size="small" style="cursor: pointer;font-size: 10px" @click="delLine2(index)" v-show="pageCtl.config.attachement.length > 1">
                <font-awesome-icon icon="minus"/>
              </el-tag>
            </el-tooltip>
            <div style="height: 5px;"></div>
            <el-tooltip effect="light" content="Template File" placement="right" :show-after="1000">
              <el-tag type="info" size="small" style="cursor: pointer;font-size: 10px" @click="createSql(index)">
                <font-awesome-icon icon="file"/>
              </el-tag>
            </el-tooltip>
          </el-col>
        </el-row>
        </div>
        <h4 style="margin: 5px">Validate SQL</h4>
        <el-row style="margin-bottom: 5px">
          <el-col :span="23">
            <scp-ace-editor v-model="pageCtl.config.validate" lang="sql" width="100%" style="border: 1px dotted var(--scp-border-color)"
                            :style="{height: (height - 150) + 'px'}"></scp-ace-editor>
          </el-col>
          <el-col :span="1">
            <el-tooltip effect="light" content="Format SQL" placement="right" :show-after="1000">
              <el-tag type="info" size="small" style="cursor: pointer;font-size: 12px;padding: 0 9px" @click="formatSQL('Validate')">
                <font-awesome-icon icon="align-left"/>
              </el-tag>
            </el-tooltip>
            <div style="height: 5px;"></div>
            <el-tooltip effect="light" content="Run SQL" placement="right" :show-after="1000">
              <el-tag type="info" size="small" style="cursor: pointer;font-size: 10px" @click="testSql(pageCtl.config.validate)">
                <font-awesome-icon :icon="pageCtl.loading.execute ? 'spinner' : 'play'" :spin="pageCtl.loading.execute"/>
              </el-tag>
            </el-tooltip>
          </el-col>
        </el-row>
        <h4 style="margin: 5px">After Save SQL</h4>
        <el-row style="margin-bottom: 5px">
          <el-col :span="23">
            <scp-ace-editor v-model="pageCtl.config.afterSave" lang="sql" width="100%" style="border: 1px dotted var(--scp-border-color)"
                            :style="{height: (height - 150) + 'px'}"></scp-ace-editor>
          </el-col>
          <el-col :span="1">
            <el-tooltip effect="light" content="Format SQL" placement="right" :show-after="1000">
              <el-tag type="info" size="small" style="cursor: pointer;font-size: 12px;padding: 0 9px" @click="formatSQL('AfterSave')">
                <font-awesome-icon icon="align-left"/>
              </el-tag>
            </el-tooltip>
          </el-col>
        </el-row>
        <h4 style="margin: 5px">Calculate SQL</h4>
        <el-row style="margin-bottom: 5px">
          <el-col :span="23">
            <scp-ace-editor v-model="pageCtl.config.calculation" lang="sql" width="100%" style="border: 1px dotted var(--scp-border-color)"
                            :style="{height: (height - 150) + 'px'}"></scp-ace-editor>
          </el-col>
          <el-col :span="1">
            <el-tooltip effect="light" content="Format SQL" placement="right" :show-after="1000">
              <el-tag type="info" size="small" style="cursor: pointer;font-size: 12px;padding: 0 9px" @click="formatSQL('Calculation')">
                <font-awesome-icon icon="align-left"/>
              </el-tag>
            </el-tooltip>
          </el-col>
        </el-row>
        <h4 style="margin: 5px">After Sent SQL</h4>
        <el-row style="margin-bottom: 5px">
          <el-col :span="23">
            <scp-ace-editor v-model="pageCtl.config.afterSend" lang="sql" width="100%" style="border: 1px dotted var(--scp-border-color)"
                            :style="{height: (height - 150) + 'px'}"></scp-ace-editor>
          </el-col>
          <el-col :span="1">
            <el-tooltip effect="light" content="Format SQL" placement="right" :show-after="1000">
              <el-tag type="info" size="small" style="cursor: pointer;font-size: 12px;padding: 0 9px" @click="formatSQL('AfterSend')">
                <font-awesome-icon icon="align-left"/>
              </el-tag>
            </el-tooltip>
          </el-col>
        </el-row>
        <h4 style="margin: 5px">Clear Table SQL</h4>
        <el-row style="margin-bottom: 5px">
          <el-col :span="23">
            <scp-ace-editor v-model="pageCtl.config.clearTable" lang="sql" width="100%" style="border: 1px dotted var(--scp-border-color)"
                            :style="{height: (height - 150) + 'px'}"></scp-ace-editor>
          </el-col>
          <el-col :span="1">
            <el-tooltip effect="light" content="Format SQL" placement="right" :show-after="1000">
              <el-tag type="info" size="small" style="cursor: pointer;font-size: 12px;padding: 0 9px" @click="formatSQL('clearTable')">
                <font-awesome-icon icon="align-left"/>
              </el-tag>
            </el-tooltip>
          </el-col>
        </el-row>
        <h4 style="margin: 5px">Mail Template</h4>
        <el-row style="margin: 5px">
          <el-col :span="19">
            <el-input type="text" size="small" placeholder="Subject" v-model="pageCtl.config.subject"></el-input>
          </el-col>
          <el-col :span="19" style="margin: 5px 0">
            <el-select
                v-model="pageCtl.config.to"
                size="small"
                multiple
                filterable
                remote
                clearable
                :reserve-keyword="false"
                class="send-message"
                placeholder="To"
                style="width: 100%;"
                :remote-method="(keyword)=>queryMailTo(keyword, 'mailto')"
                :loading="pageCtl.loading.mailto">
              <el-option
                  v-for="item in pageCtl.mailtoOpts"
                  :key="item['VALUE']"
                  :label="item['LABEL']"
                  :value="item['VALUE']">
                <span style="float: left">{{ item['LABEL'] }}</span>
                <span style="float: right; color: var(--scp-text-color-secondary);padding-right:20px">{{ item['TYPE'] }}</span>
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="19">
            <el-select
                v-model="pageCtl.config.cc"
                size="small"
                multiple
                filterable
                remote
                clearable
                :reserve-keyword="false"
                class="send-message"
                placeholder="CC"
                style="width: 100%"
                :remote-method="(keyword)=>queryMailTo(keyword, 'mailcc')"
                :loading="pageCtl.loading.mailcc">
              <el-option
                  v-for="item in pageCtl.mailccOpts"
                  :key="item['VALUE']"
                  :label="item['LABEL']"
                  :value="item['VALUE']">
                <span style="float: left">{{ item['LABEL'] }}</span>
                <span style="float: right; color: var(--scp-text-color-secondary);padding-right:20px">{{ item['TYPE'] }}</span>
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 5px">
          <el-col :span="23">
            <scp-ace-editor v-model="pageCtl.config.html" lang="html" style="border: 1px dotted var(--scp-border-color); width: 100%; height: 300px"></scp-ace-editor>
          </el-col>
          <el-col :span="1">
            <div style="height: 5px;"></div>
            <el-tooltip effect="light" content="Preview Html" placement="right" :show-after="1000">
              <el-tag type="info" size="small" style="cursor: pointer;font-size: 10px" @click="testHtml(pageCtl.config.html)">
                <font-awesome-icon icon="play"/>
              </el-tag>
            </el-tooltip>
          </el-col>
        </el-row>
        <h4 style="margin: 5px">File Template Name:</h4>
        <el-col :span="8">
          <el-input style="width: var(--scp-input-width) !important;" type="text" size="small" placeholder="请输入附件名" v-model="pageCtl.config.template"/>
        </el-col>
        <h4 style="margin: 5px">MEMO</h4>
        <el-row style="margin-bottom: 5px">
          <el-col :span="23">
            <scp-ck-editor v-model="pageCtl.config.memo" style="width: 100%; height: 300px; overflow-y: hidden"/>
          </el-col>
        </el-row>
        <h4 style="margin: 5px" v-show="false">Scheduler</h4>
        <el-row v-show="false" style="padding: 0 5px">
          <el-col :span="4">
            <el-tooltip class="item" effect="light" :show-after="500" content="Month" placement="top-end">
              <el-input v-model="pageCtl.config.month" size="small" style="width: var(--scp-input-width)"></el-input>
            </el-tooltip>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.config.dayType"
                       size="small"
                       class="search-group-left"
                       placeholder="Day Type">
              <el-option
                  v-for="item in ['DAY_OF_MONTH','DAY_OF_WEEK']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-tooltip class="item" effect="light" :show-after="500" :content="pageCtl.config.dayType" placement="top-end">
              <el-input v-model="pageCtl.config.day" size="small" style="width: var(--scp-input-width)" class="search-group-right"></el-input>
            </el-tooltip>
          </el-col>
          <el-col :span="4">
            <el-tooltip class="item" effect="light" :show-after="500" content="Hour" placement="top-end">
              <el-input v-model="pageCtl.config.hour" size="small" style="width: var(--scp-input-width)"></el-input>
            </el-tooltip>
          </el-col>
          <el-col :span="4">
            <el-tooltip class="item" effect="light" :show-after="500" content="Minute" placement="top-end">
              <el-input v-model="pageCtl.config.minute" size="small" style="width: var(--scp-input-width)"></el-input>
            </el-tooltip>
          </el-col>
        </el-row>
        <hr>
      </template>
    </scp-draggable-resizable>

    <!-- execute sql-->
    <scp-draggable-resizable w="800px" h="480px" v-model="pageCtl.visible.executeResult" title="Result" z-index="1202">
      <template v-slot="{height}">
        <scp-table
            ref="executeResultTableRef"
            :lazy="true"
            :showContextMenu="true"
            :max-height="height - 120"
            url="/toolbox/application_form/query_result"
            download-url="/toolbox/application_form/download_result"
            :download-specify-column="false"
            :params="{'scripts': pageCtl.testScript}"
            :data="pageCtl.resultData"/>
      </template>
    </scp-draggable-resizable>

    <!-- file template -->
   <scp-draggable-resizable w="800px" h="480px" v-model="pageCtl.visible.fileList" title="Result" z-index="1202">
     <el-col :span="5">
       <el-select v-model="pageCtl.visible.category"
                  placeholder="Template"
                  size="large"
                  color="pri">
         <el-option
             v-for="item in pageCtl.visible.fileList"
             :key="item['CATEGORY']"
             :label="item['CATEGORY']"
             :value="item['CATEGORY']">
         </el-option>
       </el-select>
     </el-col>
    </scp-draggable-resizable>

    <!-- execute html-->
    <scp-draggable-resizable w="820px" h="480px" v-model="pageCtl.visible.htmlPreview" title="Execute Result" z-index="1203">
      <template v-slot="{height}">
        <iframe height="100%" width="100%" :style="{height:(height - 120)}" :srcdoc="pageCtl.htmlPreview"></iframe>
      </template>
    </scp-draggable-resizable>

    <!-- compose mail-->
    <scp-draggable-resizable w="70vw" h="75vh" v-model="pageCtl.visible.composeMail" title="Compose Mail"
                             :save="sendMail" save-text="Send" :save-loading="pageCtl.loading.sendMail"
                             close-text="Close" :scroll="false" class="compose-mail-div">
      <template v-slot="{height}">
        <el-row style="padding:10px">
          <el-col :span="24">
            <el-input type="text" placeholder="Subject" v-model="pageCtl.compose.subject"></el-input>
          </el-col>
          <el-col :span="24" style="margin: 5px 0">
            <el-select
                v-model="pageCtl.compose.to"
                multiple
                filterable
                remote
                clearable
                :reserve-keyword="false"
                class="send-message"
                placeholder="To"
                style="width: 100%;"
                :remote-method="(keyword)=>queryMailTo(keyword, 'mailto')"
                :loading="pageCtl.loading.mailto">
              <el-option
                  v-for="item in pageCtl.mailtoOpts"
                  :key="item['VALUE']"
                  :label="item['LABEL']"
                  :value="item['VALUE']">
                <span style="float: left">{{ item['LABEL'] }}</span>
                <span style="float: right; color: var(--scp-text-color-secondary);padding-right:20px">{{ item['TYPE'] }}</span>
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-select
                v-model="pageCtl.compose.cc"
                multiple
                filterable
                remote
                clearable
                :reserve-keyword="false"
                class="send-message"
                placeholder="CC"
                style="width: 100%"
                :remote-method="(keyword)=>queryMailTo(keyword, 'mailcc')"
                :loading="pageCtl.loading.mailcc">
              <el-option
                  v-for="item in pageCtl.mailccOpts"
                  :key="item['VALUE']"
                  :label="item['LABEL']"
                  :value="item['VALUE']">
                <span style="float: left">{{ item['LABEL'] }}</span>
                <span style="float: right; color: var(--scp-text-color-secondary);padding-right:20px">{{ item['TYPE'] }}</span>
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="8" style="line-height: 26px;padding-left: 20px;">
            <el-link type="primary" @click="testSql(pageCtl.compose.attachement)">{{
                pageCtl.loading.execute ? 'Loading...' : pageCtl.compose.attachementName
              }}
            </el-link>
          </el-col>
          <el-col :span="4">
            <el-button @click="downloadMail" size="small" :loading="pageCtl.loading.download">
              <font-awesome-icon :icon="['fas', 'download']"/>&nbsp;
              点此可以直接下载文件
            </el-button>
          </el-col>
        </el-row>
        <div style="padding:0 10px">
          <scp-ck-editor v-model="pageCtl.compose.html" :style="{height: (height - 250) + 'px'}"/>
        </div>
        <span style="color:var(--scp-text-color-error); float:left;padding:0 10px">Last sent time: {{ pageCtl.compose.lastSentTime }}</span>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { inject, nextTick, onMounted, reactive, ref, watch } from 'vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'

const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $downloadFile: any = inject('$downloadFile')

const tableRef = ref()
const executeResultTableRef = ref()

const pageCtl = reactive({
  tooltips: 'Available Parameters: <br/>{{_userid}} 当前登录用户<br/>{{_mailid}} 邮件ID, 仅在After Send SQL中生效',
  appID: [''],
  bindTo: '',
  appConfigs: [] as Array<any>,
  settings: {
    enableUpload: false,
    uploadModule: ['INSERT-IGNORE', 'MERGE', 'REPLACE-ALL', 'REPLACE-MY-DATA'] as Array<any>,
    where: '',
    columnsOrder: [],
    calc: 'N',
    clear: 'N',
    orderBy: '',
    excelType: ['xlsx', 'xls'] as Array<any>
  },
  isAdmin: false,
  groupOpts: [],
  config: {
    id: '',
    group: '',
    name: '',
    bindTo: '',
    module: ['INSERT-IGNORE', 'MERGE'],
    operation: 'Update',
    data: [''],
    validate: '',
    attachementName: [''],
    attachement: [''],
    afterSave: '',
    afterSend: '',
    clearTable: '',
    calculation: '',
    where: '',
    columnsOrder: '',
    html: '',
    template: '',
    memo: '',
    subject: '',
    to: [],
    cc: [],
    month: '*',
    dayType: 'DAY_OF_MONTH',
    day: '*',
    hour: '0',
    minute: '-1',
    orderBy: '',
    excelType: ['xlsx']
  },
  loading: {
    execute: false,
    exe: [['fal', 'file']],
    mailto: false,
    mailcc: false,
    sendMail: false,
    compose: false,
    download: false,
    config: false,
    afterChange: false,
    saving: false
  },
  visible: {
    executeResult: false,
    config: false,
    htmlPreview: false,
    composeMail: false,
    fileList: false,
    category: ''
  },
  resultData: [[]],
  htmlPreview: '',
  mailtoOpts: [],
  mailccOpts: [],
  timeout: null as any,
  compose: {
    id: '',
    to: [],
    cc: [],
    html: '999',
    subject: '',
    attachement: [''],
    attachementName: [''],
    lastSentTime: ''
  },
  customerContextMenu: {
    calculation: {
      name: 'Calculate',
      disabled: () => {
        return pageCtl.settings.calc === 'N'
      },
      callback: () => {
        calculation()
      }
    },
    clear_table: {
      name: 'Clear Table',
      disabled: () => {
        return pageCtl.settings.clear === 'N'
      },
      callback: () => {
        clearTable()
      }
    },
    split: { name: '---------' }
  },
  testScript: '',
  sqlIndex: '',
  cascader: {
    label: 'TaskGroup',
    multiple: false
  }
})

const initPage = () => {
  $axios({
    method: 'post',
    url: '/toolbox/application_form/init_page'
  }).then((body) => {
    pageCtl.appConfigs = body.appConfigs
    pageCtl.isAdmin = body.isAdmin
    pageCtl.groupOpts = body.groupOpts
    if (body.appConfigs.length > 0) {
      pageCtl.appID[1] = body.groupOpts[0].children[0].value
    }
  }).catch((error) => {
    console.log(error)
  })
}

const calculation = () => {
  pageCtl.loading.afterChange = true
  $axios({
    method: 'post',
    url: '/toolbox/application_form/call_calculation',
    data: {
      id: pageCtl.appID[1]
    }
  }).then(() => {
    tableRef.value.search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.afterChange = false
  })
}

const clearTable = () => {
  if (confirm('确定清空数据? 此操作不可逆')) {
    pageCtl.loading.afterChange = true
    $axios({
      method: 'post',
      url: '/toolbox/application_form/clear_table',
      data: {
        id: pageCtl.appID[1]
      }
    }).then(() => {
      tableRef.value.search()
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.afterChange = false
    })
  }
}

const resetConfig = (operation?) => {
  operation = operation || 'Update'
  pageCtl.config = {
    id: '',
    group: '',
    name: '',
    bindTo: '',
    module: ['INSERT-IGNORE', 'MERGE'],
    operation,
    data: [''],
    validate: '',
    attachementName: [''],
    attachement: [''],
    afterSave: '',
    afterSend: '',
    clearTable: '',
    calculation: '',
    where: '',
    columnsOrder: '',
    html: '',
    template: '',
    memo: '',
    subject: '',
    to: [],
    cc: [],
    month: '*',
    dayType: 'DAY_OF_MONTH',
    day: '*',
    hour: '0',
    minute: '-1',
    orderBy: '',
    excelType: ['xlsx']
  }
}

const resetCompose = () => {
  pageCtl.compose = {
    id: '',
    to: [],
    cc: [],
    html: '',
    subject: '',
    attachement: [''],
    attachementName: [''],
    lastSentTime: ''
  }
}

const composeMail = () => {
  pageCtl.loading.compose = true
  $axios({
    method: 'post',
    url: '/toolbox/application_form/compose_mail',
    data: {
      id: pageCtl.appID[1]
    }
  }).then((body) => {
    if (body.RESULT === -1) {
      $message.error({
        dangerouslyUseHTMLString: true,
        message: body.CONTENT,
        type: 'error'
      })
    } else {
      pageCtl.visible.composeMail = true
      if (body.CONTENT) {
        pageCtl.compose = body.CONTENT
      }
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.compose = false
  })
}

const newLine = () => {
  pageCtl.config.data.push('')
}

const newLine2 = () => {
  pageCtl.config.attachement.push('')
  pageCtl.config.attachementName.push('')
}

const delLine = (index1) => {
  pageCtl.config.data.splice(index1, 1)
}
const delLine2 = (index1) => {
  pageCtl.config.attachement.splice(index1, 1)
  pageCtl.config.attachementName.splice(index1, 1)
}

const formatSQL = (index) => {
  let sql
  if (index === 'Validate') {
    sql = pageCtl.config.validate
  } else if (index === 'AfterSave') {
    sql = pageCtl.config.afterSave
  } else if (index === 'AfterSend') {
    sql = pageCtl.config.afterSend
  } else if (index === 'Calculation') {
    sql = pageCtl.config.calculation
  } else if (index === 'clearTable') {
    sql = pageCtl.config.clearTable
  } else {
    sql = pageCtl.config.data[index]
  }

  $axios({
    method: 'post',
    url: '/home/<USER>/format_sql',
    data: {
      scripts: sql
    }
  }).then((body) => {
    if (body) {
      if (index === 'Validate') {
        pageCtl.config.validate = body
      } else if (index === 'AfterSave') {
        pageCtl.config.afterSave = body
      } else if (index === 'AfterSend') {
        pageCtl.config.afterSend = body
      } else if (index === 'Calculation') {
        pageCtl.config.calculation = body
      } else if (index === 'clearTable') {
        pageCtl.config.clearTable = body
      } else {
        pageCtl.config.data[index] = body
      }
    }
  }).catch((error) => {
    console.log(error)
  })
}

const formatSQLattachement = (index) => {
  $axios({
    method: 'post',
    url: '/home/<USER>/format_sql',
    data: {
      scripts: pageCtl.config.attachement[index]
    }
  }).then((body) => {
    if (body) {
      pageCtl.config.attachement[index] = body
    }
  }).catch((error) => {
    console.log(error)
  })
}

const formatHtml = (html) => {
  $axios({
    method: 'post',
    url: '/toolbox/application_form/format_html',
    data: {
      html
    }
  }).then((body) => {
    if (body) {
      pageCtl.config.html = body
    }
  }).catch((error) => {
    console.log(error)
  })
}

const testSql = (scripts) => {
  if (pageCtl.loading.execute) {
    return
  }
  pageCtl.testScript = scripts
  nextTick(() => {
    showExecuteResult()
    executeResultTableRef.value.search()
  })
}

const createSql = (index) => {
  if (pageCtl.loading.execute) {
    return
  }
  pageCtl.sqlIndex = index
  nextTick(() => {
    showFileList()
  })
}

const testHtml = (html) => {
  pageCtl.htmlPreview = html
  pageCtl.visible.htmlPreview = true
}

const previewMail = () => {
  $axios({
    method: 'post',
    url: '/toolbox/application_form/preview_mail',
    data: pageCtl.config
  }).then((body) => {
    if (body.RESULT === -1) {
      $message.error({
        dangerouslyUseHTMLString: true,
        message: body.CONTENT,
        type: 'error'
      })
    } else {
      testHtml(body.CONTENT)
    }
  }).catch((error) => {
    console.log(error)
  })
}

const showExecuteResult = () => {
  pageCtl.visible.executeResult = true
}

const showFileList = () => {
  $axios({
    method: 'post',
    url: '/toolbox/application_form/file_template'
  }).then((body) => {
    pageCtl.visible.fileList = body.category
  }).catch((error) => {
    console.log(error)
  })
}

const queryConfig = () => {
  pageCtl.loading.config = true
  $axios({
    method: 'post',
    url: '/toolbox/application_form/query_config',
    data: {
      id: pageCtl.appID[1]
    }
  }).then((body) => {
    if (body) {
      body.operation = 'Update'
      if (body.module) {
        body.module = body.module.split(',')
      } else {
        body.module = []
      }
      pageCtl.config = body
    }
    pageCtl.visible.config = true
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.config = false
  })
}

const saveConfig = () => {
  pageCtl.loading.saving = true
  $axios({
    method: 'post',
    url: '/toolbox/application_form/save_config',
    data: pageCtl.config
  }).then((body) => {
    if (parseInt(body + '') === 0) {
      $message.error('No Records Updated')
    } else {
      $message.success('Configuration Saved')
      pageCtl.visible.config = false
      resetConfig()
      initPage()
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.saving = false
  })
}

const deleteConfig = () => {
  $axios({
    method: 'post',
    url: '/toolbox/application_form/delete_config',
    data: {
      id: pageCtl.appID[1]
    }
  }).then(() => {
    $message.success('Application Form Deleted')
    pageCtl.visible.config = false
    resetConfig()
    initPage()
  }).catch((error) => {
    console.log(error)
  })
}

const getSelectedConfig = (id) => {
  for (let i = 0; i < pageCtl.appConfigs.length; i++) {
    if (pageCtl.appConfigs[i].APP_ID === id) {
      return pageCtl.appConfigs[i]
    }
  }
  return {}
}

const queryMailTo = (keywords, name) => {
  if (keywords.length > 0) {
    pageCtl.loading[name] = true

    if (pageCtl.timeout != null) {
      clearTimeout(pageCtl.timeout)
    }
    pageCtl.timeout = setTimeout(() => {
      pageCtl.timeout = null
      $axios({
        method: 'post',
        url: '/toolbox/application_form/query_mailto_by_keywords',
        data: {
          keywords
        }
      }).then((body) => {
        pageCtl.loading[name] = false
        pageCtl[name + 'Opts'] = body
      }).catch((error) => {
        console.log(error)
      })
    }, 650)
  } else {
    pageCtl.mailtoOpts = []
  }
}

const operationChange = (val) => {
  if (val === 'Create') {
    resetConfig('Create')
  }
}

const sendMail = () => {
  pageCtl.loading.sendMail = true
  $axios({
    method: 'post',
    url: '/toolbox/application_form/send_mail',
    data: pageCtl.compose
  }).then((body) => {
    if (body) {
      $message.error(body)
    } else {
      $message.success('Application Mail Submitted')
      pageCtl.visible.composeMail = false
      resetCompose()
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.sendMail = false
  })
}

const downloadMail = () => {
  pageCtl.loading.download = true
  console.log(pageCtl.config.name)
  $downloadFile('/toolbox/application_form/download_email', {
    id: pageCtl.appID[1], name: pageCtl.config.name
  }, () => {
    pageCtl.loading.download = false
    // setTimeout(() => {
    //   $downloadFile('/toolbox/application_form/download_email', {
    //     id: pageCtl.appID[1], name: pageCtl.config.name // 假设你有第二个文件的ID和名称
    //   }, () => {
    //     pageCtl.loading.download = false
    //   })
    // }, 6000)
  })
}

const afterChanged = () => {
  pageCtl.loading.afterChange = true
  $axios({
    method: 'post',
    url: '/toolbox/application_form/after_changed',
    data: {
      id: pageCtl.appID[1]
    }
  }).then(() => {
    tableRef.value.search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.afterChange = false
  })
}
const sendCategoryToBackend = (category) => {
  $axios({
    method: 'post',
    url: '/toolbox/application_form/file_sql',
    data: {
      id: pageCtl.visible.category
    }
  }).then((body) => {
    pageCtl.visible.fileList = false
    pageCtl.config.attachement[pageCtl.sqlIndex] = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.afterChange = false
  })
}

onMounted(() => {
  initPage()
  pageCtl.settings.where = 'create_by$ = \'' + localStorage.getItem('username') + '\''
})

watch(() => pageCtl.appID[1], (newVal) => {
  console.log(pageCtl.appID)
  const config = getSelectedConfig(newVal)
  pageCtl.bindTo = config.TABLE_NAME
  pageCtl.visible.config = false
  if (config.TABLE_MODULE && config.TABLE_MODULE.length > 0) {
    pageCtl.settings.enableUpload = true
    pageCtl.settings.uploadModule = config.TABLE_MODULE.split(',')
    pageCtl.settings.where = config.WHERE_SQL
    pageCtl.settings.calc = config.CALCULATION
    pageCtl.settings.clear = config.CLEAR
    pageCtl.settings.orderBy = config.ORDER_BY
    pageCtl.settings.excelType = config.EXCEL_TYPE
  } else {
    pageCtl.settings.enableUpload = false
    pageCtl.settings.uploadModule = ['INSERT-IGNORE', 'MERGE', 'REPLACE-ALL', 'REPLACE-MY-DATA']
    pageCtl.settings.where = config.WHERE_SQL
    pageCtl.settings.calc = config.CALCULATION
    pageCtl.settings.clear = config.CLEAR
    pageCtl.settings.orderBy = config.ORDER_BY
    pageCtl.settings.excelType = ['xlsx', 'xls']
  }
  if (config.COLUMNS_ORDER) {
    pageCtl.settings.columnsOrder = JSON.parse(config.COLUMNS_ORDER)
  }
  if (config.TEMPLATE) {
    pageCtl.config.template = config.TEMPLATE
  }
  if (config.MEMO && config.MEMO.length > 0) {
    pageCtl.config.memo = config.MEMO
  } else {
    pageCtl.config.memo = ''
  }
})

watch(() => pageCtl.visible.category, (newVal) => {
  sendCategoryToBackend(newVal)
})

</script>

<style scoped>
.el-text-like-iframe {
  /* 样式设置，例如边框、圆角、阴影等 */
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0,0,0,0.12), 0 0 6px 0 rgba(0,0,0,0.04);
}

.iframe-content {
  width: 100%;
  height: 100%;
  border: none; /* 移除 iframe 的边框 */
  overflow: auto; /* 添加滚动条 */
}
</style>
