<template>
  <div class="left-sidebar" id="emailNotification">
    <div class="widget">
      <div class="widget-body">
        <el-container @contextmenu.prevent="">
          <el-aside width="280px" :style="{height: _leftContainerHeight}"
                    style="border-right: 1px solid var(--scp-border-color);margin-right:10px;min-height: 600px" class="left-container">
            <scp-tree-menu
                ref="treeRef"
                url="/toolbox/email_notification/query_email_notification_list"
                :new-click="showNewEmailWin"
                :node-click="clickNode"
            ></scp-tree-menu>
          </el-aside>
          <el-main style="padding: 0">
            <div v-show="pageCtl.selectedId === ''">
              <h1 style="text-align: center; color: var(--scp-text-color-lighter);height:350px;line-height: 350px; font-size: xxx-large">Email Notification</h1>
            </div>
            <el-tabs v-model="pageCtl.currentTab" v-show="pageCtl.selectedId !== ''">
              <el-tab-pane label="Last Execution" name="first">
                <div v-loading="pageCtl.loading.report1">
                  <h3 style="margin: 5px 15px 0 0;display: inline;">{{ pageCtl.report1Data['NAME'] }}</h3>
                  <el-tag :type="pageCtl.report1Data['STATUS'] === 'Inactive' ? 'danger' : ''" size="small" effect="dark" style="margin-right: 10px">
                    {{ pageCtl.report1Data['STATUS'] || '&nbsp;' }}
                  </el-tag>
                  <el-tag :type="pageCtl.report1Data['LAST_EXEC_RESULT_CODE'] !== '200' ? 'danger' : ''" size="small" effect="dark" style="margin-right: 10px"
                          v-show="_showLastExecLogs">
                    {{ pageCtl.report1Data['LAST_EXEC_RESULT_CODE'] === '200' ? 'No Errors' : 'Interrupted' }}
                  </el-tag>
                  <el-tag size="small" effect="dark" style="margin-right: 10px" v-show="_showLastExecLogs">
                    {{ pageCtl.report1Data['LAST_EXEC_TIME'] || '&nbsp;' }}
                  </el-tag>
                  <el-tag size="small" effect="dark" style="margin-right: 10px">{{ pageCtl.report1Data['CORN'] || '&nbsp;' }}</el-tag>
                  <el-tag size="small" effect="dark">maintain by {{ pageCtl.report1Data['USER_NAME'] }}({{ pageCtl.report1Data['SESA_CODE'] }})</el-tag>
                  <hr style="margin: 10px 0 15px 0">
                  <pre style="height: 280px; overflow: auto">{{ pageCtl.report1Data['LOG_FIELD'] }}</pre>
                </div>
                <hr>
                <scp-table
                    ref="report2Ref"
                    url="/toolbox/email_notification/query_report2"
                    :lazy="true"
                    :params="{id: pageCtl.selectedId}"
                    :columns="pageCtl.report2TableColumn"
                />
              </el-tab-pane>
              <el-tab-pane label="Job Info" name="second" @click="pageCtl.urls = pageCtl.modifyEmail.urls">
                <div class="new-email" style="height: var(--scp-input-width);overflow: auto">
                  <el-row>
                    <el-col :span="2" class="title">Job Name</el-col>
                    <el-col :span="12" class="content">
                      <el-input size="small" v-model="pageCtl.modifyEmail.name" placeholder="Job Name"></el-input>
                    </el-col>
                    <el-col :span="10" class="input-tips">
                      任务名, 尽量用最少的字描述任务内容
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Groups</el-col>
                    <el-col :span="4" class="content">
                      <el-autocomplete
                          class="inline-input"
                          v-model="pageCtl.modifyEmail.groups"
                          :maxlength="30"
                          style="width: 100%"
                          size="small"
                          :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                          placeholder="Group"
                          show-word-limit
                      ></el-autocomplete>
                    </el-col>
                    <el-col :span="18" class="input-tips">
                      任务分组, 可以使用 <b>.</b> 进行层级分类, 如Level.Leve2.Leve3, 这样会生成三层目录
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Status</el-col>
                    <el-col :span="4" class="content">
                      <el-select v-model="pageCtl.modifyEmail.status" size="small" style="width: 100%">
                        <el-option
                            v-for="item in ['Active','Inactive']"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="18" class="input-tips">
                      任务状态, 如果暂时不使用的任务可以设置为Inactive, 确定不再使用的任务, 建议直接删除
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Trigger</el-col>
                    <el-col :span="4" class="content">
                      <el-select v-model="pageCtl.modifyEmail.month" size="small" placeholder="Month" filterable collapse-tags clearable multiple
                                 style="width: 100%">
                        <el-option
                            v-for="item in ['*', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="4">
                      <el-select v-model="pageCtl.modifyEmail.dayType" size="small" placeholder="Day Type" class="search-group-left"
                                 @change="pageCtl.modifyEmail.day = []"
                                 style="width: 100%">
                        <el-option
                            v-for="item in ['DAY_OF_MONTH', 'DAY_OF_WEEK']"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="4" class="content">
                      <el-select v-model="pageCtl.modifyEmail.day" size="small" :placeholder="pageCtl.modifyEmail.dayType" filterable collapse-tags multiple
                                 class="search-group-right" style="width: 100%">
                        <el-option
                            v-for="item in _dayRangeOfModify"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="4" class="content">
                      <el-select v-model="pageCtl.modifyEmail.hour" size="small" placeholder="Hour" filterable collapse-tags multiple style="width: 100%">
                        <el-option
                            v-for="item in _hourRange"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6" class="input-tips">
                      任务触发配置, 精确到小时
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Recipient</el-col>
                    <el-col :span="16" class="content" style="height: auto !important;">
                      <el-select
                          v-model="pageCtl.modifyEmail.recipient" placeholder="Recipient" collapse-tags-tooltip filterable allow-create multiple
                          :collapse-tags="true" style="width: 100%">
                        <el-option
                            v-for="(item,index) in pageCtl.emailUserList"
                            :key="index"
                            :label="item['USER_NAME'] + ' [' + item['EMAIL'] +']'"
                            :value="item['EMAIL']">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6" class="input-tips">
                      收件人邮箱地址
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">CC</el-col>
                    <el-col :span="16" class="content">
                      <el-select v-model="pageCtl.modifyEmail.cc" placeholder="CC" filterable :collapse-tags="true"
                                 allow-create multiple clearable collapse-tags-tooltip style="width: 100%">
                        <el-option
                            v-for="(item,index) in pageCtl.emailUserList"
                            :key="index"
                            :label="item['USER_NAME'] + ' [' + item['EMAIL'] +']'"
                            :value="item['EMAIL']">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6" class="input-tips">
                      抄送人邮箱地址
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Subject</el-col>
                    <el-col :span="16" class="content">
                      <el-input size="small" v-model="pageCtl.modifyEmail.subject" style="width: 100%"></el-input>
                    </el-col>
                    <el-col :span="6" class="input-tips">
                      邮件主题
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Attachments</el-col>
                    <el-col :span="1" class="content">
                      <el-button type="default" @click="addAttachments" style="width: 100%">
                        <font-awesome-icon icon="paperclip"></font-awesome-icon>
                      </el-button>
                    </el-col>
                    <el-col :span="21" class="input-tips">
                      邮件附件
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Contents</el-col>
                    <el-col :span="21" class="content">
                      <scp-ck-editor v-model="pageCtl.modifyEmail.contents" style="height:280px;"/>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Remarks</el-col>
                    <el-col :span="21" class="content">
                      <el-input style="height: auto !important;" type="textarea" v-model="pageCtl.modifyEmail.remarks"></el-input>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Urls</el-col>
                    <el-col :span="21" class="input-tips" style="height: auto">
                      url:需要截图发送的链接, 可以使用DSS的Variant Sharing, 仅支持DSS<br>
                      sleep:抓取等待, 单位秒, 爬虫无法确认页面何时加载完毕, 只能预设一个等待时间, 在等待后, 执行截图操作, 大部分页面15秒足够<br>
                      zoom:默认抓取分辨率是1920*1080, 但有些页面内容较多, 可以通过调整zoom的值来缩放页面, 以达到可以在一个页面显示所有内容的目的<br>
                      areaid:如果需要发送页面某一张报表的截图, 可以选择对应的报表ID. 如果需要整页截图, 此项留空. 报表ID可在角标处查看
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2">&nbsp;</el-col>
                    <el-col class="modifyUrlTable" :span="21">
                      <el-button
                          size="small"
                          style="margin-bottom: 2px;float: right"
                          @click="addUrl(pageCtl.modifyEmail.urls)">
                        <font-awesome-icon :icon="['fa-solid', 'fa-plus']"/>
                      </el-button>
                      <el-table
                          :data="pageCtl.modifyEmail.urls"
                          border
                          :cell-style="{height:'20px',padding:'0'}"
                          :header-cell-style="{height:'20px',padding:'0'}">
                        <template v-slot:empty>
                          <span style="font-size: 5px">No Data</span>
                        </template>
                        <el-table-column
                            label="ID"
                            width="70"
                            header-align="center"
                            prop="id">
                          <template v-slot="scope">
                            <div v-if="true">
                              <el-input v-model="scope.row.id"/>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column
                            label="URL"
                            header-align="center"
                            prop="url">
                          <template v-slot="scope">
                            <div v-if="true">
                              <el-input v-model="scope.row.url"/>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column
                            label="Sleep"
                            width="50"
                            align="center"
                            prop="sleep">
                          <template v-slot="scope">
                            <div v-if="true">
                              <el-input v-model="scope.row.sleep"/>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column
                            label="Zoom"
                            width="50"
                            align="center"
                            prop="zoom">
                          <template v-slot="scope">
                            <div v-if="true">
                              <el-input v-model="scope.row.zoom"/>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column
                            label="AreaID"
                            width="200"
                            align="center"
                            prop="areaid">
                          <template v-slot="scope">
                            <div v-if="true">
                              <el-select v-model="scope.row.areaid" size="small" placeholder="Area ID" filterable clearable style="width: 100%">
                                <el-option
                                    v-for="item in pageCtl.reportIDList"
                                    :key="item['REPORT_ID']"
                                    :label="item['REPORT_ID'] + ' [' + item['TITLE'] +']'"
                                    :value="item['REPORT_ID']">
                                </el-option>
                              </el-select>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column
                            align="center"
                            width="80"
                            label="Operate">
                          <template v-slot="scope">
                            <font-awesome-icon @click="pageCtl.modifyEmail.urls.splice(scope.$index, 1)"
                                               icon="times" font-size="15px" style="color: var(--scp-bg-color-error);margin-top: 4px;cursor: pointer"/>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="23" style="text-align: right">
                      <el-popconfirm title="确定删除任务?这个操作不可恢复"
                                     iconColor="var(--scp-text-color-error)"
                                     @confirm="deleteEmail"
                                     confirmButtonType="danger"
                                     cancelButtonType="primary"
                                     confirmButtonText='确定'
                                     cancelButtonText='取消'>
                        <template #reference>
                          <el-button type="danger" size="small" :loading="pageCtl.loading.delete">
                            <font-awesome-icon icon="times"/>
                            &nbsp;Delete
                          </el-button>
                        </template>
                      </el-popconfirm>
                      <el-popconfirm title="确定要执行任务? 执行时间会随着截图数量增加而增加"
                                     iconColor="orange"
                                     style="margin-left:10px;margin-right:10px"
                                     @confirm="runJob"
                                     confirmButtonType="warning"
                                     cancelButtonType="primary"
                                     confirmButtonText='确定'
                                     cancelButtonText='取消'>
                        <template #reference>
                          <el-button type="primary" size="small" :loading="pageCtl.loading.runJob" :disabled="pageCtl.mqttConnected === false">
                            <font-awesome-icon icon="play" style="font-size: 90%;"/>
                            &nbsp;Run
                          </el-button>
                        </template>
                      </el-popconfirm>
                      <el-button type="primary" size="small" @click="modifyCurrentEmail">
                        <font-awesome-icon icon="save"/>&nbsp;
                        Save
                      </el-button>
                    </el-col>
                    <el-col :span="1">&nbsp;</el-col>
                  </el-row>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-main>
        </el-container>
      </div>
    </div>

    <scp-draggable-resizable w="1000px" h="550px" v-model="pageCtl.visible.newEmail" title="New Email Notification"
                             :save="saveNewEmail" :save-loading="pageCtl.loading.save">
      <template v-slot="{height}">
        <div class="new-email" :style="{height: height - 120 + 'px'}" style="overflow: auto">
          <el-row>
            <el-col :span="2" class="title">Job Name</el-col>
            <el-col :span="12" class="content">
              <el-input size="small" v-model="pageCtl.newEmail.name" placeholder="Job Name"></el-input>
            </el-col>
            <el-col :span="10" class="input-tips">
              任务名, 尽量用最少的字描述任务内容
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Groups</el-col>
            <el-col :span="4" class="content">
              <el-autocomplete
                  class="inline-input"
                  style="width: 100%"
                  v-model="pageCtl.newEmail.groups"
                  :maxlength="30"
                  size="small"
                  :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                  placeholder="Group"
                  show-word-limit
              ></el-autocomplete>
            </el-col>
            <el-col :span="18" class="input-tips">
              任务分组, 可以使用 <b>.</b> 进行层级分类, 如Level.Leve2.Leve3, 这样会生成三层目录
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Status</el-col>
            <el-col :span="4" class="content">
              <el-select v-model="pageCtl.newEmail.status" size="small">
                <el-option
                    v-for="item in ['Active','Inactive']"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="18" class="input-tips">
              任务状态, 如果暂时不使用的任务可以设置为Inactive, 确定不再使用的任务, 建议直接删除
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Trigger</el-col>
            <el-col :span="4" class="content">
              <el-select v-model="pageCtl.newEmail.month" size="small" placeholder="Month" filterable collapse-tags multiple style="width: 100%">
                <el-option
                    v-for="item in ['*', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="pageCtl.newEmail.dayType" size="small" placeholder="Day Type" class="search-group-left" @change="pageCtl.newEmail.day = []"
                         style="width: 100%">
                <el-option
                    v-for="item in ['DAY_OF_MONTH', 'DAY_OF_WEEK']"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4" class="content">
              <el-select v-model="pageCtl.newEmail.day" size="small" :placeholder="pageCtl.newEmail.dayType" filterable collapse-tags multiple
                         class="search-group-right"
                         style="width: 100%">
                <el-option
                    v-for="item in _dayRangeOfNew"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4" class="content">
              <el-select v-model="pageCtl.newEmail.hour" size="small" placeholder="Hour" filterable collapse-tags multiple style="width: 100%">
                <el-option
                    v-for="item in _hourRange"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="6" class="input-tips">
              任务触发配置, 精确到小时
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Recipient</el-col>
            <el-col :span="16" class="content">
              <el-select
                  v-model="pageCtl.newEmail.recipient"
                  placeholder="Recipient"
                  filterable
                  allow-create
                  collapse-tags-tooltip
                  multiple
                  clearable
                  :collapse-tags="true"
                  style="width: 100%">
                <el-option
                    v-for="(item,index) in pageCtl.emailUserList"
                    :key="index"
                    :label="item['USER_NAME'] + ' [' + item['EMAIL'] +']'"
                    :value="item['EMAIL']">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="6" class="input-tips">
              收件人邮箱地址
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">CC</el-col>
            <el-col :span="16" class="content">
              <el-select v-model="pageCtl.newEmail.cc" placeholder="CC" filterable
                         allow-create
                         multiple
                         collapse-tags-tooltip
                         clearable :collapse-tags="true" style="width: 100%">
                <el-option
                    v-for="(item,index) in pageCtl.emailUserList"
                    :key="index"
                    :label="item['USER_NAME'] + ' [' + item['EMAIL'] +']'"
                    :value="item['EMAIL']">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="6" class="input-tips">
              抄送人邮箱地址
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Subject</el-col>
            <el-col :span="16" class="content">
              <el-input size="small" v-model="pageCtl.newEmail.subject" placeholder="Subject" style="width: 100%"></el-input>
            </el-col>
            <el-col :span="6" class="input-tips">
              邮件主题
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Attachments</el-col>
            <el-col :span="2" class="content">
              <el-button type="default" @click="addAttachments" style="width: 100%">
                <font-awesome-icon icon="paperclip"></font-awesome-icon>
              </el-button>
            </el-col>
            <el-col :span="20" class="input-tips">
              邮件附件
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Contents</el-col>
            <el-col :span="21" class="content">
              <scp-ck-editor v-model="pageCtl.newEmail.contents" style="height:280px ;"/>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Remarks</el-col>
            <el-col :span="21" class="content">
              <el-input style="height: auto !important;" type="textarea" v-model="pageCtl.newEmail.remarks"></el-input>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Urls</el-col>
            <el-col :span="21" class="input-tips" style="height: auto">
              url:需要截图发送的链接, 可以使用DSS的Variant Sharing, 仅支持DSS<br>
              sleep:抓取等待, 单位秒, 爬虫无法确认页面何时加载完毕, 只能预设一个等待时间, 在等待后, 执行截图操作, 大部分页面15秒足够<br>
              zoom:默认抓取分辨率是1920*1080, 但有些页面内容较多, 可以通过调整zoom的值来缩放页面, 以达到可以在一个页面显示所有内容的目的<br>
              areaid:如果需要发送页面某一张报表的截图, 可以选择对应的报表ID. 如果需要整页截图, 此项留空. 报表ID可在角标处查看
            </el-col>
          </el-row>
          <el-row class="modifyUrlTable">
            <el-col :span="2">&nbsp;</el-col>
            <el-col :span="21">
              <el-button
                  size="small"
                  style="margin-bottom: 2px;float: right"
                  @click="addUrl(pageCtl.newEmail.urls)">
                <font-awesome-icon :icon="['fa-solid', 'fa-plus']"/>
              </el-button>
              <el-table :data="pageCtl.newEmail.urls"
                        border
                        size="small"
                        :cell-style="{height:'15px',padding:'0'}"
                        :header-cell-style="{height:'15px',padding:'0'}">
                <template v-slot:empty>
                  <span style="font-size: 5px">No Data</span>
                </template>
                <el-table-column
                    label="ID"
                    width=70
                    header-align="center"
                    prop="id">
                  <template v-slot="scope">
                    <div v-if="true">
                      <el-input v-model="scope.row.id"/>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                    label="URL"
                    header-align="center"
                    prop="url">
                  <template v-slot="scope">
                    <div v-if="true">
                      <el-input v-model="scope.row.url" placeholder="请输入需要截图的URL"/>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                    label="Sleep"
                    width="80"
                    align="center"
                    prop="sleep">
                  <template v-slot="scope">
                    <div v-if="true">
                      <el-input v-model="scope.row.sleep"/>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                    label="Zoom"
                    width="80"
                    align="center"
                    prop="zoom">
                  <template v-slot="scope">
                    <div v-if="true">
                      <el-input v-model="scope.row.zoom"/>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                    label="AreaID"
                    width="150"
                    align="center"
                    prop="areaid">
                  <template v-slot="scope">
                    <div v-if="true">
                      <el-select v-model="scope.row.areaid" size="small" placeholder="Area ID" filterable clearable style="width: 100%">
                        <el-option
                            v-for="item in pageCtl.reportIDList"
                            :key="item['REPORT_ID']"
                            :label="item['REPORT_ID'] + ' [' + item['TITLE'] +']'"
                            :value="item['REPORT_ID']">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                    width="80"
                    align="center"
                    label="Operate">
                  <template v-slot="scope">
                    <font-awesome-icon @click="pageCtl.newEmail.urls.splice(scope.$index, 1)"
                                       icon="times" font-size="15px" style="color: var(--scp-bg-color-error);margin-top: 4px;cursor: pointer"/>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </div>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable w="960px" h="520px" v-model="pageCtl.visible.attachment" title="Attachments" :save="saveAttachment" save-text="Apply">
      <template v-slot="{height}">
        <div :style="{height: height - 100 + 'px'}" style="overflow: auto;padding: 5px">
          <el-tabs
            v-model="pageCtl.activeAttachment"
            type="card"
            editable
            @edit="handleTabsEdit">
            <el-tab-pane
              v-for="item in pageCtl.attachments"
              :key="item.name"
              :label="item.title"
              :name="item.name">
              <div class="new-email" style="height: var(--scp-input-width);overflow: auto; padding-top: 0">
                <el-row>
                  <el-col :span="2" class="title">Name</el-col>
                  <el-col :span="16" class="content">
                    <el-input size="small" v-model="item.title" style="width: 100%"/>
                  </el-col>
                  <el-col :span="6" class="input-tips">
                    Sheet Name
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="2" class="title">SQL</el-col>
                  <el-col :span="21" class="content">
                    <scp-ace-editor v-model="item.content" lang="sql" width="auto" :style="{'height': (height - 230) + 'px'}"
                                    style="border-right: 1px solid var(--scp-border-color-lighter);border-bottom: 1px solid var(--scp-border-color-lighter);"/>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="23" class="input-tips" style="text-align: right;color:red">因API容量限制, 过大的附件将会导致邮件发送失败</el-col>
                  <el-col :span="1" class="input-tips">&nbsp;</el-col>
                </el-row>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onBeforeUnmount, onMounted, reactive, ref } from 'vue'
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import type { TabPaneName } from 'element-plus'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'
import { onBeforeRouteLeave } from 'vue-router'

const $randomString: any = inject('$randomString')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $startWith: any = inject('$startWith')
const $createMqttClient: any = inject('$createMqttClient')

const report2Ref = ref()
const treeRef = ref()

const pageCtl = reactive({
  mqttConnected: false,
  subscribeTopic: 'scp/dss/ui/email-notification/' + (localStorage.getItem('username') || 'nobody').toLowerCase(),
  publishTopic: 'scp/dss/email-notification/execute',
  client: {} as any,
  existsGroup: [],
  emailUserList: [],
  reportIDList: [],
  currentTab: 'first',
  quillOpt: {
    modules: {
      toolbar: [['bold', 'italic', 'underline', 'strike'], ['blockquote', 'code-block'], [{ header: 1 }, { header: 2 }], [{ list: 'ordered' }, { list: 'bullet' }], [{ script: 'sub' }, { script: 'super' }], [{ indent: '-1' }, { indent: '+1' }], [{ direction: 'rtl' }], [{ size: ['small', false, 'large', 'huge'] }], [{ header: [1, 2, 3, 4, 5, 6, false] }], [{ color: [] }, { background: [] }], [{ font: [] }], [{ align: [] }], ['clean']]
    }
  },
  loading: {
    report1: false,
    delete: false,
    runJob: false,
    save: false
  },
  visible: {
    tree: true,
    newEmail: false,
    addUrl: false,
    attachment: false
  },
  newEmail: {
    name: '',
    groups: '',
    dayType: 'DAY_OF_MONTH',
    month: ['*'],
    day: [],
    hour: [],
    status: 'Active',
    remarks: '',
    urls: [],
    recipient: '',
    cc: '',
    subject: '',
    contents: '',
    attachments: []
  },
  modifyEmail: {
    id: '',
    name: '',
    groups: '',
    dayType: 'DAY_OF_MONTH',
    month: ['*'],
    day: [],
    hour: [],
    status: 'Active',
    remarks: '',
    urls: [],
    recipient: '',
    cc: '',
    subject: '',
    contents: '',
    attachments: []
  },
  report1Data: {} as any,
  selectedId: '',
  selectedName: '',
  report2TableColumn: [
    { data: 'SEND_TIME' },
    { data: 'RECIPIENT' },
    { data: 'CC' },
    { data: 'RESULT_CODE' },
    { data: 'RESULT_TEXT', width: '200px' }
  ] as any,
  activeAttachment: 0,
  attachments: []
})

onMounted(() => {
  initPage()
  connectMqtt()
})

const closeMqttClient = () => {
  try {
    pageCtl.client.end()
  } catch (e) {

  }
}

onBeforeUnmount(() => {
  closeMqttClient()
})

onBeforeRouteLeave((to, from, next) => {
  closeMqttClient()
  next()
})

const connectMqtt = () => {
  pageCtl.client = $createMqttClient('scp-ui-email-notification')
  pageCtl.client.on('connect', () => {
    pageCtl.client.subscribe(pageCtl.subscribeTopic, { qos: 2 }, (err) => {
      if (!err) {
        pageCtl.mqttConnected = true
        console.log('subscribe topic: ' + pageCtl.subscribeTopic)
      } else {
        pageCtl.mqttConnected = false
        $message.error('mqtt connect failed, ' + err)
      }
    })
  })
  pageCtl.client.on('message', (topic, message) => {
    const messageStr = '' + message
    if ($startWith(messageStr, '[error]') === true) {
      $message.error(messageStr)
    } else {
      $message.success(messageStr)
      search()
    }
    pageCtl.loading.runJob = false
  })
}

const initPage = () => {
  $axios({
    method: 'post',
    url: '/toolbox/email_notification/init_page'
  }).then((body) => {
    pageCtl.existsGroup = body.existsGroup
    pageCtl.emailUserList = body.emailUserList
    pageCtl.reportIDList = body.reportIDList
  }).catch((error) => {
    console.log(error)
  })
}

const clickNode = (e) => {
  pageCtl.selectedId = e.key
  pageCtl.selectedName = e.label
  search()
}

const search = () => {
  searchReport1()
  searchReport2()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/toolbox/email_notification/query_report1',
    data: {
      id: pageCtl.selectedId
    }
  }).then((body) => {
    pageCtl.report1Data = body

    pageCtl.modifyEmail.id = body.id
    pageCtl.modifyEmail.name = body.NAME
    pageCtl.modifyEmail.groups = body.GROUP_NAME
    pageCtl.modifyEmail.dayType = body.DAY_TYPE
    pageCtl.modifyEmail.month = JSON.parse(body.MONTH)
    pageCtl.modifyEmail.day = JSON.parse(body.DAY)
    pageCtl.modifyEmail.hour = JSON.parse(body.HOUR)
    pageCtl.modifyEmail.status = body.STATUS
    pageCtl.modifyEmail.remarks = body.REMARKS
    pageCtl.modifyEmail.urls = body.urls
    pageCtl.modifyEmail.subject = body.subject
    pageCtl.modifyEmail.contents = body.contents
    pageCtl.modifyEmail.recipient = body.recipient
    pageCtl.modifyEmail.cc = body.cc
    pageCtl.modifyEmail.attachments = body.attachments

    pageCtl.attachments = body.attachments || []
    pageCtl.activeAttachment = pageCtl.attachments.length
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  nextTick(() => {
    report2Ref.value.search()
  })
}

const showNewEmailWin = () => {
  pageCtl.visible.newEmail = true
  pageCtl.attachments = []
  pageCtl.activeAttachment = 0
}

const saveNewEmail = () => {
  if (!checkAttachments()) {
    return
  }
  if (pageCtl.newEmail.name === '') {
    $message.error('Job name invalid')
  } else if (pageCtl.newEmail.recipient.length === 0) {
    $message.error(' Recipient  invalid')
  } else if (pageCtl.newEmail.subject === '') {
    $message.error(' Subject  invalid')
  } else {
    pageCtl.newEmail.attachments = pageCtl.attachments
    $axios({
      method: 'post',
      url: '/toolbox/email_notification/save_email',
      data: pageCtl.newEmail
    }).then(() => {
      initPage()
      loadTree()
      pageCtl.visible.newEmail = false
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.report1 = false
    })
  }
}

const modifyCurrentEmail = () => {
  if (!checkAttachments()) {
    return
  }
  if (pageCtl.modifyEmail.name === '') {
    $message.error('Job name invalid')
  } else if (pageCtl.modifyEmail.recipient.length === 0) {
    $message.error(' Recipient  invalid')
  } else if (pageCtl.modifyEmail.subject === '') {
    $message.error(' Subject  invalid')
  } else {
    pageCtl.modifyEmail.attachments = pageCtl.attachments
    $axios({
      method: 'post',
      url: '/toolbox/email_notification/modify_email',
      data: pageCtl.modifyEmail
    }).then(() => {
      initPage()
      loadTree()
      search()
      $message.success('Job saved')
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.report1 = false
    })
  }
}

const deleteEmail = () => {
  pageCtl.loading.delete = true
  $axios({
    method: 'post',
    url: '/toolbox/email_notification/delete_email',
    data: {
      id: pageCtl.selectedId
    }
  }).then(() => {
    initPage()
    loadTree()
    pageCtl.selectedId = ''
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.delete = false
  })
}

const addUrl = (urls) => {
  urls.push({
    id: '{' + $randomString(6) + '}',
    url: '',
    sleep: 15,
    zoom: 1,
    areaid: ''
  })
}

const runJob = () => {
  pageCtl.loading.runJob = true
  const content = { topic: pageCtl.subscribeTopic, id: pageCtl.selectedId }
  pageCtl.client.publish(pageCtl.publishTopic, JSON.stringify(content))
}

const loadTree = () => {
  treeRef.value.search()
}

const addAttachments = () => {
  pageCtl.visible.attachment = true
}

const checkAttachments = () => {
  for (const i in pageCtl.attachments) {
    if (pageCtl.attachments[i].title.trim() === '') {
      $message.error('Name of Sheet ' + (parseInt(i) + 1) + ' cannot be empty')
      return false
    }
    if (pageCtl.attachments[i].content.trim() === '') {
      $message.error('SQL script of Sheet ' + (parseInt(i) + 1) + ' cannot be empty')
      return false
    }
  }
  return true
}

const saveAttachment = () => {
  if (!checkAttachments()) {
    return
  }
  pageCtl.visible.attachment = false
}

const handleTabsEdit = (
  targetName: TabPaneName | undefined,
  action: 'remove' | 'add'
) => {
  if (action === 'add') {
    pageCtl.activeAttachment++
    pageCtl.attachments.push({
      title: 'Sheet ' + pageCtl.activeAttachment,
      name: pageCtl.activeAttachment,
      content: 'SELECT * FROM DUAL'
    })
  } else if (action === 'remove') {
    const tabs = pageCtl.attachments
    let activeName = pageCtl.activeAttachment
    if (activeName === targetName) {
      tabs.forEach((tab, index) => {
        if (tab.name === targetName) {
          const nextTab = tabs[index + 1] || tabs[index - 1]
          if (nextTab) {
            activeName = nextTab.name
          }
        }
      })
    }

    pageCtl.activeAttachment = activeName
    pageCtl.attachments = tabs.filter((tab) => tab.name !== targetName)
  }
}

const _showLastExecLogs = computed(() => {
  return !!pageCtl.report1Data.LAST_EXEC_RESULT_CODE
})

const _dayRangeOfNew = computed(() => {
  const result = ['*']
  if (pageCtl.newEmail.dayType === 'DAY_OF_MONTH') {
    for (let i = 1; i <= 31; i++) {
      result.push(i + '')
    }
  } else if (pageCtl.newEmail.dayType === 'DAY_OF_WEEK') {
    for (let i = 1; i <= 7; i++) {
      result.push(i + '')
    }
  }
  return result
})

const _dayRangeOfModify = computed(() => {
  const result = ['*']
  if (pageCtl.modifyEmail.dayType === 'DAY_OF_MONTH') {
    for (let i = 1; i <= 31; i++) {
      result.push(i + '')
    }
  } else if (pageCtl.modifyEmail.dayType === 'DAY_OF_WEEK') {
    for (let i = 1; i <= 7; i++) {
      result.push(i + '')
    }
  }
  return result
})

const _hourRange = computed(() => {
  const result = [] as Array<any>
  for (let i = 0; i <= 23; i++) {
    result.push(i + '')
  }
  return result
})

const _leftContainerHeight = computed(() => {
  return Math.max(document.documentElement.clientHeight - 550, 300)
})

</script>
<style lang="scss">
#emailNotification {
  .new-email {
    padding-top: 15px;
    padding-left: 10px;

    .el-button--default {
      background-color: transparent !important;
    }

    .title {
      text-align: right;
      padding-right: 8px;
      font-weight: bold;
      line-height: 2;
    }

    .content {
      padding-right: 5px;
    }

    .el-row {
      margin-bottom: 12px;
    }
  }

  .input-tips {
    padding-left: 5px;
    font-style: italic;
    color: var(--scp-text-color-secondary);
    line-height: 2;
  }

  .ql-toolbar.ql-snow {
    border: 1px solid var(--scp-border-color-lighter);
    background-color: var(--scp-bg-color-fill);
    box-sizing: border-box;
    font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
    padding: 8px;
  }

  .ql-container.ql-snow {
    border: 1px solid var(--scp-border-color-lighter);
    background-color: rgb(236 236 236 / 37%);
  }

  .modifyUrlTable {
    .el-input__wrapper {
      box-shadow: none;
      text-align: center;
      padding: 0;
      font-size: 0.42rem;
    }

    .el-table tbody tr:hover > td {
      background-color: unset !important //修改成自己想要的颜色即可
    }

    .el-table .cell {
      padding: 0;
    }

    .el-table__empty-block {
      min-height: 20px;
    }

    .el-table__empty-text {
      line-height: 20px;
    }
  }

  .el-tabs__new-tab {
    margin: 5px 0 5px 5px;
    border: none !important;
    height: auto !important;
    font-size: 0.45rem !important;
  }
  .el-tabs__nav {
    border-radius: 0 !important;
  }
  .is-icon-close {
    right: -10px !important;
  }
}
</style>
