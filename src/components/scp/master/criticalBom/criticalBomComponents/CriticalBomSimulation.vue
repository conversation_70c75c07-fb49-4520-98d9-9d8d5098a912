<template>
  <div class="left-sidebar" id="best_can_do">
    <div class="widget">
      <div class="widget-body">
        <el-container style="height: calc(100% - 10px)">
          <el-aside width="280px" style="border-right: 1px solid var(--scp-border-color);" class="left-container">
            <scp-tree-menu
                ref="treeRef"
                url="/master/critical_bom_calculator/query_batch_list"
                :new-click="showNewBatch"
                :node-click="clickNode"
            >
              <template v-slot="{ node, data }">
                <span class="custom-tree-node" :title="infoOverview(data.key)">
                    <slot :node="node" :data="data">
                      <span>{{ node.label }}
                        <span v-if="data.subLabel === 'not start'" style="color: var(--scp-text-color-secondary) !important; font-size: 10px;margin-left:5px" v-show="!!data.subLabel">{{data.subLabel}}</span>
                        <span v-if="data.subLabel === 'processing'" style="color: var(--scp-text-color-success) !important; font-size: 10px;margin-left:5px" v-show="!!data.subLabel">{{data.subLabel}}</span>
                        <span v-if="data.subLabel === 'finished'" style="color: var(--scp-text-color-highlight) !important; font-size: 10px;margin-left:5px" v-show="!!data.subLabel">{{data.subLabel}}</span>
                        <span v-show="data.children.length > 0">({{ data.children.length }})</span>
                        <span v-show="!!data.subLabel">
                            <div class="pull-right" style="width: 96px;text-align: right">
                               <el-tooltip class="item" effect="light" :show-after="1000" content="Share Variant">
                                 <font-awesome-icon class="add-name" style="line-height:24px;margin-right: 8px" icon="fa-solid fa-share-nodes"
                                                    @click="shareCondition(data.key, data.label)"/>
                               </el-tooltip>
                            </div>
                        </span>
                      </span>
                    </slot>
                </span>
              </template>
            </scp-tree-menu>
          </el-aside>
          <el-main style="padding: 0 var(--scp-widget-margin)">
            <h1 v-show="!pageCtl.conditions.batchId" style="text-align: center; color: var(--scp-text-color-lighter);height: 300px;line-height: 300px; font-size: xx-large">Select or Create a Simulation</h1>
            <el-tabs v-show="!!pageCtl.conditions.batchId" v-model="pageCtl.currentTab" @tab-click="handleTabClick">
              <el-tab-pane label="Demand" name="first">
                <div v-show="!!pageCtl.selectedBatchInfo['USER_NAME']" v-loading="pageCtl.loading.selectedBatchInfo" style="margin-bottom: var(--scp-widget-margin); display: flex; align-items: center;">
                  <h3 style="margin-right: 35px;display: inline;">
                    {{ pageCtl.selectedBatchInfo['NAME'] }}
                    <span class="maintain-by"> by {{ pageCtl.selectedBatchInfo['USER_NAME'] }}({{ pageCtl.selectedBatchInfo['SESA_CODE'] }})</span>
                  </h3>
                  <el-button size="small" @click="sendSimulateRequest" type="primary" :loading="_selectedBatchExecting">
                    <font-awesome-icon :icon="['far', 'pen-to-square']" />&nbsp;
                    Simulate
                  </el-button>
                  <el-popconfirm title="确定删除此任务?"
                                 iconColor="var(--scp-text-color-error)"
                                 @confirm="deleteBatch"
                                 confirmButtonType="danger"
                                 cancelButtonType="primary"
                                 confirmButtonText='确定'
                                 cancelButtonText='取消'
                                 style="margin-left:15px">
                    <template #reference>
                      <el-button type="danger" size="small" :loading="pageCtl.loading.delete" v-show="!_selectedBatchExecting">
                        <font-awesome-icon :icon="['fa-solid', 'fa-xmark']"/>&nbsp;
                        Delete
                      </el-button>
                    </template>
                  </el-popconfirm>
                </div>
                <div class="info-banner" style="margin-bottom: var(--scp-widget-margin)">
                  <div class="info-banner-content">
                    <div class="info-banner-icon">
                      <font-awesome-icon icon="fa-solid" />
                    </div>
                    <div class="info-banner-text">
                      <div class="info-banner-title">操作指引</div>
                      <div class="info-banner-description">请上传你想要模拟的成品料号信息，系统将基于此数据进行BOM计算分析</div>
                    </div>
                  </div>
                </div>
                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin)" >
                  <div class="subscript-title">Demand List</div>
                  <scp-subscript id="SCDP"/>
                  <scp-table
                      ref="startTableRef"
                      :params="pageCtl.conditions"
                      url="/master/critical_bom_calculator/query_start"
                      download-url="/master/critical_bom_calculator/download_start"
                      save-url="/master/critical_bom_calculator/save_start"
                      :lazy="true"
                      :contextMenuItems="pageCtl.startMenuItems"
                      :columns="pageCtl.startColumn"
                      :hiddenColumns="{columns: [0]}"
                      :update-data-enable="true"
                      :create-data-enable="true"/>
                </div>
              </el-tab-pane>
              <el-tab-pane label="Database" name="second">
                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin)" >
                  <div class="subscript-title">Demand Input</div>
                  <scp-subscript id="SCDP"/>
                  <scp-table
                      ref="report1TableRef"
                      :params="pageCtl.conditions"
                      url="/master/critical_bom_calculator/query_report1"
                      download-url="/master/critical_bom_calculator/download_report1"
                      save-url="/master/critical_bom_calculator/save_report1"
                      :lazy="true"
                      :contextMenuItems="pageCtl.report1MenuItems"
                      :columns="pageCtl.report1Column"
                      :hiddenColumns="{columns: [0]}"
                      :update-data-enable="true"
                      :create-data-enable="true"/>
                </div>

                <div class="subscript-container">
                  <div class="subscript-title">BOM List</div>
                  <scp-subscript id="SCDD"/>
                  <scp-table
                      :params="pageCtl.conditions"
                      url="/master/critical_bom_calculator/query_report2"
                      download-url="/master/critical_bom_calculator/download_report2"
                      save-url="/master/critical_bom_calculator/save_report2"
                      ref="report2TableRef"
                      :contextMenuItems="pageCtl.report2MenuItems"
                      :lazy="true"
                      :columns="pageCtl.report2Column"
                      :hiddenColumns="{columns: [0]}"
                      :update-data-enable="true"
                      :create-data-enable="true"/>
                </div>
                <div class="subscript-container" v-show="pageCtl.selectedBatchInfo.MODULE==='module3'">
                  <div class="subscript-title">Fix Recourse List</div>
                  <scp-subscript id="SCDA"/>
                  <scp-table
                      :params="pageCtl.conditions"
                      url="/master/critical_bom_calculator/query_report11"
                      download-url="/master/critical_bom_calculator/download_report11"
                      save-url="/master/critical_bom_calculator/save_report11"
                      ref="report11TableRef"
                      :contextMenuItems="pageCtl.report11MenuItems"
                      :lazy="true"
                      :columns="pageCtl.report11Column"
                      :hiddenColumns="{columns: [0]}"
                      :update-data-enable="true"
                      :create-data-enable="true"/>
                </div>
                <div class="subscript-container" v-show="pageCtl.selectedBatchInfo.MODULE==='module1'|| pageCtl.selectedBatchInfo.MODULE=== null">
                  <div class="subscript-title">Recourse Input</div>
                  <scp-subscript id="SCDA"/>
                  <scp-table
                      :params="pageCtl.conditions"
                      url="/master/critical_bom_calculator/query_report3"
                      download-url="/master/critical_bom_calculator/download_report3"
                      save-url="/master/critical_bom_calculator/save_report3"
                      ref="report3TableRef"
                      :contextMenuItems="pageCtl.report3MenuItems"
                      :lazy="true"
                      :columns="pageCtl.report3Column"
                      :hiddenColumns="{columns: [0]}"
                      :update-data-enable="true"
                      :create-data-enable="true"/>
                </div>

                <div class="subscript-container" v-show="pageCtl.selectedBatchInfo.MODULE==='module2' || pageCtl.selectedBatchInfo.MODULE==='module3'">
                  <div class="subscript-title">Recourse Input</div>
                  <scp-subscript id="SCDA"/>
                  <scp-table
                      :params="pageCtl.conditions"
                      url="/master/critical_bom_calculator/query_report9"
                      download-url="/master/critical_bom_calculator/download_report9"
                      save-url="/master/critical_bom_calculator/save_report9"
                      ref="report9TableRef"
                      :contextMenuItems="pageCtl.report9MenuItems"
                      :lazy="true"
                      :columns="pageCtl.report9Column"
                      :hiddenColumns="{columns: [0]}"
                      :update-data-enable="true"
                      :create-data-enable="true"/>
                </div>
              </el-tab-pane>
              <el-tab-pane label="BCD Log" name="third">
                <pre id="batchLogsPre" :style="{height: _batchLogsHeight + 'px', overflow: 'auto'}" v-loading="pageCtl.loading.logging">{{ pageCtl.batchLogs.join('\r\n') }}
                </pre>
              </el-tab-pane>
              <el-tab-pane label="Result" name="forth">
                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin);" v-show="pageCtl.selectedBatchInfo.MODULE==='module1' || pageCtl.selectedBatchInfo.MODULE===null">
                  <div class="subscript-title">Best Can Do</div>
                  <scp-subscript id="SCDR"/>
                  <scp-table
                      :download-specify-column="false"
                      :params="pageCtl.conditions"
                      url="/master/critical_bom_calculator/query_report4"
                      download-url="/master/critical_bom_calculator/download_report4"
                      ref="report4TableRef"
                      :lazy="true"
                      :columns="pageCtl.report4Column"
                      :editable="false"/>
                </div>

                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin);" v-show="pageCtl.selectedBatchInfo.MODULE==='module1' || pageCtl.selectedBatchInfo.MODULE===null">
                  <div class="subscript-title">Recourse Shortage</div>
                  <scp-subscript id="SCDE"/>
                  <scp-table
                      :download-specify-column="false"
                      :params="pageCtl.conditions"
                      url="/master/critical_bom_calculator/query_report5"
                      download-url="/master/critical_bom_calculator/download_report5"
                      ref="report5TableRef"
                      :lazy="true"
                      :columns="pageCtl.report5Column"
                      :editable="false"/>
                </div>

                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin);" v-show="pageCtl.selectedBatchInfo.database==='module2' || pageCtl.selectedBatchInfo.database==='module3'">
                  <div class="subscript-title">Output Max</div>
                  <scp-subscript id="SCDE"/>
                  <scp-table
                      :download-specify-column="false"
                      :params="pageCtl.conditions"
                      url="/master/critical_bom_calculator/query_report10"
                      download-url="/master/critical_bom_calculator/download_report10"
                      ref="report10TableRef"
                      :lazy="true"
                      :columns="pageCtl.report10Column"
                      :editable="false"/>
                </div>

                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin);">
                  <div class="subscript-title">Remain Recourse</div>
                  <scp-subscript id="SCDE"/>
                  <scp-table
                      :download-specify-column="false"
                      :params="pageCtl.conditions"
                      url="/master/critical_bom_calculator/query_report8"
                      download-url="/master/critical_bom_calculator/download_report8"
                      ref="report8TableRef"
                      :lazy="true"
                      :columns="pageCtl.report8Column"
                      :editable="false"/>
                </div>
              </el-tab-pane>
              <el-tab-pane label="Parameter" name="fifth">
                <pre v-loading="pageCtl.loading.selectedBatchInfo">{{pageCtl.selectedBatchInfo['PARAMS']}}</pre>
              </el-tab-pane>
              <el-tab-pane label="Configuration" name="fourth">
                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin);">
                  <div class="subscript-title">Task History</div>
                  <scp-table
                      :params="pageCtl.conditions"
                      :max-height="600"
                      ref="taskQueueTableRef"
                      url="/master/critical_bom_calculator/query_task_queue"
                      :lazy="true"
                      :columns="pageCtl.taskQueueColumns"/>
                </div>
                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin);">
                  <div class="subscript-title">Component Substitution</div>
                  <scp-subscript id="SCDE"/>
                  <scp-table
                      :params="pageCtl.conditions"
                      :download-specify-column="false"
                      url="/master/critical_bom_calculator/query_report7"
                      download-url="/master/critical_bom_calculator/download_report7"
                      save-url="/master/critical_bom_calculator/save_report7"
                      ref="report7TableRef"
                      :contextMenuItems="pageCtl.report7MenuItems"
                      :lazy="true"
                      :columns="pageCtl.report7Column"
                      :hiddenColumns="{columns: [0]}"
                      :update-data-enable="true"
                      :create-data-enable="true"/>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-main>
        </el-container>
      </div>
    </div>

    <!-- new batch -->
    <scp-draggable-resizable w="700px" h="550px" v-model="pageCtl.visible.newBatch" title="New Task"
                             save-text="Create Task" :save="createBatch" :save-loading="pageCtl.loading.newBatch">
      <template v-slot="{height}">
        <el-form ref="newBatchForm" :model="pageCtl.newBatchForm" label-width="150px" :style="{ height: height - 120 + 'px'}" style="padding:20px 15px 5px 5px">
          <el-form-item label="New Task Name" required prop="name">
            <el-row>
              <el-col :span="16">
                <el-input v-model="pageCtl.newBatchForm.name" placeholder="Task Name" size="small" style="width: 100%"></el-input>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="Group" required prop="groups">
            <el-row>
              <el-col :span="16">
                <el-autocomplete
                    class="inline-input"
                    v-model="pageCtl.newBatchForm.groups"
                    style="width: 100%"
                    :maxlength="30"
                    size="small"
                    :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                    placeholder="Task Group"
                    show-word-limit
                ></el-autocomplete>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="Plant Code" required prop="plant">
            <el-row>
              <el-col :span="16">
                <el-select size="small" v-model="pageCtl.newBatchForm.plant" placeholder="Plant" style="width: 100%;" filterable  >
                  <el-option
                      v-for="item in pageCtl.plantOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="Database" required prop="database">
            <el-row>
              <el-col :span="16">
                <el-select v-model="pageCtl.newBatchForm.database" size="small" style="width: 100%;" placeholder="Mode" >
                  <el-option label="CSF" value="CSF" />
                  <el-option label="MPP" value="MPP" disabled />
                  <el-option label="MO" value="MO" disabled />
                  <el-option label="DC INVENTORY" value="DC" disabled />
                </el-select>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item :label="pageCtl.newBatchForm.database === 'MO' ? 'BASIC_START_DATE' : (pageCtl.newBatchForm.database === 'CSF' ? 'Month' : '&nbsp;')" v-if="['MO', 'CSF'].includes(pageCtl.newBatchForm.database)" required prop="dateRange">
            <el-row v-show="pageCtl.newBatchForm.database === 'MO' || pageCtl.newBatchForm.database === 'CSF'">
              <el-col :span="16">
                <el-date-picker
                    size="small"
                    style="width: calc(100% - 20px)"
                    v-model="pageCtl.newBatchForm.dateRange"
                    type="monthrange"
                    unlink-panels
                    format="YYYY/MM"
                    value-format="YYYY/MM"
                    range-separator="to"
                    :clearable="true"
                    start-placeholder="Start Month"
                    end-placeholder="End Month"
                    :disabled-date="disabledDate">
                </el-date-picker>
              </el-col>
            </el-row>
          </el-form-item>
          <!--<el-form-item  label="Time Category" required prop="timeCategory">
            <el-row>
              <el-col :span="16">
                <el-select v-model="pageCtl.newBatchForm.timeCategory" size="small" style="width: 100%" placeholder="时间类型">
                  <el-option label="DAY" value="DAY"/>
                  <el-option label="WEEK" value="WEEK"/>
                  <el-option label="MONTH" value="MONTH"/>
                </el-select>
              </el-col>
            </el-row>
          </el-form-item>-->
          <el-form-item label="PO">
            <el-row>
              <el-col :span="16">
                <scp-cascader showCopy="false" v-model="pageCtl.newBatchForm.po" :options="pageCtl.poOpts" style="width: 100%"
                              placeholder="PO Filters"/>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="Stock Configure">
            <el-row>
              <el-col :span="16">
                <el-select size="small" v-model="pageCtl.newBatchForm.stockConfig" style="width: 100%" multiple clearable filterable collapse-tags>
                  <el-option
                      v-for="item in ['UU_STOCK', 'STOCK_IN_QI', 'BLOCKED_STOCK', 'RESTRICTED_STOCK', 'RETURNS_STOCK', 'INTER_STOCK_TRANSFER']"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
          </el-form-item>
        </el-form>
      </template>
    </scp-draggable-resizable>
    <!-- upload start list -->
    <scp-upload
        ref="startRef"
        title="Upload Demand start list"
        :show-btn="false"
        w="600px"
        h="200px"
        upload-url='/master/critical_bom_calculator/upload_start'
        download-template-url='/master/critical_bom_calculator/download_start_template'
        :on-upload-end="() => startTableRef.search()"
        :upload-params="{'batchId' : pageCtl.conditions.batchId}">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>
    <!-- upload report1 -->
    <scp-upload
        ref="upload1Ref"
        title="Upload Demand input"
        :show-btn="false"
        w="600px"
        h="200px"
        upload-url='/master/critical_bom_calculator/upload_report1'
        download-template-url='/master/critical_bom_calculator/download_report1_template'
        :on-upload-end="() => report1TableRef.search()"
        :upload-params="{'batchId' : pageCtl.conditions.batchId}">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>

    <!-- upload report2 -->
    <scp-upload
        ref="upload2Ref"
        title="Upload BOM List"
        :show-btn="false"
        w="600px"
        h="200px"
        upload-url='/master/critical_bom_calculator/upload_report2'
        download-template-url='/master/critical_bom_calculator/download_report2_template'
        :on-upload-end="() => report2TableRef.search()"
        :upload-params="{'batchId' : pageCtl.conditions.batchId}">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>

    <!-- upload report11 -->
    <scp-upload
        ref="upload11Ref"
        title="Upload Fix Recourse List"
        :show-btn="false"
        w="600px"
        h="200px"
        upload-url='/master/critical_bom_calculator/upload_report11'
        download-template-url='/master/critical_bom_calculator/download_report11_template'
        :on-upload-end="() => report11TableRef.search()"
        :upload-params="{'batchId' : pageCtl.conditions.batchId}">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>

    <!-- upload report3 -->
    <scp-upload
        ref="upload3Ref"
        title="Upload Recourse Input"
        :show-btn="false"
        w="600px"
        h="200px"
        upload-url='/master/critical_bom_calculator/upload_report3'
        download-template-url='/master/critical_bom_calculator/download_report3_template'
        :on-upload-end="() => report3TableRef.search()"
        :upload-params="{'batchId' : pageCtl.conditions.batchId}">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>
    <!-- upload report9 -->
    <scp-upload
        ref="upload9Ref"
        title="Upload Recourse Input"
        :show-btn="false"
        w="600px"
        h="200px"
        upload-url='/master/critical_bom_calculator/upload_report9'
        download-template-url='/master/critical_bom_calculator/download_report9_template'
        :on-upload-end="() => report9TableRef.search()"
        :upload-params="{'batchId' : pageCtl.conditions.batchId}">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>
    <scp-upload
        ref="upload7Ref"
        title="Upload Component Substitution"
        :show-btn="false"
        w="600px"
        h="200px"
        upload-url='/master/critical_bom_calculator/upload_report7'
        download-template-url='/master/critical_bom_calculator/download_report7_template'
        :on-upload-end="() => report7TableRef.search()"
        :upload-params="{'batchId' : pageCtl.conditions.batchId}">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>
    <!-- share email-->
    <scp-draggable-resizable v-model="visibleCtl.share" :save="shareConditionAction" :save-loading="loadingCtl.share" save-text="Share"
                             w="60vw" h="600px" :title="data.shareTitle">
      <template v-slot="{height}">
        <div style="padding: 5px 5px 0 5px;">
          <el-row>
            <el-col :span="24">
              <el-select v-model="data.sharedUsers" :placeholder="loadingCtl.query ? 'Loading...' : 'Share to'" style="width: 100% !important;"
                         collapse-tags clearable multiple filterable>
                <el-option
                    v-for="user in data.allUsers" :key="user['VAL']" :label="user['LABEL']" :value="user['VAL']">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="24">
              <scp-ck-editor v-model="data.remarks" :style="{height: (height - 180) + 'px'}"/>
            </el-col>
          </el-row>
        </div>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import ScpUpload from '@/components/starter/components/Upload.vue'
import { computed, inject, onMounted, reactive, ref, nextTick, watch, onBeforeUnmount } from 'vue'
import search from '@/components/starter/components/Search.vue'
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'
import { onBeforeRouteLeave } from 'vue-router'

const $thousandBitSeparator:any = inject('$thousandBitSeparator')
const $shortenNumber:any = inject('$shortenNumber')
const $px2Rem:any = inject('$px2Rem')
const $randomString:any = inject('$randomString')
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $isEmpty: any = inject('$isEmpty')
const $createMqttClient: any = inject('$createMqttClient')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')

const treeRef = ref()
const startTableRef = ref()
const report1TableRef = ref()
const report2TableRef = ref()
const report11TableRef = ref()
const report3TableRef = ref()
const report4TableRef = ref()
const report5TableRef = ref()
const report10TableRef = ref()
const report6TableRef = ref()
const report7TableRef = ref()
const report8TableRef = ref()
const report9TableRef = ref()
const aptExcludeTableRef = ref()
const taskQueueTableRef = ref()
const upload1Ref = ref()
const upload2Ref = ref()
const upload11Ref = ref()
const upload3Ref = ref()
const upload9Ref = ref()
const upload7Ref = ref()

onMounted(() => {
  connectMqtt()
  initPage()
  initExistsGroup()
  pageCtl.pageWidth = document.documentElement.clientWidth - 360
})

const closeMqttClient = () => {
  try {
    pageCtl.client.end()
  } catch (e) {

  }
}

onBeforeUnmount(() => {
  closeMqttClient()
})

onBeforeRouteLeave((to, from, next) => {
  closeMqttClient()
  next()
})

const showUpload1Win = () => {
  upload1Ref.value.showUploadWin()
}

const showUpload2Win = () => {
  upload2Ref.value.showUploadWin()
}

const showUpload11Win = () => {
  upload11Ref.value.showUploadWin()
}

const showUpload3Win = () => {
  upload3Ref.value.showUploadWin()
}

const showUpload9Win = () => {
  upload9Ref.value.showUploadWin()
}

const showUpload7Win = () => {
  upload7Ref.value.showUploadWin()
}

const pageCtl = reactive({
  pageWidth: 0 as any,
  currentTab: '',
  client: {} as any,
  moOpts: [],
  soOpts: [],
  poOpts: [],
  plantOpts: [],
  existsGroup: [],
  bindTable: 'BCD_REMOVE_RM',
  visible: {
    newBatch: false,
    simulate: false
  },
  loading: {
    filter: false,
    logging: false,
    newBatch: false,
    delete: false,
    simulateRequest: false,
    selectedBatchInfo: false
  },
  firstLoad: {
    tab1: false,
    tab2: false,
    tab3: false,
    tab4: false,
    tab6: false
  },
  OverviewData: {
    LEFT_BCD: 0,
    LEFT_DEMAND: 0,
    LEFT_SEMI_FINISH_GOODS: 0,
    RIGHT_BCD: 0,
    RIGHT_DEMAND: 0,
    RIGHT_SEMI_FINISH_GOODS: 0
  },
  conditions: {
    leafDepth: 2,
    batchId: '',
    dateRange: [],
    database: '',
    taskNameList: [],
    taskInfo: [],
    selectedValue: '',
    selectedxAxis: [],
    xAxisOpts: [],
    level1: '',
    level2: '',
    report1Tooltips: [],
    startTooltips: [],
    balanceValue: 0
  },
  selectedBatchInfo: {
    NAME: '',
    USER_NAME: '',
    SESA_CODE: '',
    WORK_MODE: '',
    PARAMS: '',
    database: ''
  } as any,
  newBatchForm: {
    name: '',
    groups: '',
    plant: '',
    timeCategory: '',
    database: '',
    po: [],
    stockConfig: ['UU_STOCK', 'STOCK_IN_QI'],
    strategy: 'QTY_BEST',
    type: 'WHOLE_LINE',
    dateRange: [],
    priority: [],
    step: '',
    step_mode: '',
    addPO: '',
    minusFcst: ''
  },
  startMenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload1Win
    },
    view_split: { name: '---------' }
  },
  report1MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload1Win
    },
    view_split: { name: '---------' }
  },
  report2MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload2Win
    },
    view_split: { name: '---------' }
  },
  report11MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload11Win
    },
    view_split: { name: '---------' }
  },
  report3MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload3Win
    },
    view_split: { name: '---------' }
  },
  report9MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload9Win
    },
    view_split: { name: '---------' }
  },
  report7MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload7Win
    },
    view_split: { name: '---------' }
  },
  startColumn: [
    { data: 'ROW_ID' },
    { data: 'MATERIAL' },
    { data: 'PLANT_CODE' }
  ],
  report1Column: [
    { data: 'ROW_ID' },
    { data: 'PRIMARY_KEY' },
    { data: 'MATERIAL' },
    { data: 'PRIORITY' },
    { data: 'QTY', type: 'strict-numeric' }

  ],
  report2Column: [
    { data: 'ROW_ID' },
    { data: 'MATERIAL' },
    { data: 'BOM_COMPONENT' },
    { data: 'USAGE', type: 'strict-numeric' }
  ],
  report11Column: [
    { data: 'ROW_ID' },
    { data: 'FIX_RECOURSE' }
  ],
  report9Column: [
    { data: 'ROW_ID' },
    { data: 'BOM_COMPONENT' },
    { data: 'STEP0', type: 'strict-numeric' }
  ],
  report4Column: [
  ],
  report5Column: [
  ],
  report10Column: [
  ],
  report8Column: [
  ],
  report6Column: [
  ],
  report7Column: [
    { data: 'ROW_ID' },
    { data: 'MATERIAL_IN_BOM' },
    { data: 'FOLLOW_UP_MATERIAL' }
  ],
  simulateForm: {
    batchId: '',
    type: '',
    strategy: '',
    database: '',
    balanceValue: ''
  },
  batchLogs: [] as Array<any>,
  taskQueueColumns: [
    { data: 'BATCH_ID', title: 'Task ID' },
    { data: 'NAME', title: 'Task Name' },
    { data: 'USERNAME', title: 'Username' },
    { data: 'WORK_MODE', title: 'Work Mode' },
    { data: 'CREATE_DATE', title: 'Create Time' },
    { data: 'START_TIME', title: 'Start Time' },
    { data: 'END_TIME', title: 'End Time' },
    { data: 'EXEC_TIME_IN_MIN', title: 'Time Cost(min)', type: 'numeric', precision: 1 },
    { data: 'STATUS', title: 'Status' }
  ]
})

interface Conditions {
  COND_ID: string,
  CONDITIONS: string,
  SHARED_BY: string,
  NAME: string,
  WITH_DATE: string,
  SHARED_REMARKS: string,
  IS_DEFAULT: string
}

const data = reactive({
  batch_id: '',
  cid: '',
  name: '',
  withDate: false,
  allUsers: [],
  sharedUsers: [],
  remarks: '',
  shareTitle: '',
  conditions: [] as Array<Conditions>,
  expand: false,
  enableUrlShare: false,
  enableDefaultPage: false
})

const connectMqtt = () => {
  const username = (localStorage.getItem('username') || 'nobody').toLowerCase()
  const topic = 'scp/dss/ui/bcd/' + username
  pageCtl.client = $createMqttClient('scp-ui-best-can-do')
  pageCtl.client.on('connect', () => {
    pageCtl.client.subscribe(topic, { qos: 0 }, (err) => {
      if (!err) {
        console.log('subscribe topic: ' + topic)
      }
    })
  })

  pageCtl.client.on('message', (topic, message) => {
    try {
      const logObj = JSON.parse(message + '')
      pageCtl.batchLogs.push(logObj.time + ' - ' + logObj.message)
    } catch (e) {
      console.warn(e)
    }
  })
}

const initPage = () => {
  queryTaskInfo()
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/master/critical_bom_calculator/init_page'
  }).then((body) => {
    pageCtl.moOpts = body.moList
    pageCtl.soOpts = body.soList
    pageCtl.poOpts = body.poList
    pageCtl.plantOpts = body.plantList
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const createTypeChanged = () => {
  pageCtl.newBatchForm = {
    name: '',
    groups: '',
    plant: '',
    timeCategory: '',
    stockConfig: ['UU_STOCK', 'STOCK_IN_QI'],
    po: [],
    database: '',
    strategy: 'QTY_BEST',
    type: 'WHOLE_LINE',
    dateRange: [],
    priority: [],
    step: '',
    step_mode: '',
    addPO: '',
    minusFcst: ''
  }
}

const queryTaskInfo = () => {
  $axios({
    method: 'post',
    url: '/master/critical_bom_calculator/query_task_info'
  }).then((body) => {
    pageCtl.conditions.taskNameList = body.nameList
    pageCtl.conditions.taskInfo = body.taskInfo
  }).catch((error) => {
    console.log(error)
  })
}

const generateTask = () => {
  let batchId:any = pageCtl.conditions.selectedValue[1].split('@')
  batchId = batchId[batchId.length - 1]
  pageCtl.newBatchForm = JSON.parse(pageCtl.conditions.taskInfo[batchId])
}

const initExistsGroup = () => {
  $axios({
    method: 'post',
    url: '/simulation/best_can_do/init_exists_group'
  }).then((body) => {
    pageCtl.existsGroup = body
  }).catch((error) => {
    console.log(error)
  })
}

const showNewBatch = () => {
  createTypeChanged()
  pageCtl.visible.newBatch = true
}

const clickNode = (e) => {
  const taskInfo = JSON.parse(pageCtl.conditions.taskInfo[e.key])
  const dateRange = taskInfo.dateRange
  pageCtl.conditions.dateRange = dateRange
  resetTabSearch()
  pageCtl.currentTab = 'first'
  pageCtl.conditions.batchId = e.key
  nextTick(() => {
    searchTab1()
  })
}

const createBatch = () => {
  if (pageCtl.newBatchForm.name === '') {
    $message.error('Please input the task name')
    return
  }
  if (pageCtl.newBatchForm.groups === '') {
    $message.error('Please input a groups')
    return
  }
  if (pageCtl.newBatchForm.plant === '') {
    $message.error('Please select a plant code')
    return
  }
  pageCtl.loading.newBatch = true
  $axios({
    method: 'post',
    url: '/master/critical_bom_calculator/save_new_batch',
    data: pageCtl.newBatchForm
  }).then((body) => {
    pageCtl.conditions.batchId = body
    pageCtl.conditions.dateRange = body
    pageCtl.visible.newBatch = false
    treeRef.value.search()
    queryTaskInfo()
    initExistsGroup()
    infoOverview()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.newBatch = false
  })
}

const infoOverview = (key?) => {
  if (key !== null) {
    const taskInfo = JSON.parse(pageCtl.conditions.taskInfo[key])
    let addPO:any = taskInfo.addPO
    let minusFcst:any = taskInfo.minusFcst
    if (addPO === 'true') {
      addPO = 'ADD_PO'
    } else {
      addPO = 'NONE_PO'
    }
    if (minusFcst === 'true') {
      minusFcst = 'MINUS_FCST'
    } else {
      minusFcst = 'NONE_FCST'
    }
    return taskInfo.name + ' ' + addPO + ' ' + minusFcst
  } else {
    return null
  }
}

const searchTab1 = () => {
  pageCtl.firstLoad.tab1 = true
  pageCtl.loading.selectedBatchInfo = true
  $axios({
    method: 'post',
    url: '/master/critical_bom_calculator/query_batch_info',
    data: pageCtl.conditions
  }).then(body => {
    pageCtl.selectedBatchInfo = body
  }).catch(err => {
    console.warn(err)
  }).finally(() => {
    pageCtl.loading.selectedBatchInfo = false
  })
  startTableRef.value.search()
  report1TableRef.value.search()
  report2TableRef.value.search()
  report11TableRef.value.search()
  report3TableRef.value.search()
  report9TableRef.value.search()
}

const searchTab2 = () => {
  pageCtl.firstLoad.tab2 = true
  pageCtl.loading.logging = true
  $axios({
    method: 'post',
    url: '/master/critical_bom_calculator/query_execute_logs',
    data: {
      batchId: pageCtl.conditions.batchId
    }
  }).then((body) => {
    pageCtl.batchLogs = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.logging = false
  })
}

const searchTab3 = () => {
  pageCtl.firstLoad.tab3 = true
  report4TableRef.value.search()
  report5TableRef.value.search()
  report10TableRef.value.search()
  report8TableRef.value.search()
  // report6TableRef.value.search()
}

const searchTab4 = () => {
  pageCtl.firstLoad.tab4 = true
  report7TableRef.value.search()
}

const resetTabSearch = () => {
  pageCtl.firstLoad.tab1 = false
  pageCtl.firstLoad.tab2 = false
  pageCtl.firstLoad.tab3 = false
  pageCtl.firstLoad.tab4 = false
  pageCtl.firstLoad.tab6 = false
  pageCtl.batchLogs = []
}

const deleteBatch = () => {
  pageCtl.loading.delete = true
  $axios({
    method: 'post',
    url: '/master/critical_bom_calculator/delete_batch',
    data: pageCtl.conditions
  }).then((body) => {
    if (parseInt(body) === 0) {
      $message.success('Task deleted')
      pageCtl.conditions.batchId = ''
      treeRef.value.search()
      initExistsGroup()
    } else {
      $message.error('You cannot delete this task')
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.delete = false
  })
}

const showSimulate = () => {
  pageCtl.visible.simulate = true
}

const sendSimulateRequest = () => {
  pageCtl.loading.simulateRequest = true
  pageCtl.simulateForm.batchId = pageCtl.conditions.batchId
  pageCtl.simulateForm.type = pageCtl.newBatchForm.type
  pageCtl.simulateForm.strategy = pageCtl.newBatchForm.strategy
  pageCtl.simulateForm.database = pageCtl.conditions.database
  pageCtl.simulateForm.balanceValue = pageCtl.conditions.balanceValue
  $axios({
    method: 'post',
    url: '/master/critical_bom_calculator/send_simulate_request',
    data: pageCtl.simulateForm
  }).then((body) => {
    console.log(pageCtl.simulateForm.balanceValue)
    if (body === '0') {
      $message.success('Request sent')
      pageCtl.visible.simulate = false
      pageCtl.currentTab = 'second'
    } else {
      $message.error(body)
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.simulateRequest = false
  })
}

const scrollBatchLogs = () => {
  const batchLogsPre:any = document.getElementById('batchLogsPre')
  if (batchLogsPre.scrollTop === 0 || batchLogsPre.scrollHeight - batchLogsPre.scrollTop - _batchLogsHeight.value < 500) {
    batchLogsPre.scrollTo({ top: batchLogsPre.scrollHeight, behavior: 'smooth' })
  }
}

const visibleCtl = reactive({
  pop: false,
  share: false
})

const loadingCtl = reactive({
  share: false,
  query: false,
  params: false,
  init: false
})

const shareCondition = (key, label) => {
  visibleCtl.share = true
  data.batch_id = key
  data.name = label
  data.shareTitle = 'Share [ ' + key + '-' + label + ' ]'
  $axios({
    method: 'post',
    url: '/search/query_all_users'
  }).then((body) => {
    data.allUsers = body
  }).catch((error) => {
    console.log(error)
  })
}

const shareConditionAction = () => {
  if (data.sharedUsers.length === 0) {
    $message.error('Please select at least one user to share')
    return
  }
  loadingCtl.share = true
  $axios({
    method: 'post',
    url: '/master/critical_bom_calculator/share_condition',
    data: {
      batch_id: data.batch_id,
      name: data.name,
      withDate: data.withDate,
      users: data.sharedUsers,
      remarks: data.remarks
    }
  }).then((body) => {
    if (body) {
      $message.error(body + '')
    } else {
      $message.success(data.name + ' shared!')
      visibleCtl.share = false
      data.remarks = ''
      data.sharedUsers = []
      data.cid = ''
      data.name = ''
      data.withDate = false
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    loadingCtl.share = false
  })
}

watch(() => pageCtl.currentTab, () => {
  if (pageCtl.currentTab === 'second') {
    scrollBatchLogs()
  }
})

const _selectedBatchExecting = computed(() => {
  return pageCtl.selectedBatchInfo.START_TIME && !pageCtl.selectedBatchInfo.END_TIME
})

const _batchLogsHeight = computed(() => {
  return Math.max(document.body.clientHeight - 200, 400)
})

// 禁用当前月份之前的月份
const disabledDate = (time) => {
  const currentDate = new Date()
  const currentYear = currentDate.getFullYear()
  const currentMonth = currentDate.getMonth() // 0-based month

  const date = new Date(time)
  const year = date.getFullYear()
  const month = date.getMonth()

  // 禁用当前月份之前的月份
  return year < currentYear || (year === currentYear && month < currentMonth)
}
</script>

<style lang="scss">
#best_can_do {
  .el-form-item__content {
    display: block;
  }

  height: calc(100% - 25px);

  .maintain-by {
    font-size: 10px;
    font-weight: normal;
    color: var(--scp-text-color-secondary);
    font-style: italic;
  }

  .bcd-widget {
    .el-card {
      background-color: transparent !important;
      border: 1px solid var(--scp-border-color) !important;
      margin: 0 10px 0 0 !important;

      .el-card__body {
        padding: 0.85rem !important;
        text-align: center !important;

        h4 {
          color: var(--scp-text-color-primary) !important;
          font-size: 0.833rem !important;
          margin-top: 5px !important;
          margin-bottom: 5px !important;
        }

        h6 {
          font-weight: 500 !important;
          font-size: 0.417rem !important;
          text-transform: uppercase !important;
          color: var(--scp-text-color-secondary);
          margin: 0 !important;
        }
      }
    }
  }
}

/* 信息提示框样式 – 淡绿书本版 */
.info-banner {
  background: linear-gradient(135deg, #e8f5e9 0%, #f1f8e9 100%);
  border: 1px solid #66bb6a;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(102, 187, 106, 0.12);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(102, 187, 106, 0.18);
    transform: translateY(-1px);
  }
}

.info-banner-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

/* 书本图标 */
.info-banner-icon {
  color: #43a047;
  font-size: 20px;
  margin-top: 2px;
  flex-shrink: 0;
  /* 如需更精细的图标，可把 content 换成自定义字体或 SVG */
  &::before {
    content: "\01F4D6"; /* 📖 */
  }
}

.info-banner-text {
  flex: 1;
}

.info-banner-title {
  font-size: 16px;
  font-weight: 600;
  color: #2e7d32;
  margin-bottom: 4px;
  line-height: 1.4;
}

.info-banner-description {
  font-size: 14px;
  color: #424242;
  line-height: 1.5;
  opacity: 0.9;
}
</style>
