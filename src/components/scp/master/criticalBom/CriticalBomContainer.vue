<template>
  <div id="e2e">
    <el-container>
      <el-aside>
        <el-menu
            :default-active="pageCtl.activeMenu"
            collapse
            popper-effect="light"
            @select="handleMenuClick"
        >
          <el-menu-item index="BOM Structure">
            <el-icon>
              <icon-menu/>
            </el-icon>
            <template #title>BOM Structure</template>
          </el-menu-item>
          <el-menu-item index="Material Risk"  v-show="pageCtl.isAdmin">
            <el-icon>
              <tools/>
            </el-icon>
            <template #title>Material Risk</template>
          </el-menu-item>
          <el-menu-item index="Critical BOM Simulation"  v-show="pageCtl.isAdmin">
            <el-icon>
              <cpu/>
            </el-icon>
            <template #title>Critical BOM Simulation</template>
          </el-menu-item>
        </el-menu>
      </el-aside>
      <el-main class="right-main">
        <keep-alive>
          <component :is="pageCtl.currentComponent"></component>
        </keep-alive>
      </el-main>
    </el-container>
  </div>
</template>

<script lang="ts" setup>

import { inject, markRaw, onMounted, reactive } from 'vue'
import {
  Document,
  Menu as IconMenu, Calendar, Tools, Cpu
} from '@element-plus/icons-vue'

import BOMStructure from '@/components/scp/master/criticalBom/criticalBomComponents/CriticalBomStructure.vue'
import MaterialRisk from '@/components/starter/private/MaterialRisk.vue'
import CriticalBomSimulation from '@/components/scp/master/criticalBom/criticalBomComponents/CriticalBomSimulation.vue'

const $axios: any = inject('$axios')
const pageCtl = reactive({
  activeMenu: 'BOM Structure' as string,
  currentComponent: markRaw(BOMStructure) as any,
  isAdmin: false
})

const handleMenuClick = (index: string) => {
  pageCtl.activeMenu = index
  switch (index) {
    case 'BOM Structure':
      pageCtl.currentComponent = markRaw(BOMStructure)
      break
    case 'Material Risk':
      pageCtl.currentComponent = markRaw(MaterialRisk)
      break
    case 'Critical BOM Simulation':
      pageCtl.currentComponent = markRaw(CriticalBomSimulation)
      break
  }
}

const adminCheck = () => {
  $axios({
    method: 'post',
    url: '/master/bom/admin_check'
  }).then((body) => {
    pageCtl.isAdmin = body.isAdmin
  }).catch((error) => {
    console.log(error)
  })
}

onMounted(() => {
  adminCheck()
})
</script>

<style lang="scss" scoped>
// 优化后的CSS，移除不必要的样式，修复布局问题
#e2e {
  .widget-body {
    padding-left: 0;
  }

  .el-menu {
    border: 0;
  }

  // 左侧菜单样式
  .el-aside {
    width: 50px;
    overflow: hidden;
    display: flex;
    position: fixed;
    top: 45px;
  }

  .el-menu--collapse {
    width: 50px !important; // 2. 菜单本身也要压扁
  }

  .el-menu-item,
  .el-menu-item .el-menu-tooltip__trigger {
    width: 50px;
    padding: 0;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .el-menu-item .el-icon {
    margin: 0 !important;
  }

  .el-menu--collapse {
    width: 50px;
  }

  // 右侧主内容区域
  .el-main {
    --el-main-padding: 0px;
    padding-left: 50px;
  }
}

</style>
