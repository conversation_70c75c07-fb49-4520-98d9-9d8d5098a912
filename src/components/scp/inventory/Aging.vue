<template>
    <div class="left-sidebar" id="aging">
        <div class="widget">
            <div class="widget-body">
                <el-row class="search-box">
                    <el-col :span="5">
                        <scp-filter v-model="pageCtl.conditions.$scpFilter" cascader-base="STOCK_AGING_FILTER_V" :filter-base="['STOCK_AGING_V']"
                                    :after-apply="search"/>
                    </el-col>
                    <!--type-->
                    <el-col :span="4">
                        <el-select v-model="pageCtl.conditions.type" size="small" placeholder="Type">
                            <el-option
                                    v-for="item in ['Quantity', 'Moving Average Price']"
                                    :key="item"
                                    :label="item"
                                    :value="item">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <el-select v-model="pageCtl.conditions.stockType" size="small" placeholder="Stock Type">
                            <el-option
                                    v-for="item in ['Normal', 'Consignment']"
                                    :key="item"
                                    :label="item"
                                    :value="item">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="1">
                        <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                                    :data-exclude="['dateRange','dateRange3']"/>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="10">
                        <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report1">
                            <scp-subscript id="INAP"/>
                            <chart ref="report1ChartRef" :height="500" :option="_report1Opt"/>
                        </div>
                    </el-col>
                    <el-col :span="14">
                        <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report2">
                            <scp-subscript id="INAB"/>
                            <el-row style="margin-bottom: var(--scp-widget-margin)">
                                <el-col :span="6">
                                    <el-date-picker
                                            size="small"
                                            v-model="pageCtl.conditions.dateRange"
                                            type="daterange"
                                            unlink-panels
                                            format="YYYY/MM/DD"
                                            value-format="YYYY/MM/DD"
                                            range-separator="to"
                                            :clearable="false"
                                            start-placeholder="Start date"
                                            end-placeholder="End date"
                                            :picker-options="pageCtl.pickerOptions"
                                            style="width: calc(100% - 20px)">
                                    </el-date-picker>
                                </el-col>
                                <el-col :span="12" style="padding:5px 0 0 20px">
                                    <el-radio-group v-model="pageCtl.conditions.report2Type">
                                        <el-radio value="by_day">By Day</el-radio>
                                        <el-radio value="by_week">By Week</el-radio>
                                        <el-radio value="by_month">By Month</el-radio>
                                    </el-radio-group>
                                </el-col>
                            </el-row>
                            <chart ref="report2ChartRef" :height="463" :option="_report2Opt"/>
                        </div>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="24">
                        <div class="subscript-container" v-loading="pageCtl.loading.report3">
                            <scp-subscript id="INAT"/>
                            <el-row class="search-box">
                                <el-col :span="5">
                                    <el-select v-model="pageCtl.conditions.report3SelectedColumn" size="small" placeholder="Columns" multiple collapse-tags
                                               filterable clearable>
                                        <el-option
                                                v-for="item in _pivotColumns"
                                                :key="item"
                                                :label="item"
                                                :value="item">
                                        </el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="4">
                                    <el-date-picker
                                            size="small"
                                            v-model="pageCtl.conditions.dateRange3"
                                            type="monthrange"
                                            unlink-panels
                                            format="YYYYMM"
                                            value-format="YYYYMM"
                                            range-separator="to"
                                            :clearable="false"
                                            start-placeholder="Start date"
                                            end-placeholder="End date"
                                            :picker-options="pageCtl.pickerOptions"
                                            style="width: calc(100% - 25px)">
                                    </el-date-picker>
                                </el-col>
                                <el-col :span="1">
                                    <el-button size="small" @click="searchReport3">
                                        <font-awesome-icon icon="search"/>
                                    </el-button>
                                </el-col>
                            </el-row>
                            <scp-table
                                    url="/inventory/aging/query_report3"
                                    :params="pageCtl.conditions"
                                    :lazy="true"
                                    :pagging="false"
                                    :fixed-columns-left="_report3SelectedColumn.length"
                                    :max-height="550"
                                    :context-menu-items="pageCtl.report3ContextItems"
                                    :after-select="afterReport3Select"
                                    :last-row-bold="true"
                                    ref="report3TableRef"
                                    :columns="pageCtl.report3Columns"/>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $axios: any = inject('$axios')
const $shortenNumber: any = inject('$shortenNumber')
const $dateFormatter: any = inject('$dateFormatter')
const $px2Rem: any = inject('$px2Rem')
const $join: any = inject('$join')
const $deepClone: any = inject('$deepClone')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')

const searchRef = ref()
const report1ChartRef = ref()
const report2ChartRef = ref()
const report3TableRef = ref()

const viewReport3Details = () => {
  $viewDetails({
    url: '/inventory/aging/query_report3_details',
    durl: '/inventory/aging/download_report3_details',
    params: pageCtl.conditions,
    title: pageCtl.report3DetailsTitle
  })
}

const pageCtl = reactive({
  agingFilterOpts: [],
  loading: {
    filter: false,
    report1: false,
    report2: false,
    report3: false
  },
  conditions: {
    $scpFilter: {
      cascader: [['MATERIAL_TYPE', 'DIEN-SERVICE'], ['MATERIAL_TYPE', 'ERSA-SPARE'], ['MATERIAL_TYPE', 'HALB-SEMI_FG'], ['MATERIAL_TYPE', 'HAWA-FG'], ['MATERIAL_TYPE', 'ROH-RM'], ['MATERIAL_TYPE', 'VERP-PACKAGING']],
      filter: []
    },
    stockType: 'Normal',
    dateRange: ['2019/12/31', $dateFormatter(new Date(), 'yyyy/MM/dd')],
    dateRange3: ['201912', $dateFormatter(new Date(), 'yyyyMM')],
    selectedCategory: '',
    type: 'Moving Average Price',
    report2Type: 'by_month',
    report2ViewDetailsType: 'all',
    specialContent: '',
    specialType: 'MATERIAL',
    report3SelectedColumn: ['CLUSTER_NAME', 'ENTITY'],
    report3ColumnNames: [],
    report3SelectedValue: [],
    report3SelectedDate: [] as any
  },
  report1Data: [],
  report2Data: { xAxis: [], yAxis: [] },
  pickerOptions: {
    shortcuts: [{
      text: 'Next 6 months',
      onClick (picker) {
        const start = new Date()
        const end = new Date()
        end.setMonth(end.getMonth() + 6)
        picker.$emit('pick', [start, end])
      }
    }, {
      text: 'Next 1 year',
      onClick (picker) {
        const start = new Date()
        const end = new Date()
        end.setMonth(end.getMonth() + 12)
        picker.$emit('pick', [start, end])
      }
    }]
  },
  report3Columns: [],
  report3DetailsTitle: '',
  report3ContextItems: {
    view_details: {
      name: 'View details',
      callback: viewReport3Details
    },
    view_split0: { name: '---------' }
  }
})

onMounted(() => {
  report1ChartRef.value.chart().on('dblclick', (params) => {
    pageCtl.conditions.selectedCategory = params.name
    $viewDetails({
      url: '/inventory/aging/query_report1_details',
      durl: '/inventory/aging/download_report1_details',
      params: pageCtl.conditions,
      title: 'View Details ' + params.name
    })
  })
  report2ChartRef.value.chart().on('dblclick', (params) => {
    pageCtl.conditions.selectedCategory = params.name
    pageCtl.conditions.report2ViewDetailsType = 'part'
    $viewDetails({
      url: '/inventory/aging/query_report2_details',
      durl: '/inventory/aging/download_report2_details',
      params: pageCtl.conditions,
      title: 'View Details [' + params.name + ']'
    })
  })
  initPage()
})

watch(() => pageCtl.conditions.dateRange, () => {
  searchReport2()
})

watch(() => pageCtl.conditions.report2Type, () => {
  searchReport2()
})

const initPage = () => {
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/inventory/aging/init_page'
  }).then((body) => {
    pageCtl.agingFilterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const search = () => {
  searchReport1()
  searchReport2()
  searchReport3()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/inventory/aging/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/inventory/aging/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  report3TableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/inventory/aging/query_report3_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report3ColumnNames = body
    pageCtl.report3Columns = parseReport3Columns()
    report3TableRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

const parseReport3Columns = () => {
  const result = [] as any
  const selectColumns = _report3SelectedColumn.value
  for (let i = 0; i < selectColumns.length; i++) {
    if (i === 0) {
      result.push({
        data: selectColumns[i],
        render: (hotInstance, td, row, column, prop, value) => {
          if (value === 'Total') {
            td.style.fontWeight = 'bold'
          }
          td.innerHTML = value
        }
      })
    } else {
      result.push({
        data: selectColumns[i]
      })
    }
  }

  for (let i = 0; i < pageCtl.conditions.report3ColumnNames.length; i++) {
    result.push({
      title: pageCtl.conditions.report3ColumnNames[i],
      data: '\'' + pageCtl.conditions.report3ColumnNames[i] + '\'_TOTAL',
      type: 'numeric',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        if (value) {
          td.innerHTML = $thousandBitSeparator(value, 0)
        } else {
          td.innerHTML = value
        }
      }
    })
  }
  return result
}

const afterReport3Select = (r, rc, c) => {
  if (c.indexOf('\'') !== -1) {
    pageCtl.conditions.report3SelectedDate = c.split('\'')[1]
  } else {
    pageCtl.conditions.report3SelectedDate = ''
  }

  const selectColumns = _report3SelectedColumn.value

  const selectedValue = [] as any
  for (let i = 0; i < selectColumns.length; i++) {
    selectedValue.push(r[selectColumns[i]])
  }
  pageCtl.conditions.report3SelectedValue = selectedValue

  let title = $join(pageCtl.conditions.report3SelectedDate, ...selectedValue)
  if (title) {
    title = ' [' + title + ']'
  }

  pageCtl.report3DetailsTitle = 'View Details' + title
}

const _report3SelectedColumn = computed(() => {
  if (pageCtl.conditions.report3SelectedColumn.length === 0) {
    return ['CLUSTER_NAME', 'ENTITY']
  } else {
    return pageCtl.conditions.report3SelectedColumn
  }
})

const _report1Opt = computed(() => {
  const colorSettings = {
    '<1W': '#2c821d',
    '1W-2W': '#398719',
    '2W-3W': '#4f8f15',
    '3W-4W': '#6a9a12',
    '4W-2M': '#85a30d',
    '2M-3M': '#ccb601',
    '3M-6M': '#e2a705',
    '6M-1Y': '#dc7318',
    '1Y-2Y': '#d45e21',
    '>2Y': '#c12e34'
  }
  const colors = [] as any
  const data = $deepClone(pageCtl.report1Data)
  const legendOrg = ['<1W', '1W-2W', '2W-3W', '3W-4W', '4W-2M', '2M-3M', '3M-6M', '6M-1Y', '1Y-2Y', '>2Y']
  const legend = [] as any

  for (let i = 0; i < data.length; i++) {
    const name = data[i].name
    colors.push(colorSettings[name])
    data[i].label = { color: colorSettings[name] }
    if (legendOrg.indexOf(name) !== -1) {
      legend.push(name)
    }
  }
  return {
    title: {
      text: 'Stock Aging Chart'
    },
    toolbox: $toolbox({
      opts: [],
      view: {
        url: '/inventory/aging/query_report1_details',
        durl: '/inventory/aging/download_report1_details',
        params: () => {
          pageCtl.conditions.selectedCategory = ''
          return pageCtl.conditions
        },
        title: 'View Details [Total]'
      }
    }),
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div style="width:7.5rem;">')
        tip.push(params.marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value, 1))
        tip.push('(')
        tip.push(params.percent)
        tip.push('%)')
        tip.push('</span></div>')
        return tip.join('')
      }
    },
    legend: $legend({ type: 'pie', data: legend }),
    color: colors,
    series: [
      {
        type: 'pie',
        radius: '60%',
        center: ['30%', '55%'],
        data,
        label: {
          formatter: (params) => {
            const displayPercent = params.percent.toFixed(1) + '%'
            const value = $shortenNumber(params.value, 1)
            return `${value}\n(${displayPercent})`
          },
          fontSize: 12,
          lineHeight: 14
        },
        labelLine: {
          show: true, // 是否显示引导线
          length: 10,
          length2: 15
        }
      }
    ]
  }
})

const _report2Opt = computed(() => {
  const xAxis = pageCtl.report2Data.xAxis
  const yAxis = pageCtl.report2Data.yAxis
  const yAxis2 = [0] // 辅助计算变量
  for (let i = 0; i < yAxis.length - 1; i++) {
    yAxis2.push(yAxis2[i] + yAxis[i])
  }
  return {
    title: {
      text: 'Stock Aging Diagram'
    },
    toolbox: $toolbox({
      opts: [],
      view: {
        url: '/inventory/aging/query_report2_details',
        durl: '/inventory/aging/download_report2_details',
        params: () => {
          pageCtl.conditions.report2ViewDetailsType = 'all'
          return pageCtl.conditions
        },
        title: 'View Details [Total]'
      }
    }),
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tar = params[1]
        const marker = params[1].marker

        const tip = [] as any
        tip.push(tar.name)
        tip.push('<div style="width:9.5rem;">')
        tip.push(marker)
        tip.push(pageCtl.conditions.type)
        tip.push(' : <span style="float: right">')
        tip.push($shortenNumber(tar.value))
        tip.push('</span><div>')
        tip.push('<div style="width:9.5rem;">')
        tip.push(marker)
        tip.push('Accumulated : <span style="float: right">')
        tip.push($shortenNumber(parseInt(params[0].value) + parseInt(tar.value)))
        tip.push('</span><div>')
        return tip.join('')
      }
    },
    grid: $grid({ bottom: 10 }),
    xAxis: {
      type: 'category',
      splitLine: { show: false },
      data: xAxis
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    series: [
      {
        name: '辅助',
        type: 'bar',
        stack: 'Total',
        itemStyle: {
          borderColor: 'rgba(0,0,0,0)',
          color: 'rgba(0,0,0,0)'
        },
        emphasis: {
          itemStyle: {
            borderColor: 'rgba(0,0,0,0)',
            color: 'rgba(0,0,0,0)'
          }
        },
        data: yAxis2
      },
      {
        name: 'Stock',
        type: 'bar',
        stack: 'Total',
        label: {
          show: yAxis.length < 24,
          position: 'top',
          formatter: (value) => {
            return $shortenNumber(value.data, 1)
          }
        },
        data: yAxis
      }
    ]
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.agingFilterOpts, 'MATERIAL', 'VENDOR_CODE', 'MRP_CONTROLLER_DESCRIPTION')
})
</script>
