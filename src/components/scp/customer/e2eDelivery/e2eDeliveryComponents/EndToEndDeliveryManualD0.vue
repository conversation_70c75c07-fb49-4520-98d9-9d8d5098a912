<template>
  <div class="left-sidebar" id="manualD0">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['E2E_D0_MANUAL_DN_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="5">
            <el-date-picker
                style="width: calc(100% - 35px)"
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
            ></el-date-picker>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="[]"/>
          </el-col>
        </el-row>
        <div class="subscript-container">
          <el-row type="flex" justify="space-between" align="middle" style="padding-bottom: 10px">
            <p style="font-size: 14px; font-weight: 600; margin: 0;">End to End D+0 Manual DN Maintenance</p>
            <el-button style="width: 10%" plain @click="openGuidePage">
              <el-icon><guide /></el-icon>
              &nbsp;上传指南
            </el-button>
          </el-row>
          <scp-table
              url="/customer/end_to_end_delivery_tracking/query_manual_d0"
              download-url="/customer/end_to_end_delivery_tracking/download_manual_d0"
              save-url="/customer/end_to_end_delivery_tracking/save_manual_d0"
              ref="report1TableRef"
              :params="pageCtl.conditions"
              :contextMenuItemsReverse="true"
              :lazy="true"
              :columns="pageCtl.report1Columns"
              :primary-key-id="['PRIMARY_KEY']"/>
        </div>
        <div class="subscript-container">
          <el-row type="flex" justify="space-between" align="middle" style="padding-bottom: 10px">
            <p style="font-size: 14px; padding: 10px 10px 10px 0; font-weight: 600">End to End D+0 Manual DN
              Details</p>
            <el-button style="width: 10%" plain @click="openFeedbackGuidePage">
              <el-icon><guide /></el-icon>
              &nbsp;DC Feedback 维护指南
            </el-button>
          </el-row>
          <scp-table
              url="/customer/end_to_end_delivery_tracking/query_manual_d0_details"
              download-url="/customer/end_to_end_delivery_tracking/download_manual_d0_details"
              save-url="/customer/end_to_end_delivery_tracking/save_feedback"
              ref="report2TableRef"
              :primary-key-id="['DELIVERY_NUMBER']"
              :page-sizes="[50, 100, 200, 500]"
              :params="pageCtl.conditions"
              :contextMenuItemsReverse="true"
              :columns="_report2Column"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import { computed, inject, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
import { ElMessageBox } from 'element-plus'
import {
  Guide
} from '@element-plus/icons-vue'

const $dateFormatter: any = inject('$dateFormatter')
const $axios: any = inject('$axios')
const report1TableRef = ref()
const report2TableRef = ref()

const camelCaseStartPlaceholder = (word: string) => {
  const words = word.split('_')
  const camelCaseWords = words.map((word, index) => {
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  })
  return camelCaseWords.join(' ')
}

const pageCtl = reactive({
  filterOpts: [],
  report2Columns: [],
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    dateRange: [] as any
  },
  loading: {
    report1: false
  },
  report1Columns: [
    { data: 'DELIVERY_NUMBER', title: camelCaseStartPlaceholder('DELIVERY_NUMBER') },
    { data: 'UPLOAD_OWNER', title: camelCaseStartPlaceholder('UPLOAD_OWNER') },
    { data: 'UPLOAD_OWNER_NAME', title: camelCaseStartPlaceholder('UPLOAD_OWNER_NAME') },
    { data: 'CREATE_DATE', title: camelCaseStartPlaceholder('CREATE_DATE') }
  ] as Array<any>
})

const search = () => {
  searchReport1()
}

const searchReport1 = () => {
  report1TableRef.value.search()
  report2TableRef.value.search()
}

const initPage = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/customer/end_to_end_delivery_tracking/manual_d0_init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.report2Columns = body.report2Columns
  }).catch((error) => {
    console.log(error)
  }).finally(
    pageCtl.loading.report1 = false
  )
}

const openGuidePage = () => {
  ElMessageBox.alert(
      `
    <div style="font-size: 14px; color: red; line-height: 1.5;">
      请参考下方图片操作步骤完成上传操作。
    </div>
    <div style="text-align: center; margin: 10px 0;">
      <img
        src="http://scp-dss.cn.schneider-electric.com/chevereto/images/2025/09/11/End-to-End-Manual-D0-Guide-Book.png"
        alt="操作指南"
        style="max-width: 100%; height: auto; display: block; margin: 0 auto;"
      />
    </div>
    `,
      'Manual D0 上传指南',
      {
        dangerouslyUseHTMLString: true,
        customClass: 'end-to-end-manual-d0-message-box'
      }
  )
}

const openFeedbackGuidePage = () => {
  ElMessageBox.alert(
      `
    <div style="font-size: 14px; color: red; line-height: 1.5;">
      请参考下方图片操作步骤完成上传操作。
    </div>
    <div style="text-align: center; margin: 10px 0;">
      <img
        src="http://scp-dss.cn.schneider-electric.com/chevereto/images/2025/09/10/D0-Feedback-Guide-Book.png"
        alt="DC Feedback 维护指南"
        style="max-width: 100%; height: auto; display: block; margin: 0 auto;"
      />
    </div>
    `,
      'DC Feedback 维护指南',
      {
        dangerouslyUseHTMLString: true,
        customClass: 'end-to-end-manual-d0-message-box'
      }
  )
}

onMounted(() => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  pageCtl.conditions.dateRange = [
    $dateFormatter(end, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  initPage()
  search()
})

const _report2Column = computed(() => {
  const columns = [] as any
  for (const item of pageCtl.report2Columns) {
    switch (item) {
      case 'ONTIME_STATUS':
        columns.push(
          { data: item, title: camelCaseStartPlaceholder(item), render: renderRatio }
        )
        break
      case 'DC_FEEDBACK':
        columns.push(
          {
            title: 'DC Feedback',
            data: item,
            type: 'autocomplete',
            source: (query, process) => {
              process(['Fulfill', 'Can not Fulfill'])
            }
          }
        )
        break
      case 'DC_COMMENTS':
        columns.push(
          { data: item, title: 'DC Comments' }
        )
        break
      case 'COT':
        columns.push(
          { data: item, title: 'COT' }
        )
        break
      default:
        columns.push(
          { data: item, title: camelCaseStartPlaceholder(item) }
        )
    }
  }
  return columns
})

const renderRatio = (hotInstance, td, row, column, prop, value) => {
  td.innerHTML = value
  if (value === 'Fail') {
    td.style = 'color: var(--scp-text-color-error) !important; font-weight: bold !important'
  } else if (value === 'Others') {
    td.style = 'color: var(--scp-text-color-warning) !important; font-weight: bold !important'
  } else if (value === 'OnTime') {
    td.style = 'color: var(--scp-text-color-success) !important; font-weight: bold !important'
  } else if (value === 'Ongoing') {
    td.style = 'color: var(--scp-text-color-highlight) !important; font-weight: bold !important'
  }
}
</script>

<style lang="scss">
.end-to-end-manual-d0-message-box {
  --el-messagebox-width: 60vw !important;
  height: 80vh !important;
  margin: auto !important;
  position: absolute !important;
  top: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;

  .el-message-box__btns {
    padding-top: 0;
  }
  .el-message-box__title {
    font-weight: bold;
  }
}
</style>
