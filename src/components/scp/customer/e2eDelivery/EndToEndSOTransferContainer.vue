<template>
  <div id="e2e">
    <el-container>
      <el-aside>
        <el-menu
            :default-active="pageCtl.activeMenu"
            collapse
            popper-effect="light"
            @select="handleMenuClick"
        >
          <el-menu-item index="E2E Allowlist">
            <el-icon>
              <money/>
            </el-icon>
            <template #title>E2E Allowlist</template>
          </el-menu-item>
          <el-menu-item index="E2E Allowlist">
            <el-icon>
              <money/>
            </el-icon>
            <template #title>E2E Allowlist</template>
          </el-menu-item>
        </el-menu>
      </el-aside>
      <el-main class="right-main">
        <keep-alive>
          <component :is="pageCtl.currentComponent"></component>
        </keep-alive>
      </el-main>
    </el-container>
  </div>
</template>

<script lang="ts" setup>

import { markRaw, reactive } from 'vue'
import {
  Document,
  Menu as IconMenu, Calendar, Tools, Money
} from '@element-plus/icons-vue'

import Allowlist from '@/components/scp/customer/e2eDelivery/e2eSOTransferComponents/EndToEndSOTransferAllowlist.vue'

const pageCtl = reactive({
  activeMenu: 'E2E Allowlist' as string,
  currentComponent: markRaw(Allowlist) as any
})

const handleMenuClick = (index: string) => {
  pageCtl.activeMenu = index
  switch (index) {
    case 'E2E Allowlist':
      pageCtl.currentComponent = markRaw(Allowlist)
      break
  }
}
</script>

<style lang="scss">
#e2e {
  .widget-body {
    padding-left: 0;
  }

  .el-menu {
    border: 0;
  }

  .el-aside {
    width: 35px;
    overflow: hidden;

    display: flex;
    position: fixed;
    top: 45px;
  }

  .el-menu-item .el-menu-tooltip__trigger {
    width: 35px;
    padding: 0;
    display: flow;
    text-align: center;
  }

  .el-menu--collapse {
    width: 35px;
  }

  .el-main {
    --el-main-padding: 0px;
    padding-left: 35px;
  }

}

</style>
