<template>
  <div class="left-sidebar" id="manualD0">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['MR3_E2E_SO_TRANSFER_ALLOWLIST']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="5">
            <el-select
                v-model="pageCtl.conditions.ruleType"
                size="small"
                filterable>
              <el-option v-for="item in pageCtl.ruleTypeOpts"
                         :key="item"
                         :label="item"
                         :value="item"/>
            </el-select>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="[]"/>
          </el-col>
        </el-row>
        <div class="subscript-container">
          <p style="font-size: 14px; padding: 10px 10px 10px 0; font-weight: 600">End to End SO Transfer Allowlist
            Maintenance</p>
          <scp-table
              url="/customer/end_to_end_so_transfer/allowlist_query_report1"
              download-url="/customer/end_to_end_so_transfer/allowlist_download_report1"
              save-url="/customer/end_to_end_so_transfer/allowlist_save_report1"
              ref="report1TableRef"
              :params="pageCtl.conditions"
              :lazy="true"
              :columns="pageCtl.report1Columns"
              :contextMenuItems="pageCtl.report1MenuItems"
              :after-save="search"
              :primary-key-id="['ROW_ID']"
              :editable="true"/>
        </div>
        <div class="subscript-container">
          <p style="font-size: 14px; padding: 10px 10px 10px 0; font-weight: 600">End to End SO Transfer Allowlist</p>
          <scp-table
              url="/customer/end_to_end_so_transfer/allowlist_query_report2"
              download-url="/customer/end_to_end_so_transfer/allowlist_download_report2"
              save-url="/customer/end_to_end_so_transfer/allowlist_save_report2"
              ref="report2TableRef"
              :params="pageCtl.conditions"
              :editable="false"
              :lazy="true"/>
        </div>
      </div>
    </div>

    <scp-upload
        ref="upload1Ref"
        title="Upload SO Transfer Allowlist"
        :show-btn="false"
        w="600px"
        h="250px"
        :upload-params="{ conditions: JSON.stringify(pageCtl.conditions) }"
        :download-template-params="pageCtl.conditions"
        upload-url='/customer/end_to_end_so_transfer/upload_report1'
        download-template-url='/customer/end_to_end_so_transfer/download_report1_template'
        :on-upload-end="() => {
          report1TableRef.search()
          report2TableRef.search()
        }">
      <template #tip>
        <p>This file will be <b>merged</b> with the data you uploaded before</p>
      </template>
    </scp-upload>
  </div>
</template>

<script setup lang="ts">

import { inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
import ScpUpload from '@/components/starter/components/Upload.vue'

const $axios: any = inject('$axios')
const report1TableRef = ref()
const report2TableRef = ref()

const upload1Ref = ref()

onMounted(() => {
  initPage()
  search()
})

const camelCaseStartPlaceholder = (word: string) => {
  const words = word.split('_')
  const camelCaseWords = words.map((word, index) => {
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  })
  return camelCaseWords.join(' ')
}

const pageCtl = reactive({
  filterOpts: [],
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    ruleType: 'Factory by Material',
    report1ColumnsList: []
  },
  loading: {
    report1: false
  },
  report1Columns: [
    { data: 'MATERIAL', title: camelCaseStartPlaceholder('MATERIAL') },
    { data: 'PLANT_CODE', title: camelCaseStartPlaceholder('PLANT_CODE') }
  ] as Array<any>,
  ruleTypeOpts: ['Factory by Material'] as Array<any>,
  ruleTypeColumnMap: {},
  report1MenuItems: {
    upload: {
      name: 'Upload',
      callback: () => {
        upload1Ref.value.showUploadWin()
      }
    },
    view_split: { name: '---------' }
  }
})

const generateReport1Columns = () => {
  const columnsForType = pageCtl.ruleTypeColumnMap?.[pageCtl.conditions.ruleType]
  let report1Columns = []
  const columnsList = []

  if (Array.isArray(columnsForType) && columnsForType.length > 0) {
    report1Columns = columnsForType.map(column => {
      const columnData = { data: column, title: camelCaseStartPlaceholder(column) }
      columnsList.push(column) // 添加到 dataList 中
      return columnData
    })
  } else {
    report1Columns = [
      { data: 'MATERIAL', title: camelCaseStartPlaceholder('MATERIAL') },
      { data: 'PLANT_CODE', title: camelCaseStartPlaceholder('PLANT_CODE') }
    ]
    columnsList.push('MATERIAL')
    columnsList.push('PLANT_CODE')
  }
  pageCtl.report1Columns = report1Columns
  pageCtl.conditions.report1ColumnsList = columnsList
}

watch(() => pageCtl.conditions.ruleType, () => {
  generateReport1Columns()
  report1TableRef.value.search()
})

const search = () => {
  generateReport1Columns()
  report1TableRef.value.search()
  report2TableRef.value.search()
}

const initPage = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/customer/end_to_end_so_transfer/allowlist_init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.ruleTypeColumnMap = body.ruleTypeColumnMap
    pageCtl.ruleTypeOpts = body.ruleTypeOpts
  }).catch((error) => {
    console.log(error)
  }).finally(pageCtl.loading.report1 = false
  )
}

</script>
