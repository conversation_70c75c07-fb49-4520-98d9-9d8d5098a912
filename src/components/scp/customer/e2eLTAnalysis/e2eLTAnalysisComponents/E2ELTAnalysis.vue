<template>
  <div class="left-sidebar" id="e2eLTAnalysis">
    <div class="widget">
      <el-row class="search-box">
        <el-col :span="5">
          <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                      :filter-base="['MD3_IDS_CLO_SO_DATA_AGG_V']" :after-apply="search"/>
        </el-col>
        <el-col :span="5">
          <el-date-picker
              size="small"
              v-model="pageCtl.conditions.dateRange"
              type="monthrange"
              unlink-panels
              range-separator="~"
              format="YYYYMM"
              value-format="YYYYMM"
              start-placeholder="Start date"
              end-placeholder="End date"
              :clearable="false"/>
        </el-col>
        <el-col :span="3">
          <el-select v-model="pageCtl.conditions.resultType" size="small">
            <el-option
                v-for="item in ['Number of Records', 'Quantity', 'Net Value', 'Lines']"
                :key="item"
                :label="item"
                :value="item">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="pageCtl.conditions.calendarType" size="small">
            <el-option
                v-for="item in ['Order Creation Date', 'Order Clean Date']"
                :key="item"
                :label="item"
                :value="item">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="pageCtl.conditions.calendarCalcType" size="small">
            <el-option
                v-for="item in ['Working Days', 'Calendar Days']"
                :key="item"
                :label="item"
                :value="item">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="pageCtl.conditions.reportViewType" size="small">
            <el-option
                v-for="item in ['CALENDAR_MONTH', 'CALENDAR_QUARTER', 'CALENDAR_YEAR']"
                :key="item"
                :label="camelCaseStartPlaceholder(item)"
                :value="item">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="1">
          <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                      :data-exclude="['dateRange']"/>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12" v-loading="pageCtl.loading.report1">
          <div class="subscript-container subscript-container-left" style="height: 350px">
            <scp-subscript id="LTAR1" ref="report1SubRef"/>
            <div class="front">
              <el-table
                  :data="pageCtl.report1Data"
                  style="width: 100%"
                  height="350"
                  row-key="id"
                  lazy
                  :load="loadReport1"
                  :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                  @sort-change="report1SortChange"
                  class="tree-table" @contextmenu.prevent="">
                <el-table-column prop="category" label="Category" sortable
                                 :sort-method="(a, b) => report1Sort(a, b, 'category')">
                  <template #header>
                    <el-select v-model="pageCtl.conditions.report1Categories" size="small"
                               style="border:0 !important;width:calc(100% - 30px)"
                               @click.stop="(e)=>e.preventDefault()"
                               placeholder="Pivot Columns"
                               collapse-tags collapse-tags-tooltip clearable multiple :multiple-limit="7" filterable
                               :loading="pageCtl.loading.filter">
                      <el-option
                          v-for="item in pageCtl.pivotOpts"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </template>
                  <template v-slot="scope">
                    <el-tooltip effect="light" :show-after="1000" :content="scope.row.category"
                                placement="bottom-end">
                          <span v-contextmenu:contextmenu1Ref :data-value="JSON.stringify(scope.row)"
                                @dblclick="report1Doubleclick"
                                @contextmenu.prevent="report1RightClick2">
                            {{ scope.row.category }}
                          </span>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="netValue" label="Net Value (€)" sortable
                                 :sort-method="(a, b) => report1Sort(a, b, 'netValue')" width="150"
                                 class-name="plus-header">
                  <template v-slot="scope">
                    <el-tooltip effect="light" :show-after="500" placement="top-end">
                      <template #content>
                        fail Ratio: {{ $shortenNumber(scope.row.netValue) }}
                      </template>
                      <span v-contextmenu:contextmenu1Ref :data-value="JSON.stringify(scope.row)"
                            @dblclick="report1Doubleclick"
                            @contextmenu.prevent="report1RightClick2">
                            {{ $shortenNumber(scope.row.netValue) }}
                          </span>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="numberOfRecords" label="Number of Records" sortable
                                 :sort-method="(a, b) => report1Sort(a, b, 'numberOfRecords')" width="150"
                                 class-name="plus-header">
                  <template v-slot="scope">
                    <el-tooltip effect="light" :show-after="500" placement="top-end">
                      <template #content>
                        Measure for Comparison: {{ $shortenNumber(scope.row.numberOfRecords, 1) }}
                      </template>
                      <span v-contextmenu:contextmenu1Ref :data-value="JSON.stringify(scope.row)"
                            @dblclick="report1Doubleclick"
                            @contextmenu.prevent="report1RightClick2">
                            {{ $shortenNumber(scope.row.numberOfRecords, 1) }}
                          </span>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="differenceInDaysMinus" label="Difference in days (-)" sortable
                                 :sort-method="(a, b) => report1Sort(a, b, 'differenceInDaysMinus')"
                                 width="200" class-name="fail-header">
                  <template v-slot="scope">
                    <el-tooltip effect="light" :show-after="1500" placement="top-end">
                      <template #content>
                        Measure for Comparison: {{ $thousandBitSeparator(scope.row.differenceInDaysMinus, 1) }}
                      </template>
                      <div v-contextmenu:contextmenu1Ref :data-value="JSON.stringify(scope.row)"
                           @dblclick="report1Doubleclick"
                           @contextmenu.prevent="report1RightClick2">
                        <div style="width: 200px;display: flex;justify-content: right;align-items:center;"
                             v-if="scope.row.category !== 'Total'">
                          <!-- differenceInDaysMinus显示文本 -->
                          <div style="font-size: 80%;cursor: pointer;" :style="{color: pageCtl.color.differenceInDaysMinus}"
                               @click="switchReport1DisplayMode">
                            {{ report1DislayValue(scope.row, 'differenceInDaysMinus') }}
                          </div>
                          <div style="height:19px;margin-left: 2px"
                               :style="{width: scope.row.differenceInDaysMinusWidth + '%', backgroundColor: pageCtl.color.differenceInDaysMinus}">
                            &nbsp;
                          </div>
                        </div>
                        <div v-else style="text-align: right;cursor: pointer" :style="{color: pageCtl.color.differenceInDaysMinus}"
                             @click="switchReport1DisplayMode">
                          {{ report1DislayValue(scope.row, 'differenceInDaysMinus') }}
                        </div>
                      </div>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="differenceInDaysPlus" label="Difference In Days (+)" sortable
                                 :sort-method="(a, b) => report1Sort(a, b, 'differenceInDaysPlus')"
                                 width="200" class-name="plus-header">
                  <template v-slot="scope">
                    <el-tooltip effect="light" :show-after="1500" placement="top-end">
                      <template #content>
                        Measure for Comparison: {{ $thousandBitSeparator(scope.row.differenceInDaysPlus, 1) }}
                      </template>
                      <div v-contextmenu:contextmenu1Ref :data-value="JSON.stringify(scope.row)"
                           @dblclick="report1Doubleclick"
                           @contextmenu.prevent="report1RightClick2">
                        <div style="width: 200px;display: flex;justify-content: left;align-items:center;"
                             v-if="scope.row.category !== 'Total'">
                          <div style="height:19px;;margin-right: 2px"
                               :style="{width: scope.row.differenceInDaysPlusWidth + '%', backgroundColor: pageCtl.color.differenceInDaysPlus}">
                            &nbsp;
                          </div>
                          <div style="font-size: 80%;cursor: pointer" :style="{color: pageCtl.color.differenceInDaysPlus}"
                               @click="switchReport1DisplayMode">
                            {{ report1DislayValue(scope.row, 'differenceInDaysPlus') }}
                          </div>
                        </div>
                        <div v-else style="text-align: left;cursor: pointer" :style="{color: pageCtl.color.differenceInDaysPlus}"
                             @click="switchReport1DisplayMode">
                          {{ report1DislayValue(scope.row, 'differenceInDaysPlus') }}
                        </div>
                      </div>
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="back">
              <div class="box">
                <div class="box-title">E2E LT Analysis Settings</div>
                <el-row>
                  <el-col :span="10">Compare Parameter 1</el-col>
                  <el-col :span="14">
                    <el-select
                        v-model="pageCtl.conditions.report1Parameter1" size="small" filterable>
                      <el-option
                          v-for="item in pageCtl.report1ParameterOptions"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="10">Compare Parameter 2</el-col>
                  <el-col :span="14">
                    <el-select
                        v-model="pageCtl.conditions.report1Parameter2" size="small" filterable>
                      <el-option
                          v-for="item in pageCtl.report1ParameterOptions"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <div class="box-footer">
                  <el-button
                      @click="report1SubRef.toggleView()">
                    Back
                  </el-button>
                  <el-button
                      type="primary"
                      @click="report1SubRef.toggleView();searchReport1()">Search
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="12" v-loading="pageCtl.loading.report2">
          <div class="subscript-container subscript-container-right" style="height: 350px">
            <scp-subscript id="LTAR2" ref="report2SubRef"/>
            <div class="front">
              <chart ref="report2ChartRef" :height="350" :option="_report2Opt"/>
            </div>
            <div class="back"><!-- 高度已调整 -->
              <div class="box">
                <div class="box-title">Filter Settings</div>
                <el-row>
                  <el-col :span="6">Stack By</el-col>
                  <el-col :span="18">
                    <el-select
                        v-model="pageCtl.conditions.report2ViewType" size="small" filterable>
                      <el-option
                          v-for="item in _pivotColumns"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <div class="box-footer">
                  <el-button
                      @click="report2SubRef.toggleView()">
                    Back
                  </el-button>
                  <el-button
                      type="primary"
                      @click="report2SubRef.toggleView();searchReport2()">Search
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" v-loading="pageCtl.loading.report3">
          <div class="subscript-container">
            <scp-subscript id="LTAR3" ref="report3SubRef"/>
            <el-row class="search-box">
              <el-col :span="3">
                <el-select v-model="pageCtl.conditions.report3SelectedColumns" size="small" placeholder="Columns"
                           multiple collapse-tags
                           clearable filterable>
                  <el-option v-for="item in _pivotColumns"
                             :label="item"
                             :key="item"
                             :value="item"/>
                </el-select>
              </el-col>
              <el-col :span="1">
                <el-button size="small" @click="searchReport3">
                  <font-awesome-icon icon="search"/>
                </el-button>
              </el-col>
            </el-row>
            <scp-table
                url="/customer/e2e_lt_analysis/analysis/query_report3"
                download-url="/customer/e2e_lt_analysis/analysis/download_report3"
                :params="pageCtl.conditions"
                :lazy="true"
                :show-total="true"
                :pagging="false"
                :fixed-columns-left="_report3SelectedColumns.length"
                :max-height="375"
                :context-menu-items="pageCtl.report3ContextItems"
                :after-select="afterReport3Select"
                ref="report3TableRef"
                :columns="pageCtl.report3Columns"
                :editable="false"/>
          </div>
        </el-col>
      </el-row>
    </div>

    <v-contextmenu ref="contextmenu1Ref">
      <v-contextmenu-item @click="report1ViewDetails">
        View Details
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onActivated, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $deepClone: any = inject('$deepClone')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')
const $toFixed: any = inject('$toFixed')
const $axios: any = inject('$axios')
const $join: any = inject('$join')
const $px2Rem: any = inject('$px2Rem')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')

const searchRef = ref()
const contextmenu1Ref = ref()
const report1SubRef = ref()
const report2SubRef = ref()
const report2ChartRef = ref()
const report3SubRef = ref()
const report3TableRef = ref()

const viewReport3Details = () => {
  let title = $join(
    pageCtl.conditions.report3SelectedDate,
    ...pageCtl.conditions.report3SelectedValues
  )
  if (title) {
    title = ' [' + title + ']'
  }

  $viewDetails({
    url: '/customer/e2e_lt_analysis/analysis/query_report3_details',
    durl: '/customer/e2e_lt_analysis/analysis/download_report3_details',
    params: pageCtl.conditions,
    title: 'View Details' + title
  })
}

const pageCtl = reactive({
  color: {
    differenceInDaysPlus: '#61a3f1',
    differenceInDaysMinus: '#fc6464'
  },
  pivotOpts: [],
  filterOpts: [],
  rcaDesc: {},
  loading: {
    filter: false,
    report1: false,
    report2: false,
    report3: false
  },
  filterDateColumns: [],
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: [{
        joiner: 'AND',
        fields: [
          'GSC_REGION'
        ],
        types: [
          'VARCHAR2'
        ],
        operator: 'NOT IN',
        value: [],
        text: 'GLOBAL ETO'
      }, {
        joiner: 'AND',
        fields: [
          'LONG_CRD_FLAG_CHAR'
        ],
        types: [
          'VARCHAR2'
        ],
        operator: 'NOT IN',
        value: [],
        text: '1'
      }, {
        joiner: 'AND',
        fields: [
          'OG_IG_FLAG'
        ],
        types: [
          'VARCHAR2'
        ],
        operator: 'IN',
        value: [],
        text: 'OG'
      }, {
        joiner: 'AND',
        fields: [
          'FIRST_DELAY_FAMILY'
        ],
        types: [
          'VARCHAR2'
        ],
        operator: 'NOT IN',
        value: [],
        text: 'Credit Control'
      }, {
        joiner: 'AND',
        fields: [
          'LAST_DELAY_FAMILY'
        ],
        types: [
          'VARCHAR2'
        ],
        operator: 'NOT IN',
        value: [],
        text: 'Credit Control'
      }
      ]
    },
    reportViewType: 'CALENDAR_MONTH',
    dateColumn: 'CALENDAR_WEEK',
    report1DisplayMode: 'value',
    report1Categories: ['GSC_SUB_REGION', 'COUNTRY_NAME'],
    report1SelectedValues: '',
    warningValue: {
      Fail: 95
    },
    fields: ['COUNTRY_NAME'],
    dateRange: [] as any,
    report2ShowType: 'VIEW_BY_VALUE',
    report2ViewType: 'GSC_REGION',
    report1Tooltips: [],
    report1Parameter1: 'ACTUAL_LT',
    report1Parameter2: 'CONTRACTUAL_LT',
    selectedTreePath: '',
    report2SeriesType: 'bar',
    report2SelectedValue: '',
    report3SelectedColumns: ['GSC_REGION', 'COUNTRY_NAME'],
    report3SelectedDate: '',
    report3SelectedValues: [],
    report3ValueType: 'Value',
    report3ColumnNames: [],
    resultType: 'Net Value',
    calendarType: 'Order Creation Date',
    calendarCalcType: 'Working Days'
  },
  report1ParameterOptions: [
    'CUSTOMER_REQUESTED_LT',
    'CUSTOMER_REQUESTED_LT_1',
    'LOGISTICS_OFFER_LT',
    'LOGISTICS_OFFER_LT_1',
    'FIRST_CONFIRMED_LT',
    'FIRST_CONFIRMED_LT_1',
    'ACTUAL_LT',
    'ACTUAL_LT_1',
    'CONTRACTUAL_LT',
    'CONTRACTUAL_LT_1'
  ],
  report1Data: [],
  report1RightClickData: '',
  report1ColumnStartName: '',
  report2Data: [] as any,
  report3ContextItems: {
    view_details: {
      name: 'View details',
      callback: viewReport3Details
    },
    view_split0: { name: '---------' }
  },
  report3Columns: []
})

onActivated(() => {
  if (report2ChartRef.value) {
    setTimeout(() => {
      report2ChartRef.value?.resize()
    }, 0)
  }
})

onMounted(() => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(now.getFullYear(), 0, 1)
  pageCtl.conditions.dateRange = [
    $dateFormatter(start, 'yyyyMM'),
    $dateFormatter(end, 'yyyyMM')
  ]
  initPage()
  searchRef.value.loadAndClick()

  report2ChartRef.value.chart().on('dblclick', (obj) => {
    pageCtl.conditions.report2SelectedValue = obj.name

    $viewDetails({
      url: '/customer/e2e_lt_analysis/analysis/query_report2_details',
      durl: '/customer/e2e_lt_analysis/analysis/download_report2_details',
      params: pageCtl.conditions,
      title: 'View Details [' + [obj.name].join(', ') + ']'
    })
  })
  report2ChartRef.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      pageCtl.conditions.report2SeriesType = obj.currentType
    }
  })
})

const initPage = () => {
  $axios({
    method: 'post',
    url: '/customer/e2e_lt_analysis/analysis/init_page',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.pivotOpts = body.pivotOpts
    pageCtl.rcaDesc = body.rcaDesc
    pageCtl.filterDateColumns = body.filterDateColumns
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
  })
}

const camelCaseStartPlaceholder = (word: string) => {
  const words = word.split('_')
  const camelCaseWords = words.map((word, index) => {
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  })
  return camelCaseWords.join(' ')
}

const search = () => {
  pageCtl.report1ColumnStartName = 'Response'
  pageCtl.conditions.report1SelectedValues = ''
  pageCtl.report1RightClickData = ''
  initPage()
  searchReport1()
  searchReport2()
  searchReport3()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/customer/e2e_lt_analysis/analysis/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const switchReport1DisplayMode = () => {
  if (pageCtl.conditions.report1DisplayMode === 'value') {
    pageCtl.conditions.report1DisplayMode = 'percent'
  } else if (pageCtl.conditions.report1DisplayMode === 'percent') {
    pageCtl.conditions.report1DisplayMode = 'value'
  }
}

const report1DislayValue = (row, type) => {
  if (pageCtl.conditions.report1DisplayMode === 'value') {
    return $shortenNumber(row[type])
  } else if (pageCtl.conditions.report1DisplayMode === 'percent') {
    const result = $toFixed(row[type] * 100 / (row.differenceInDaysMinus + row.differenceInDaysPlus), 1)
    if (result) {
      return result + '%'
    } else {
      return '-'
    }
  }
}

const loadReport1 = (
  row: any,
  treeNode: any,
  resolve: (date: any[]) => void
) => {
  $axios({
    method: 'post',
    url: '/customer/e2e_lt_analysis/analysis/query_report1_sub',
    data: {
      ...pageCtl.conditions,
      parent: row.parent || [],
      expandValue: row.category,
      parentMaxWidth: row.parentMaxWidth
    }
  }).then((body) => {
    resolve(body)
  }).catch((error) => {
    console.log(error)
  })
}

const report1SortCache = {}

const report1SortChange = (e) => {
  report1SortCache[e.prop] = e.order
}

const report1Sort = (a, b, category) => {
  const beforeOrder = report1SortCache[category]
  if (a.category === 'Total') {
    return beforeOrder === 'ascending' ? -1 : 1
  } else {
    return a[category] > b[category] ? 1 : (a[category] === b[category] ? 0 : -1)
  }
}

const report1RightClick2 = (e) => {
  let target = e.target
  let maxLoop = 5
  while (maxLoop-- > 0) {
    if (target && target.dataset.value) {
      break
    }
    target = target.parentElement
  }
  // 用户点击右键时, 不一定继续点击右键弹出菜单, 所以找个临时变量来存一下
  pageCtl.report1RightClickData = target.dataset.value
}

const report1Doubleclick = (e) => {
  report1RightClick2(e)
  report1ViewDetails()
}

const report1ViewDetails = () => {
  pageCtl.conditions.report1SelectedValues = pageCtl.report1RightClickData
  const title = JSON.parse(pageCtl.conditions.report1SelectedValues)
  $viewDetails({
    url: '/customer/e2e_lt_analysis/analysis/query_report1_details',
    durl: '/customer/e2e_lt_analysis/analysis/download_report1_details',
    params: pageCtl.conditions,
    title: 'View Details [' + [...title.parent, title.category].join(', ') + ']',
    editable: false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/customer/e2e_lt_analysis/analysis/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}
const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/customer/e2e_lt_analysis/analysis/query_report3_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report3ColumnNames = body
    pageCtl.report3Columns = parseReport3Columns()
    report3TableRef.value.clearAndSearch()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const afterReport3Select = (r, rc, c) => {
  if (c.indexOf('\'') !== -1) {
    pageCtl.conditions.report3SelectedDate = c.split('\'')[1]
  } else {
    pageCtl.conditions.report3SelectedDate = ''
  }
  const selected = [] as any
  for (let i = 0; i < _report3SelectedColumns.value.length; i++) {
    let v = r[_report3SelectedColumns.value[i]]
    if (v === 'Total') {
      v = ''
    }
    selected.push(v)
  }
  pageCtl.conditions.report3SelectedValues = selected
}
const parseReport3Columns = () => {
  const result = [] as any
  for (let i = 0; i < _report3SelectedColumns.value.length; i++) {
    result.push({
      data: _report3SelectedColumns.value[i],
      render: (hotInstance, td, row, column, prop, value) => {
        if (value === 'Total') {
          td.style.fontWeight = 'bold'
        }
        td.innerHTML = value
      }
    })
  }

  for (let i = 0; i < pageCtl.conditions.report3ColumnNames.length; i++) {
    result.push({
      title: pageCtl.conditions.report3ColumnNames[i],
      data: '\'' + pageCtl.conditions.report3ColumnNames[i] + '\'_TOTAL',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        if (pageCtl.conditions.report3ValueType === 'Value') {
          td.innerHTML = value ? $thousandBitSeparator(value, 0) : '0'
        } else {
          td.innerHTML = value ? (value * 100).toFixed(2) + '%' : '0'
        }
      }
    })
  }
  return result
}

const _report2Opt = computed(() => {
  const series = [] as any
  const legend = [] as any

  let yAxisData = pageCtl.report2Data

  // 转换数字为百分比
  if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
    yAxisData = {}
    const stackTotal = {}
    const xAxis = pageCtl.report2Data.xAxis

    for (const key in pageCtl.report2Data) {
      if (pageCtl.report2Data.hasOwnProperty(key) && key !== 'xAxis' && key !== 'lineData') {
        for (let i = 0; i < xAxis.length; i++) {
          const x = xAxis[i]
          stackTotal[x] = (stackTotal[x] || 0) + pageCtl.report2Data[key][i]
        }
      }
    }

    for (const key in pageCtl.report2Data) {
      if (pageCtl.report2Data.hasOwnProperty(key) && key !== 'xAxis' && key !== 'lineData') {
        yAxisData[key] = []
        for (let i = 0; i < xAxis.length; i++) {
          const total = stackTotal[xAxis[i]]
          const v = pageCtl.report2Data[key][i]
          if (v && total) {
            yAxisData[key].push($toFixed(v * 100 / total, 3))
          } else {
            yAxisData[key].push(null)
          }
        }
      }
    }
  }
  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis' && key !== 'lineData') {
      legend.push(key)
      series.push({
        name: key,
        type: pageCtl.conditions.report2SeriesType,
        smooth: false,
        stack: 'sum',
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }
  const lineData = yAxisData.lineData

  if (lineData) {
    for (const key in lineData) {
      legend.push(key)
      series.push({
        name: key,
        type: 'line',
        smooth: false,
        yAxisIndex: 1,
        data: lineData[key] || []
      })
    }
  }

  let subTitle = ''

  if (pageCtl.conditions.reportViewType === 'CALENDAR_MONTH') {
    subTitle = 'Month'
  } else if (pageCtl.conditions.reportViewType === 'CALENDAR_QUARTER') {
    subTitle = 'Quarter'
  } else if (pageCtl.conditions.reportViewType === 'CALENDAR_YEAR') {
    subTitle = 'Year'
  }

  return {
    title: {
      text: 'Evolution of E2E LT by ' + subTitle +
          (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:9.5rem;">')
          const marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
          tip.push('%')
        }
        tip.push('</span></div>')

        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({
      data: legend,
      selected: {
        CUSTOMER_REQUESTED_LT_1: false,
        LOGISTICS_OFFER_LT_1: false,
        FIRST_CONFIRMED_LT_1: false,
        ACTUAL_LT_1: false,
        CONTRACTUAL_LT_1: false
      }
    }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: pageCtl.conditions.report2SeriesType === 'line' ? false : [0, '10%'],
      data: pageCtl.report2Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: [{
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }, {
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      },
      splitLine: {
        show: false
      },
      splitArea: {
        show: false
      }
    }],
    toolbox: $toolbox({ opts: ['line', 'stack', 'no-details'] }),
    series
  }
})

const _report3SelectedColumns = computed(() => {
  if (pageCtl.conditions.report3SelectedColumns.length > 0) {
    return pageCtl.conditions.report3SelectedColumns
  } else {
    return ['COUNTRY_NAME']
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})

</script>

<style lang="scss">
#e2eLTAnalysis {
  .tree-table {
    table {
      tr:last-child {
        font-weight: bold;
      }

      tr {
        background-color: transparent;

        th.fail-header {
          border-right: solid 2.5px var(--scp-bg-color) !important;
        }

        th.plus-header {
          border-left: solid 2.5px var(--scp-bg-color) !important;
        }

        th {
          padding: 2px 2px !important;
          border-bottom: 0 !important;
          border-right: solid 5px var(--scp-bg-color) !important;
          background-color: var(--scp-bg-color-fill) !important;

          &:nth-last-child(1) {
            border-right: none !important;
          }

          .cell {
            font-size: 0.5rem !important;
            font-weight: normal;
            text-align: center;
            line-height: 21px !important;
            white-space: nowrap;
            padding: 0 !important;

            .el-select {
              margin-left: 10px;
              width: calc(100% - 40px) !important;

              .el-input__icon {
                width: 10px !important;
              }

              .el-select__wrapper {
                .el-select__selected-item:first-child {
                  max-width: calc(100% - 40px) !important;
                }

                .el-tag:nth-child(2) {
                  font-size: .4rem;
                  color: var(--scp-text-color-secondary);
                }

                .el-tag {
                  padding: 0 !important;
                  margin-left: 0 !important;
                  font-size: 0.45rem;
                  color: var(--scp-text-color-primary);
                  background-color: transparent !important;

                  .el-tag__close {
                    display: none !important;
                  }
                }
              }
            }
          }
        }

        td {
          padding: 0 !important;
          background-color: transparent;

          .cell {
            padding-left: 0 !important;
            padding-right: 5px !important;
            font-size: 0.5rem !important;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td:not(:first-child) {
          text-align: right;
        }

        td.el-table_1_column_3, td.el-table_1_column_4, td.el-table_1_column_5, td.el-table_1_column_6 {
          text-align: right;
        }
      }
    }

    .el-table__header {
      .el-select__wrapper {
        background-color: transparent !important;
        --el-input-border-color: transparent !important;
        box-shadow: none !important;
      }
    }
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-success) !important;
    color: #fff !important;
  }

  .el-table td, .el-table th.is-leaf {
    border-bottom: none;
  }

  .el-table tr:hover {
    td {
      * {
        color: #fff !important;
      }
    }
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
  }
}
</style>
