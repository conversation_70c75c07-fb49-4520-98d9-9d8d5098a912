import { createApp } from 'vue'
import App from './App.vue'
import ElementPlus, { ElSelect } from 'element-plus'
import en from 'element-plus/dist/locale/en.mjs' // 设置element ui的主题
import router from './router'
import axios from 'axios'
import store from './store'
import Functions from './assets/js/function'
import EChartsUtil from './assets/js/echarts-util'
import NProgress from 'nprogress'
import { library } from '@fortawesome/fontawesome-svg-core'
import { fas } from '@fortawesome/free-solid-svg-icons'
import { far } from '@fortawesome/free-regular-svg-icons'
import { fab } from '@fortawesome/free-brands-svg-icons'
import { FontAwesomeIcon, FontAwesomeLayers, FontAwesomeLayersText } from '@fortawesome/vue-fontawesome'

import contextmenu from 'v-contextmenu'
import ScpSubscript from './components/starter/components/Subscript.vue'
import ScpDraggableResizable from './components/starter/components/DraggableResizable.vue'
import ScpSearch from './components/starter/components/Search.vue'
import ScpTable from '@/components/starter/components/Table.vue'
import ScpTable2 from '@/components/starter/components/Table2.vue'
import ScpDatagrid from '@/components/starter/components/Datagrid.vue'
import ScpTreeMenu from '@/components/starter/components/TreeMenu.vue'
import ScpCascader from '@/components/starter/components/Cascader.vue'
import ScpChart from '@/components/starter/components/Chart.vue'
// @ts-ignore
import CKEditor from '@ckeditor/ckeditor5-vue'

import 'nprogress/nprogress.css'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import 'v-contextmenu/dist/themes/default.css'

import './assets/js/mdfont'
// echarts region
import { use, registerMap } from 'echarts/core'
import {
  BarChart, PieChart, LineChart, SunburstChart, CandlestickChart,
  ScatterChart, TreemapChart, SankeyChart, TreeChart, GraphChart, MapChart, BoxplotChart
} from 'echarts/charts'
import {
  TitleComponent, TooltipComponent, GridComponent, DataZoomComponent,
  LegendComponent, MarkLineComponent, ToolboxComponent,
  DataZoomSliderComponent, VisualMapComponent, SingleAxisComponent,
  GraphicComponent, ParallelComponent, GeoComponent, TimelineComponent, MarkPointComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import ECharts from 'vue-echarts'
import './assets/js/echarts-customed-theme.js'
import './assets/js/echarts-customed-theme-dark.js'
import worldJson from './assets/map/world.json'
import worldE2eJson from './assets/map/world for e2e.json'

use([BarChart, PieChart, LineChart, SunburstChart, TitleComponent, TooltipComponent, DataZoomComponent,
  GridComponent, LegendComponent, CanvasRenderer, CandlestickChart, MarkLineComponent, ToolboxComponent,
  DataZoomSliderComponent, ScatterChart, TreemapChart, SankeyChart, TreeChart, VisualMapComponent, SingleAxisComponent,
  GraphChart, GraphicComponent, ParallelComponent, GeoComponent, TimelineComponent, MarkPointComponent, MapChart, BoxplotChart])

registerMap('world', worldJson as any)
registerMap('world e2e', worldE2eJson as any)
// endregion

library.add(fas, far, fab)
const app = createApp(App)

en.el.pagination.pagesize = ' per page'
ElSelect.props.collapseTagsTooltip = {
  type: Boolean,
  default: true
}
app.use(ElementPlus, {
  locale: en,
  size: 'small'
})

app.component('echarts', ECharts)
app.component('chart', ScpChart)
app.component('font-awesome-icon', FontAwesomeIcon)
app.component('font-awesome-layers', FontAwesomeLayers)
app.component('font-awesome-layers-text', FontAwesomeLayersText)
app.component('scp-draggable-resizable', ScpDraggableResizable)
app.component('scp-subscript', ScpSubscript)
app.component('scp-search', ScpSearch)
app.component('scp-table', ScpTable)
app.component('scp-table2', ScpTable2)
app.component('scp-datagrid', ScpDatagrid)
app.component('scp-tree-menu', ScpTreeMenu)
app.component('scp-cascader', ScpCascader)

app.config.globalProperties.$axios = axios
app.provide('$axios', axios)
app.provide('$notify', app.config.globalProperties.$notify)
app.provide('$message', app.config.globalProperties.$message)

for (const key in Functions) {
  app.provide(key, Functions[key])
}

for (const key in EChartsUtil) {
  app.provide(key, EChartsUtil[key])
}

// region baseUrl
const href = window.location.href
let $baseUrl = ''

if (href.indexOf('https') === 0) {
  if (process.env.NODE_ENV === 'development') {
    $baseUrl = 'https://scphost-dev:8081'
  } else {
    $baseUrl = 'https://scp-dss.cn.schneider-electric.com:8443'
  }
} else {
  if (process.env.NODE_ENV === 'development') {
    $baseUrl = 'http://scphost-dev:8081'
  } else {
    $baseUrl = 'http://************:8081'
  }
}
app.provide('$baseUrl', $baseUrl)
// endregion

// region Nprogress
/*
 * Nprogress 控制进度条
 * 进度条不会自动跳动, 只有在ajax成功后, 往前随机跳动一定长度
 *
 * 1. 控制条仅监控ajax请求, 不对路由进行监控
 * 2. 当请求发起时, 在nprogressProcess中记录请求URL
 * 3. 当请求失败, 或者请求完成时, 将对应的URL从nprogressProcess中删除
 * 4. nprogressStart调用时, 如果nprogressProcess为空, 说明初次加载, 则启动进度条
 * 5. nprogressDone调用时, 如果nprogressProcess为空, 则延时150毫秒, 如果150毫秒内无新的ajax请求进来, 则结束进度条;
 *    这样可以避免在某些情况下, 同一页面进度条加载多次的情况, 缺点就是当页面请求加载完成时, 进度条需要在150毫秒后消失
 * 6. 当路由跳转成功时, 清空nprogressProcess对象
 * 7. 如果同时请求多个相同URL, 则可能会出现进度条统计不准确的情况, 该情况出现不多, 忽略
 */
let nprogressProcess = {}
let nprogressTimer

function nprogressStart (key) {
  if (Object.keys(nprogressProcess).length === 0) {
    NProgress.start()
  }
  nprogressProcess[key] = true
  clearTimeout(nprogressTimer)
}

function nprogressDone (key) {
  delete nprogressProcess[key]
  NProgress.inc()
  if (Object.keys(nprogressProcess).length === 0) {
    nprogressTimer = setTimeout(() => {
      NProgress.done()
    }, 150)
  }
}

function nprogressClear () {
  nprogressProcess = {}
}

// endregion

// region axios
axios.interceptors.request.use(
  config => {
    nprogressStart(config.url)
    if (config.headers['Content-Type'] !== 'multipart/form-data') {
      config.headers['Content-Type'] = 'application/json;charset=UTF-8'
    }
    config.headers.page = localStorage.getItem('page')
    if (localStorage.getItem('debug') === 'Y') {
      config.headers.debug = 'Y'
    }
    config.headers.token = localStorage.getItem('token')
    config.baseURL = $baseUrl
    return config
  },
  error => {
    if (error && error.config && error.config.url) {
      nprogressDone(error.config.url)
    } else {
      nprogressClear()
    }
    return Promise.reject(error.response)
  }
)

axios.interceptors.response.use(
  response => {
    const data = response.data
    const contentType = response.headers['content-type'] || ''
    nprogressDone(response.config.url)
    if (contentType.toLowerCase().indexOf('application/json') !== -1) {
      if (!data.header) {
        const error = new Error('no response header')
        return Promise.reject(error)
      } else if (data.header.status !== 200 && data.header.status !== 204) {
        let title = 'Internal Server Error'
        switch (data.header.status) {
          case 401:
            title = 'No privileges on this request'
            break
          case 403:
            title = 'Request is not permitted'
            break
          case 408:
            title = 'Session timeout, relogin please'
            break
          case 500:
            title = 'Internal Server Error'
            break
        }

        // 后台如果传回来的是403消息, 则说明用户的输入有误, 以message的形式提醒用户
        if (data.header.status === 403) {
          app.config.globalProperties.$message({
            message: data.header.message,
            dangerouslyUseHTMLString: true,
            type: 'error',
            duration: 60000,
            showClose: true
          })
        } else {
          app.config.globalProperties.$notify({
            title,
            message: data.header.message,
            type: 'error'
          })
        }

        if (data.header.status === 408) {
          return router.push('/login')
        }
        return Promise.reject(data.header)
      }
      return response.data.body
    } else {
      return response
    }
  },
  error => {
    if (error && error.config && error.config.url) {
      nprogressDone(error.config.url)
    } else {
      nprogressClear()
    }
    const ignore = (error.config ? error.config.ignore : []) || []
    if (ignore && ignore.indexOf(error.message) !== -1) {
      return Promise.reject(error)
    } else {
      app.config.globalProperties.$notify({
        title: 'Internal Server Error',
        message: error,
        type: 'error'
      })
      return Promise.reject(error)
    }
  }
)
// endregion

// region router
let maintains = false // 不要改这里
const maintainsURL = '/maintains'

function setMaintains (m) {
  maintains = m
}

setMaintains(false) // 改这里

// router添加过滤器, 实现登录token过滤
router.beforeEach((to, from, next) => {
  const pendingPage = localStorage.getItem('pending-page')
  // 所有路由在跳转前, 如果不是https的链接, 优先跳转到https
  if (Functions.$redirectHttp2Https()) {
    console.log('redirecting from http to https')
  } else if (maintains === false && to.path === maintainsURL) {
    // 如果当前不是维护模式, 但是用户停留在了维护页面上, 那么就需要跳转出来
    next({ path: '/' })
  } else if (maintains === true && to.path !== maintainsURL) {
    // 如果当前是维护模式, 并且用户跳转的页面不是维护页面, 则跳转到维护页面
    next({ path: maintainsURL })
  } else if (to.meta.filter === false) {
    // 如果是非鉴权(需要在路由的ts文件中配置)路径, 直接跳转
    next()
  } else if (pendingPage) {
    // pending路由的意思是, 用户希望跳转, 但是系统问题, 实际并未发生跳转, 比如系统出现更新, 资源文件丢失
    // 每次加载路由之前, 都要判断有没有pending的路由, 如果有, 优先跳转pending路由
    // pending路由跳转一次后失效
    next({ path: pendingPage })
  } else {
    // 正常流程, 检查将要跳转的路由是否需要权限验证
    axios({
      method: 'post',
      url: '/check_route_auth',
      headers: {
        route: to.path
      }
    }).then((body) => {
      // 如果返回值200, 则说明有权限, 不拦截
      if (body.status === 200) {
        // embed是一个外链地址, setCurrentPage不在此控制
        if (body.menu && to.path !== '/embed') {
          store.commit('setCurrentPage', body.menu || [])
        }
        // 记录将要跳转的地址
        localStorage.setItem('page', to.path)
        // 执行跳转
        next()
      } else if (body.status === 403) {
        // 如果返回403, 说明用户token有效, 但是没有权限访问此页面
        app.config.globalProperties.$notify({
          title: 'Unauthorized', message: 'You have no privileges on page ' + to.fullPath, type: 'error'
        })
        // 提示信息之后跳转到默认页面
        next({ path: '/' })
      } else {
        // 其他情况校验失败的, 都跳转至login页面
        next({ path: '/login', query: { redirect: to.fullPath } })
      }
    }).catch((error) => {
      console.log(error)
    })
  }
})

// planned-page是在用户点击菜单时记录, 表示用户正在前往的页面
// pending-page表示, 用户想要去, 但是因为系统问题没有去成功的页面
// 因为系统资源文件更新会导致用户点击菜单失效, 这时候就需要加个判断, 检测到失效, 将用户需要跳转的页面放入pending-page, 方便刷新后还可以跳转
router.onError((error) => {
  const pattern = /Loading chunk (\d)+ failed/g
  const isChunkLoadFailed = error.message.match(pattern)
  if (isChunkLoadFailed) {
    localStorage.setItem('pending-page', '' + localStorage.getItem('planned-page')) // 如果找不到资源, 那就将之前保存的地址存入pending-page
    window.location.reload()
  }
})

router.afterEach(() => {
  nprogressClear()
})

// endregion

app.use(router)
app.config.globalProperties.$store = store
// @ts-ignore
app.use(store)
app.use(contextmenu)
app.use(CKEditor)

// @ts-ignore
window.$vueApp = app // 用一种很蠢的方式将vue上下文暴露出来, 供function.js等方法使用
// @ts-ignore
window.$env = process.env.NODE_ENV

app.mount('#app')
